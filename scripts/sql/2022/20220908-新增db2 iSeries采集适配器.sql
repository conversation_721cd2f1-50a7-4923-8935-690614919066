INSERT INTO edgs.t99_datasource_adapter (adapter_id, adapter_name, adapter_version, description, adapter_type) VALUES ('adapter-db2-as400', 'DB2_AS400', '1.0', 'DB2 iSeries access400元数据采集', 'database');

INSERT INTO edgs.t99_datasource_adapter_mode (mode_id, mode_name, adapter_id, toolkit_version, action_mode, mode_order, auth_mode) VALUES ('adapter-db2-as400-jdbc', 'db2(jdbc)', 'adapter-db2-as400', 'iSeries/AS 400', 'jdbc', 1, 1);

INSERT INTO edgs.t99_datasource_adapter_param (mode_id, param_code, param_name, default_value, required, display_order, editor_type, description, regular) VALUES ('adapter-db2-as400-jdbc', 'database', '数据库', 'demo', 'Y', 4, null, '数据库', '');
INSERT INTO edgs.t99_datasource_adapter_param (mode_id, param_code, param_name, default_value, required, display_order, editor_type, description, regular) VALUES ('adapter-db2-as400-jdbc', 'driver.class.name', '驱动', 'com.ibm.as400.access.AS400JDBCDriver', 'Y', 1, 'readonly', '数据连接所需驱动', '');
INSERT INTO edgs.t99_datasource_adapter_param (mode_id, param_code, param_name, default_value, required, display_order, editor_type, description, regular) VALUES ('adapter-db2-as400-jdbc', 'ip', 'ip地址', '127.0.0.1', 'Y', 3, null, 'IP', '');
INSERT INTO edgs.t99_datasource_adapter_param (mode_id, param_code, param_name, default_value, required, display_order, editor_type, description, regular) VALUES ('adapter-db2-as400-jdbc', 'link_param', '连接参数', null, 'N', 8, null, '连接参数', '');
INSERT INTO edgs.t99_datasource_adapter_param (mode_id, param_code, param_name, default_value, required, display_order, editor_type, description, regular) VALUES ('adapter-db2-as400-jdbc', 'url', '数据访问URL', 'jdbc:as400://[ip];libraries=[database];', 'Y', 2, 'readonly', '数据访问URL', '');

