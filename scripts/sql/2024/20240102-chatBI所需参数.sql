#新增反馈字典类型
insert into t99_dic(dict_code, dict_code_desc, code_item, code_item_name, show_order)
values ('CORRECT_FEEDBACK', '正确反馈分类', '1', '回答准确且专业', '1');
insert into t99_dic(dict_code, dict_code_desc, code_item, code_item_name, show_order)
values ('CORRECT_FEEDBACK', '正确反馈分类', '2', '回答清晰易于理解', '2');

insert into t99_dic(dict_code, dict_code_desc, code_item, code_item_name, show_order)
values ('ERROR_FEEDBACK', '错位反馈分类', '1', '存在错误信息', '1');
insert into t99_dic(dict_code, dict_code_desc, code_item, code_item_name, show_order)
values ('ERROR_FEEDBACK', '错位反馈分类', '2', '回复内容没什么帮助', '2');



#新增系统参数-chatBI服务地址
INSERT INTO `edgs`.`t99_system_param` (`param_name`, `param_value`, `built_in`, `is_encrypted`, `description`, `item_order`, `required`) VALUES ('chatBI_server', 'http://**************:30002', 'N', 'N', 'chatBI服务端地址', 1, 'Y');
#新增系统参数-最大数据集连接数
INSERT INTO `edgs`.`t99_system_param` (`param_name`, `param_value`, `built_in`, `is_encrypted`, `description`, `item_order`, `required`) VALUES ('max_dataset_count', '10', 'N', 'N', '数据集最大连接数', 1, 'Y');
