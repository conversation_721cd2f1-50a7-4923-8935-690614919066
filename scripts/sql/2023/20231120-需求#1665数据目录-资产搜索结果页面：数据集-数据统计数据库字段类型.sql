-- mysql数据类型
INSERT INTO `t99_dic` (`dict_code`, `dict_code_desc`, `code_item`, `code_item_name`, `show_order`) VALUES ('DATATYPE_MYSQL', 'mysql数据类型分类', '字符串', 'char,varchar,text,tinytext,longtext,blob,tinyblob,longblob,enum,json', 1);
INSERT INTO `t99_dic` (`dict_code`, `dict_code_desc`, `code_item`, `code_item_name`, `show_order`) VALUES ('DATATYPE_MYSQL', 'mysql数据类型分类', '数值', 'int,tinyint,smallint,bigint,bit,float,double,deciml', 3);
INSERT INTO `t99_dic` (`dict_code`, `dict_code_desc`, `code_item`, `code_item_name`, `show_order`) VALUES ('DATATYPE_MYSQL', 'mysql数据类型分类', '时间', 'date,time,datetime,timestamp,year', 2);

-- oracle数据类型
INSERT INTO `t99_dic` (`dict_code`, `dict_code_desc`, `code_item`, `code_item_name`, `show_order`) VALUES ('DATATYPE_ORACLE', 'oracle数据类型分类', '字符串', 'char,nchar,varchar2,nvarchar2,clob,nclob', 1);
INSERT INTO `t99_dic` (`dict_code`, `dict_code_desc`, `code_item`, `code_item_name`, `show_order`) VALUES ('DATATYPE_ORACLE', 'oracle数据类型分类', '数值', 'integer,smallint,bigint,number,long,float,double,deciml', 3);
INSERT INTO `t99_dic` (`dict_code`, `dict_code_desc`, `code_item`, `code_item_name`, `show_order`) VALUES ('DATATYPE_ORACLE', 'oracle数据类型分类', '时间', 'date,timestamp', 2);

-- postgreSQL数据类型
INSERT INTO `t99_dic` (`dict_code`, `dict_code_desc`, `code_item`, `code_item_name`, `show_order`) VALUES ('DATATYPE_POSTGRESQL', 'postgreSQL数据类型分类', '字符串', 'char,varchar,text,json,jsonb,cidr,inet', 1);
INSERT INTO `t99_dic` (`dict_code`, `dict_code_desc`, `code_item`, `code_item_name`, `show_order`) VALUES ('DATATYPE_POSTGRESQL', 'postgreSQL数据类型分类', '数值', 'integer,smallint,bigint,bit,numeric,double,real', 3);
INSERT INTO `t99_dic` (`dict_code`, `dict_code_desc`, `code_item`, `code_item_name`, `show_order`) VALUES ('DATATYPE_POSTGRESQL', 'postgreSQL数据类型分类', '时间', 'date,time,timestamp,interval', 2);

-- 达梦数据类型
INSERT INTO `t99_dic` (`dict_code`, `dict_code_desc`, `code_item`, `code_item_name`, `show_order`) VALUES ('DATATYPE_DM', '达梦数据类型分类', '字符串', 'char,nchar,varchar2,nvarchar2,clob,nclob', 1);
INSERT INTO `t99_dic` (`dict_code`, `dict_code_desc`, `code_item`, `code_item_name`, `show_order`) VALUES ('DATATYPE_DM', '达梦数据类型分类', '数值', 'integer,smallint,bigint,number,long,float,double,deciml', 3);
INSERT INTO `t99_dic` (`dict_code`, `dict_code_desc`, `code_item`, `code_item_name`, `show_order`) VALUES ('DATATYPE_DM', '达梦数据类型分类', '时间', 'date,timestamp', 2);

-- kingbase数据类型
INSERT INTO `t99_dic` (`dict_code`, `dict_code_desc`, `code_item`, `code_item_name`, `show_order`) VALUES ('DATATYPE_KINGBASE', 'kingbase数据类型分类', '字符串', 'char,varchar,text,json,jsonb,cidr,inet', 1);
INSERT INTO `t99_dic` (`dict_code`, `dict_code_desc`, `code_item`, `code_item_name`, `show_order`) VALUES ('DATATYPE_KINGBASE', 'kingbaseSQL数据类型分类', '数值', 'integer,smallint,bigint,bit,numeric,double,real', 3);
INSERT INTO `t99_dic` (`dict_code`, `dict_code_desc`, `code_item`, `code_item_name`, `show_order`) VALUES ('DATATYPE_KINGBASE', 'kingbase数据类型分类', '时间', 'date,time,timestamp,interval', 2);

-- greenplum数据类型
INSERT INTO `t99_dic` (`dict_code`, `dict_code_desc`, `code_item`, `code_item_name`, `show_order`) VALUES ('DATATYPE_GREENPLUM', 'greenplum数据类型分类', '字符串', 'char,varchar,text,json,jsonb,cidr,inet', 1);
INSERT INTO `t99_dic` (`dict_code`, `dict_code_desc`, `code_item`, `code_item_name`, `show_order`) VALUES ('DATATYPE_GREENPLUM', 'greenplum数据类型分类', '数值', 'integer,smallint,bigint,bit,numeric,double,real', 3);
INSERT INTO `t99_dic` (`dict_code`, `dict_code_desc`, `code_item`, `code_item_name`, `show_order`) VALUES ('DATATYPE_GREENPLUM', 'greenplum数据类型分类', '时间', 'date,time,timestamp,interval', 2);

-- SQLServer数据类型
INSERT INTO `t99_dic` (`dict_code`, `dict_code_desc`, `code_item`, `code_item_name`, `show_order`) VALUES ('DATATYPE_SQLSERVER', 'SQLServer数据类型分类', '字符串', 'char,varchar,text,nchar,nvarchar,ntext', 1);
INSERT INTO `t99_dic` (`dict_code`, `dict_code_desc`, `code_item`, `code_item_name`, `show_order`) VALUES ('DATATYPE_SQLSERVER', 'SQLserver数据类型分类', '数值', 'int,tinyint,smallint,bigint,bit,float,decimal,real,numeric', 3);
INSERT INTO `t99_dic` (`dict_code`, `dict_code_desc`, `code_item`, `code_item_name`, `show_order`) VALUES ('DATATYPE_SQLSERVER', 'SQLserver数据类型分类', '时间', 'time,date,datetime', 2);

-- db2数据类型
INSERT INTO `t99_dic` (`dict_code`, `dict_code_desc`, `code_item`, `code_item_name`, `show_order`) VALUES ('DATATYPE_DB2', 'db2数据类型分类', '字符串', 'char,varchar,clob,nchar,nvarchar,nclob', 1);
INSERT INTO `t99_dic` (`dict_code`, `dict_code_desc`, `code_item`, `code_item_name`, `show_order`) VALUES ('DATATYPE_DB2', 'db2数据类型分类', '数值', 'integer,smallint,bigint,decimal,numeric,float,double', 3);
INSERT INTO `t99_dic` (`dict_code`, `dict_code_desc`, `code_item`, `code_item_name`, `show_order`) VALUES ('DATATYPE_DB2', 'db2数据类型分类', '时间', 'date,time,timestamp,interval', 2);

