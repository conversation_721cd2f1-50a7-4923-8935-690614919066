INSERT INTO edgs.t99_datasource_adapter (adapter_id, adapter_name, adapter_version, description, adapter_type)
VALUES ('adapter-oceanbase', 'OceanBase', '1.0', 'OceanBase', 'database');

INSERT INTO edgs.t99_datasource_adapter_mode (mode_id, mode_name, adapter_id, toolkit_version, action_mode, mode_order,
                                              auth_mode)
VALUES ('adapter-oceanbase-jdbc', 'OceanBase(jdbc)', 'adapter-oceanbase', 'common', 'jdbc', 2, 1);

INSERT INTO edgs.t99_datasource_adapter_param (mode_id, param_code, param_name, default_value, required, display_order,
                                               editor_type, description, regular)
VALUES ('adapter-oceanbase-jdbc', 'cluster_name', '集群名', 'obcluster', 'N', 5, null, '集群名', null);
INSERT INTO edgs.t99_datasource_adapter_param (mode_id, param_code, param_name, default_value, required, display_order,
                                               editor_type, description, regular)
VALUES ('adapter-oceanbase-jdbc', 'database', '数据库', null, 'N', 7, null, '数据库', '');
INSERT INTO edgs.t99_datasource_adapter_param (mode_id, param_code, param_name, default_value, required, display_order,
                                               editor_type, description, regular)
VALUES ('adapter-oceanbase-jdbc', 'driver.class.name', '驱动', 'com.oceanbase.jdbc.Driver', 'Y', 2, 'readonly',
        '数据连接所需驱动', '');
INSERT INTO edgs.t99_datasource_adapter_param (mode_id, param_code, param_name, default_value, required, display_order,
                                               editor_type, description, regular)
VALUES ('adapter-oceanbase-jdbc', 'ip', 'ip地址', null, 'Y', 3, null, 'IP', '');
INSERT INTO edgs.t99_datasource_adapter_param (mode_id, param_code, param_name, default_value, required, display_order,
                                               editor_type, description, regular)
VALUES ('adapter-oceanbase-jdbc', 'link_param', '连接参数', null, 'N', 8, null, '连接参数', '');
INSERT INTO edgs.t99_datasource_adapter_param (mode_id, param_code, param_name, default_value, required, display_order,
                                               editor_type, description, regular)
VALUES ('adapter-oceanbase-jdbc', 'port', '端口', null, 'Y', 4, null, 'port', '');
INSERT INTO edgs.t99_datasource_adapter_param (mode_id, param_code, param_name, default_value, required, display_order,
                                               editor_type, description, regular)
VALUES ('adapter-oceanbase-jdbc', 'tenant_name', '租户名', null, 'Y', 6, null, '租户名', null);
INSERT INTO edgs.t99_datasource_adapter_param (mode_id, param_code, param_name, default_value, required, display_order,
                                               editor_type, description, regular)
VALUES ('adapter-oceanbase-jdbc', 'url', '数据访问URL', 'jdbc:oceanbase://[ip]:[port]/[database]', 'Y', 9, 'readonly',
        '数据访问URL', '');


