# mysql
alter table t01_extract_job
    add enable_parse_scan char default 'N' not null comment '是否开启采集完自动解析:‘Y’：是，‘N’：否''';

CREATE TABLE `t01_dataflow_automatic_parse` (
                                                `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                `datasource_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '数据源id',
                                                `schema_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'schema的名称,可以是perl、fineReport等',
                                                `classifier_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '元数据类型',
                                                `create_time` datetime DEFAULT NULL COMMENT '生成时间',
                                                `parse_status` char(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '0: 未解析，1：正在解析,2:解析成功，3:解析失败,4: 血缘触发成功',
                                                `failure_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '解析失败的错误信息',
                                                `session_id` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '写入到记录表的批次Id',
                                                `extract_task_id` int DEFAULT NULL COMMENT '采集任务的taskID，记录之后方便排错',
                                                PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='自动解析记录表'

;

# oracle dm
ALTER TABLE t01_extract_job
    ADD enable_parse_scan CHAR(1) DEFAULT 'N' NOT NULL;
COMMENT ON COLUMN T01_EXTRACT_JOB.enable_parse_scan IS '是否开启采集完自动解析:‘Y’：是，‘N’：否';

create
    sequence SEQ_T01_DATAFLOW_A_P_ID start
    with 1 increment by 1 nomaxvalue nocache;

CREATE TABLE t01_dataflow_automatic_parse
(
    id                NUMBER not null
        primary key,
    datasource_id     VARCHAR2(32),
    schema_name       VARCHAR2(100),
    classifier_id     VARCHAR2(50),
    create_time       TIMESTAMP,
    parse_status      CHAR(1),
    failure_message   CLOB,
    session_id        VARCHAR2(50),
    extract_task_id   NUMBER
);

COMMENT ON TABLE t01_dataflow_automatic_parse IS '自动解析记录表';

COMMENT ON COLUMN t01_dataflow_automatic_parse.id IS '主键';
COMMENT ON COLUMN t01_dataflow_automatic_parse.datasource_id IS '数据源id';
COMMENT ON COLUMN t01_dataflow_automatic_parse.schema_name IS 'schema的名称,可以是perl、fineReport等';
COMMENT ON COLUMN t01_dataflow_automatic_parse.classifier_id IS '元数据类型';
COMMENT ON COLUMN t01_dataflow_automatic_parse.create_time IS '生成时间';
COMMENT ON COLUMN t01_dataflow_automatic_parse.parse_status IS '0: 未解析，1：正在解析,2:解析成功，3:解析失败,4: 血缘触发成功';
COMMENT ON COLUMN t01_dataflow_automatic_parse.failure_message IS '解析失败的错误信息';
COMMENT ON COLUMN t01_dataflow_automatic_parse.session_id IS '写入到记录表的批次Id';
COMMENT ON COLUMN t01_dataflow_automatic_parse.extract_task_id IS '采集任务的taskID，记录之后方便排错';

# kingbase
ALTER TABLE t01_extract_job
    ADD enable_parse_scan CHAR(1) DEFAULT 'N' NOT NULL COMMENT '是否开启采集完自动解析:‘Y’：是，‘N’：否';

CREATE TABLE t01_dataflow_automatic_parse
(
    id                SERIAL PRIMARY KEY,
    datasource_id     VARCHAR(32),
    schema_name       VARCHAR(100),
    classifier_id     VARCHAR(50),
    create_time       TIMESTAMP,
    parse_status      CHAR(1) COLLATE "C",
    failure_message   TEXT,
    session_id        VARCHAR(50) COLLATE "C",
    extract_task_id   INT
);

COMMENT ON TABLE t01_dataflow_automatic_parse IS '自动解析记录表';

COMMENT ON COLUMN t01_dataflow_automatic_parse.id IS '主键';
COMMENT ON COLUMN t01_dataflow_automatic_parse.datasource_id IS '数据源id';
COMMENT ON COLUMN t01_dataflow_automatic_parse.schema_name IS 'schema的名称,可以是perl、fineReport等';
COMMENT ON COLUMN t01_dataflow_automatic_parse.classifier_id IS '元数据类型';
COMMENT ON COLUMN t01_dataflow_automatic_parse.create_time IS '生成时间';
COMMENT ON COLUMN t01_dataflow_automatic_parse.parse_status IS '0: 未解析，1：正在解析,2:解析成功，3:解析失败,4: 血缘触发成功';
COMMENT ON COLUMN t01_dataflow_automatic_parse.failure_message IS '解析失败的错误信息';
COMMENT ON COLUMN t01_dataflow_automatic_parse.session_id IS '写入到记录表的批次Id';
COMMENT ON COLUMN t01_dataflow_automatic_parse.extract_task_id IS '采集任务的taskID，记录之后方便排错';


