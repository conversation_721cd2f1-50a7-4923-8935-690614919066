INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Attribute', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Attribute', 'Feature', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Attribute', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('CandidateKey', 'KeyGroup', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('CheckConstraintUsage', 'CheckConstraint', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Class', 'Classifier', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Class', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Class', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Classifier', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Classifier', 'ModelElement', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ClassifierMap', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ClassifierMap', 'ModelElement', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Cognos8_Calculation', 'Cognos8_DataObject', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Cognos8_DataItem', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Cognos8_DataItem', 'Feature', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Cognos8_DataItem', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Cognos8_Filter', 'Class', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Cognos8_Filter', 'Classifier', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Cognos8_Filter', 'Cognos8_DataObject', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Cognos8_Filter', 'ColumnSet', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Cognos8_Filter', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Cognos8_Filter', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Cognos8_QueryItem', 'Cognos8_DataItem', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Cognos8_QueryItem', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Cognos8_QueryItem', 'Feature', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Cognos8_QueryItem', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Cognos8_QuerySubject', 'Cognos8_DataObject', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Cognos8_ShortCut', 'Cognos8_DataObject', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Cognos8_SubReport', 'Class', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Cognos8_SubReport', 'Classifier', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Cognos8_SubReport', 'ColumnSet', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Cognos8_SubReport', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Cognos8_SubReport', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Cognos8_SubReport', 'NamedColumnSet', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Column', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Column', 'Feature', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Column', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ColumnSet', 'Class', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ColumnSet', 'Classifier', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ColumnSet', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ColumnSet', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Domain', 'Column', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Domain', 'ERColumn', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Domain', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Domain', 'Feature', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Domain', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ERColumn', 'Column', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ERColumn', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ERColumn', 'Feature', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ERColumn', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ERForeignKey', 'ForeignKey', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ERForeignKey', 'KeyGroup', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ERPrimaryKey', 'KeyGroup', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ERPrimaryKey', 'PrimaryKey', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ERTable', 'Class', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ERTable', 'Classifier', 'N', 2);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ERTable', 'ColumnSet', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ERTable', 'Element', 'N', 2);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ERTable', 'ModelElement', 'N', 2);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ERTable', 'NamedColumnSet', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ERTable', 'Table', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ERTablespace', 'Tablespace', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ERTrigger', 'Trigger', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ERView', 'Class', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ERView', 'Classifier', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ERView', 'ColumnSet', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ERView', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ERView', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ERView', 'NamedColumnSet', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ERView', 'View', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ETLActivity', 'ModelElement', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ETLConstant', 'ModelElement', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ETLScript', 'ETLTask', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ETLScript', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ETLTask', 'ModelElement', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ETLTransformer', 'ModelElement', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Entity', 'Class', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Entity', 'Classifier', 'N', 2);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Entity', 'ColumnSet', 'N', 2);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Entity', 'Element', 'N', 2);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Entity', 'ModelElement', 'N', 2);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Entity', 'NamedColumnSet', 'N', 2);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Entity', 'Table', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Entity', 'View', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('EtlAnnotate', 'ClassifierMap', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('EtlAnnotate', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('EtlAnnotate', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Feature', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Feature', 'ModelElement', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ModelElement', 'Element', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('NamedColumnSet', 'Class', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('NamedColumnSet', 'Classifier', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('NamedColumnSet', 'ColumnSet', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('NamedColumnSet', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('NamedColumnSet', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('NonPersisAttribute', 'Attribute', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('NonPersisAttribute', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('NonPersisAttribute', 'Feature', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('NonPersisAttribute', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('NonPersisCls', 'Classifier', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('NonPersisCls', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('NonPersisCls', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('NonUniqueKey', 'KeyGroup', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDColumn', 'Column', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDColumn', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDColumn', 'Feature', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDColumn', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDIndex', 'SQLIndex', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDIndexColumn', 'Column', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDIndexColumn', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDIndexColumn', 'Feature', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDIndexColumn', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDKey', 'PrimaryKey', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDProcedure', 'Procedure', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDTable', 'Class', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDTable', 'Classifier', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDTable', 'ColumnSet', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDTable', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDTable', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDTable', 'NamedColumnSet', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDTable', 'Table', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDTablespace', 'Tablespace', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDTrigger', 'Trigger', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDView', 'Class', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDView', 'Classifier', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDView', 'ColumnSet', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDView', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDView', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDView', 'NamedColumnSet', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDView', 'View', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDViewColumn', 'Column', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDViewColumn', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDViewColumn', 'Feature', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PDViewColumn', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('ParseSchema', 'Schema', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86ColumnSet', 'Class', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86ColumnSet', 'Classifier', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86ColumnSet', 'ColumnSet', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86ColumnSet', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86ColumnSet', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86DbColumn', 'Column', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86DbColumn', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86DbColumn', 'Feature', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86DbColumn', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86FlatFile', 'Classifier', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86FlatFile', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86FlatFile', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86FlatFileColumn', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86FlatFileColumn', 'Feature', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86FlatFileColumn', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86Repository', 'SystemMap', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86SourceInstance', 'Pc86Instance', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86TargetInstance', 'Pc86Instance', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86TransformationInstance', 'Classifier', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86TransformationInstance', 'ClassifierMap', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86TransformationInstance', 'Element', 'N', 2);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86TransformationInstance', 'ModelElement', 'N', 2);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86TransformationInstance', 'Pc86Instance', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86TransformfieldInstance', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86TransformfieldInstance', 'Feature', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86TransformfieldInstance', 'FeatureMap', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Pc86TransformfieldInstance', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PersisAttribute', 'Attribute', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PersisAttribute', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PersisAttribute', 'Feature', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PersisAttribute', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PersisCls', 'Classifier', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PersisCls', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('PersisCls', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Table', 'Class', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Table', 'Classifier', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Table', 'ColumnSet', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Table', 'Element', 'N', 2);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Table', 'LifeCycle', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Table', 'ModelElement', 'N', 2);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('TeradataMacro', 'TdMacro', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('View', 'Class', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('View', 'Classifier', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('View', 'ColumnSet', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('View', 'Element', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('View', 'ModelElement', 'N', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('View', 'NamedColumnSet', 'Y', 1);
INSERT INTO edgs.t00_inherit (classifier_id, owner_classifier_id, is_parent, path_num) VALUES ('Webapi', 'ReqParam', 'Y', 1);
