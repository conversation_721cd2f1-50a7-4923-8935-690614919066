INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Cognos8_DataObject_DataItem', 'Cognos8_DataObject_DataItem', 'Cognos8_DataObject', '1', 'Cognos8_DataItem', '*', 'Cognos8_DataObject_DataItem');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Cognos8_Folder_DataObject', 'Cognos8_Folder_DataObject', 'Cognos8_Folder', '1', 'Cognos8_DataObject', '*', 'Cognos8_Folder_DataObject');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Cognos8_Folder_Folder', 'Cognos8_Folder_Folder', 'Cognos8_Folder', '1', 'Cognos8_Folder', '*', 'Cognos8_Folder_Folder');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Cognos8_Package_Folder', 'Cognos8_Package_Folder', 'Cognos8_Package', '1', 'Cognos8_Folder', '*', 'Cognos8_Package_Folder');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Cognos8_Package_Model', 'Cognos8_Package_Model', 'Cognos8_Package', '1', 'Cognos8_Model', '*', 'Cognos8_Package_Model');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Cognos8_Project_Package', 'Cognos8_Project_Package', 'Cognos8_Project', '1', 'Cognos8_Package', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-AssetCatalog-AssetCatalog', '资产目录组合资产目录', 'AssetCatalog', '1', 'AssetCatalog', '*', '资产目录组合资产目录');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-BOFolder-BOReport', 'BO文件夹组合BO报表', 'BOFolder', '1', 'BOReport', '*', 'BO文件夹组合BO报表');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-CandidateKey-KeyGroupMember', '键组包含键成员', 'CandidateKey', '1', 'KeyGroupMember', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-CdbKpi-CdbReport', '指标文件组合报表', 'CdbKpi', '1', 'CdbReport', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-CdbKpi-CdbSchema', '指标文件组合Schema', 'CdbKpi', '1', 'CdbSchema', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-CdbKpi-CdbSourceTable', '指标文件组合源表', 'CdbKpi', '1', 'CdbSourceTable', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-CdbReport-CdbDim', '报表组合维度', 'CdbReport', '1', 'CdbDim', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-CdbReport-CdbIndex', '报表组合指标', 'CdbReport', '1', 'CdbIndex', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-CdbSchema-CdbSourceTable', 'Schema组合源表', 'CdbSchema', '1', 'CdbSourceTable', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-CdbSourceTable-CdbSourceCol', '源表组合源字段', 'CdbSourceTable', '1', 'CdbSourceCol', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Classifier-Feature', 'Classifier组合Feature', 'Classifier', '1', 'Feature', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ClassifierMap-FeatureMap', 'ClassifierMap-FeatureMap', 'ClassifierMap', '1', 'FeatureMap', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_Calculation-Cognos8_DataItem', 'comp-Cognos8_Calculation-Cognos8_DataItem', 'Cognos8_Calculation', '1', 'Cognos8_DataItem', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_Catalog-Cognos8_Schema', 'Comp-Cognos8_Catalog-Cognos8_Schema', 'Cognos8_Catalog', '1', 'Cognos8_Schema', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_ColumnSet-Cognos8_Column', 'Comp-Cognos8_ColumnSet-Cognos8_Column', 'Cognos8_ColumnSet', '1', 'Cognos8_Column', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_DataSource-Cognos8_Catalog', 'Comp-Cognos8_DataSource-Cognos8_Catalog', 'Cognos8_DataSource', '1', 'Cognos8_Catalog', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_Filter-Cognos8_DataItem', 'comp-Cognos8_Filter-Cognos8_DataItem', 'Cognos8_Filter', '1', 'Cognos8_DataItem', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_Folder-Cognos8_Calculation', 'Comp-Cognos8_Folder-Cognos8_Calculation', 'Cognos8_Folder', '1', 'Cognos8_Calculation', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_Folder-Cognos8_Filter', 'Comp-Cognos8_Folder-Cognos8_Filter', 'Cognos8_Folder', '1', 'Cognos8_Filter', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_Folder-Cognos8_Namespace', 'comp-Cognos8_Folder-Cognos8_Namespace', 'Cognos8_Folder', '1', 'Cognos8_Namespace', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_Folder-Cognos8_Package', 'Comp-Cognos8_Folder-Cognos8_Package', 'Cognos8_Folder', '1', 'Cognos8_Package', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_Folder-Cognos8_QuerySubject', 'Cognos8_Folder组合QuerySubject', 'Cognos8_Folder', '1', 'Cognos8_QuerySubject', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_Folder-Cognos8_Report', 'comp-Cognos8_Folder-Cognos8_Report', 'Cognos8_Folder', '1', 'Cognos8_Report', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_Folder-Cognos8_ShortCut', 'Comp-Cognos8_Folder-Cognos8_ShortCut', 'Cognos8_Folder', '1', 'Cognos8_ShortCut', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_Model-Cognos8_Namespace', 'comp-Cognos8_Model-Cognos8_Namespace', 'Cognos8_Model', '1', 'Cognos8_Namespace', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_Namespace-Cognos8_Calculation', 'comp-Cognos8_Namespace-Cognos8_Calculation', 'Cognos8_Namespace', '1', 'Cognos8_Calculation', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_Namespace-Cognos8_DataObject', 'comp-Cognos8_Namespace-Cognos8_DataObject', 'Cognos8_Namespace', '1', 'Cognos8_DataObject', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_Namespace-Cognos8_Filter', 'comp-Cognos8_Namespace-Cognos8_Filter', 'Cognos8_Namespace', '1', 'Cognos8_Filter', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_Namespace-Cognos8_Folder', 'comp-Cognos8_Namespace-Cognos8_Folder', 'Cognos8_Namespace', '1', 'Cognos8_Folder', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_Namespace-Cognos8_NameSpace', 'comp-Cognos8_Namespace-Cognos8_Namespace', 'Cognos8_Namespace', '1', 'Cognos8_Namespace', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_Namespace-Cognos8_QuerySubject', 'comp-Cognos8_Namespace-Cognos8_QuerySubject', 'Cognos8_Namespace', '1', 'Cognos8_QuerySubject', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_Namespace-Cognos8_ShortCut', 'comp-Cognos8_Namespace-Cognos8_ShortCut', 'Cognos8_Namespace', '1', 'Cognos8_ShortCut', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_Package-Cognos8_Report', 'Comp-Cognos8_Package-Cognos8_Report', 'Cognos8_Package', '1', 'Cognos8_Report', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_Project-Cognos8_DataSource', 'Comp-Cognos8_Project-Cognos8_DataSource', 'Cognos8_Project', '1', 'Cognos8_DataSource', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_Project-Cognos8_Folder', 'Comp-Cognos8_Project-Cognos8_Folder', 'Cognos8_Project', '1', 'Cognos8_Folder', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_QuerySubject-Cognos8_QueryItem', 'Comp-Cognos8_QuerySubject-QueryItem', 'Cognos8_QuerySubject', '1', 'Cognos8_QueryItem', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_Report-Cognos8_SubReport', 'Comp-Cognos8_Report-Cognos_SubReport', 'Cognos8_Report', '1', 'Cognos8_SubReport', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_Schema-Cognos8_ColumnSet', 'Comp-Cognos8_Schema-Cognos8_ColumnSet', 'Cognos8_Schema', '1', 'Cognos8_ColumnSet', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_Schema-Cognos8_StoredProcedure', 'comp-Cognos8_Schema- Cognos8_StoredProcedure ', 'Cognos8_Schema', '1', 'Cognos8_StoredProcedure', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_StoredProcedure-Cognos8_Column', 'comp-Cognos8_StoredProcedure-Cognos8_Column ', 'Cognos8_StoredProcedure', '1', 'Cognos8_Column', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_StoredProcedure-Cognos8_ColumnSet', 'comp-Cognos8_StoredProcedure-Cognos8_ColumnSet', 'Cognos8_StoredProcedure', '1', 'Cognos8_ColumnSet', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_StoredProcedure-Cognos8_Parameter', 'comp-Cognos8_StoredProcedure-Cognos8_Parameter', 'Cognos8_StoredProcedure', '1', 'Cognos8_Parameter', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Cognos8_SubReport-Cognos8_ReportField', 'comp-Cognos8_SubReport-Cognos8_ReportField', 'Cognos8_SubReport', '1', 'Cognos8_ReportField', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ColumnSet-Column', 'Comp-ColumnSet-Column', 'ColumnSet', '1', 'Column', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-DataX-DataXConnect', 'DataX下的连接信息', 'DataX', '1', 'DataXConnect', '*', '');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-DataX-DataXFolder', 'DataX下的文件夹', 'DataX', '1', 'DataXFolder', '*', '');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-DataX-DataxJob', 'Datax下的任务', 'DataX', '1', 'DataxJob', '*', '');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-DataXFile-DataXColumn', '文件下的字段', 'DataXFile', '1', 'DataXColumn', '*', '');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-DataXFolder-DataXFolder', 'DataXFolder下的DataXFolder', 'DataXFolder', '1', 'DataXFolder', '*', '');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-DataXFolder-DataxJob', 'DataX文件夹下DataxJob', 'DataXFolder', '1', 'DataxJob', '*', '');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Database-Schema', 'Database组合Schema', 'Database', '1', 'Schema', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-DataxJob-DataXFile', '任务下的File', 'DataxJob', '1', 'DataXFile', '*', '抽象的datax任务，针对多个目标表');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Dimension-DimensionItem', '维度标准组合维度值', 'Dimension', '1', 'DimensionItem', '*', '维度标准组合维度值');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Directory-BaseAsset', '目录组合基础资产', 'Directory', '1', 'BaseAsset', '*', '目录组合基础资产');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Directory-BaseStandard', '目录组合基础标准', 'Directory', '1', 'BaseStandard', '*', '目录组合基础标准');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Directory-BusinessTerm', '目录组合业务术语', 'Directory', '1', 'BusinessTerm', '*', '目录组合业务术语');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Directory-ComputeRule', '目录组合统计规则', 'Directory', '1', 'ComputeRule', '*', '目录组合统计规则');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Directory-Dimension', '目录组合维度标准', 'Directory', '1', 'Dimension', '*', '目录组合维度标准');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Directory-Directory', '目录组合目录', 'Directory', '1', 'Directory', '*', '目录组合目录');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Directory-ExtAsset', '目录组合外部资产', 'Directory', '1', 'ExtAsset', '*', '目录组合外部资产');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Directory-ExtResource', '目录组合外部资源', 'Directory', '1', 'ExtResource', '*', '目录组合外部资源');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Directory-IndexAsset', '目录组合指标资产', 'Directory', '1', 'IndexAsset', '*', '目录组合指标资产');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Directory-IndexStandard', '目录组合指标标准', 'Directory', '1', 'IndexStandard', '*', '目录组合指标标准');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Directory-InternalAsset', '目录组合内部资产', 'Directory', '1', 'InternalAsset', '*', '目录组合内部资产');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Directory-PublicCode', '目录组合代码标准', 'Directory', '1', 'PublicCode', '*', '目录组合代码标准');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Directory-Qualifier', 'Comp-Directory-qualifier', 'Directory', '1', 'Qualifier', '1', '目录组合修饰词');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ERForeignKey-KeyGroupMember', '键组包含键成员', 'ERForeignKey', '1', 'KeyGroupMember', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ERModel-CheckConstraintUsage', 'Erwin包含的检查约束', 'ERModel', '1', 'CheckConstraintUsage', '*', 'since 7.1');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ERModel-ERSequence', '序列', 'ERModel', '1', 'ERSequence', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ERModel-ERTable', 'modelToERTable', 'ERModel', '1', 'ERTable', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ERModel-ERTablespace', 'Comp-ERModel-ERTablespace', 'ERModel', '1', 'ERTablespace', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ERModel-ERTrigger', '包含的触发器', 'ERModel', '1', 'ERTrigger', '*', 'ER模型包含Trigger');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ERModel-ERView', 'modelToERView', 'ERModel', '1', 'ERView', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ERModel-TeradataMacro', '模型包含的宏', 'ERModel', '1', 'TeradataMacro', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ERPrimaryKey-KeyGroupMember', '键组包含键成员', 'ERPrimaryKey', '1', 'KeyGroupMember', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ERTable-CandidateKey', 'ertableToCandidateKey', 'ERTable', '1', 'CandidateKey', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ERTable-CheckConstraint', 'ertableToCheckConstraint', 'ERTable', '1', 'CheckConstraint', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ERTable-ERColumn', 'ertableToColumn', 'ERTable', '1', 'ERColumn', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ERTable-ERForeignKey', '表的外键', 'ERTable', '1', 'ERForeignKey', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ERTable-ERPrimaryKey', '表的主键', 'ERTable', '1', 'ERPrimaryKey', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ERTable-NonUniqueKey', 'ertableToNonUniqueKey', 'ERTable', '1', 'NonUniqueKey', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ERTable-UniqueKey', 'ertableToUniqueKey', 'ERTable', '1', 'UniqueKey', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ERView-CandidateKey', 'erviewToCandidateKey', 'ERView', '1', 'CandidateKey', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ERView-CheckConstraint', 'erviewToCheckConstraint', 'ERView', '1', 'CheckConstraint', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ERView-ERColumn', 'erviewToColumn', 'ERView', '1', 'ERColumn', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ERView-ERForeignKey', '表的外键', 'ERView', '1', 'ERForeignKey', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ERView-ERPrimaryKey', '表的主键', 'ERView', '1', 'ERPrimaryKey', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ERView-NonUniqueKey', 'erviewToNonUniqueKey', 'ERView', '1', 'NonUniqueKey', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ERView-UniqueKey', 'erviewToUniqueKey', 'ERView', '1', 'UniqueKey', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ETLActivity-ETLConstant', 'comp-ETLActivity-EtlConstant', 'ETLActivity', '1', 'ETLConstant', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ETLActivity-ETLScript', 'ETL工程组合ETL脚本', 'ETLActivity', '1', 'ETLScript', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ETLActivity-ETLSubSystem', 'Comp-ETLActivity-ETLTask', 'ETLActivity', '1', 'ETLSubSystem', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ETLActivity-PERLScript', 'Comp-ETLActivity-PERLScript', 'ETLActivity', '1', 'PERLScript', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ETLActivity-ParseCatalog', 'comp-ETLActivity-ParseCatalog', 'ETLActivity', '1', 'ParseCatalog', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ETLFile-ETLScript', 'Comp-ETLFile-ETLScript', 'ETLFile', '1', 'ETLScript', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ETLScript-ETLTransformer', 'Comp-ETLScript-ETLTransformer', 'ETLScript', '1', 'ETLTransformer', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ETLSubSystem-ETLTask', 'Comp-ETLSubSystem-ETLTask', 'ETLSubSystem', '1', 'ETLTask', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ETLTransformer-ClassifierMap', 'ETLTransformer组合ClassifierMap', 'ETLTransformer', '1', 'ClassifierMap', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Entity-ERColumn', 'entityToColumn', 'Entity', '1', 'ERColumn', '*', 'ER实体包含字段');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Entity-ERForeignKey', '表的外键', 'Entity', '1', 'ERForeignKey', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Entity-ERPrimaryKey', '表的主键', 'Entity', '1', 'ERPrimaryKey', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-EsCluster-EsIndex', '相关的索引', 'EsCluster', '1', 'EsIndex', '*', '相关的索引');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-EsCluster-EsNode', '相关的索引', 'EsCluster', '1', 'EsNode', '*', '相关的索引');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-EsDocType-EsField', '组合的字段', 'EsDocType', '1', 'EsField', '*', '');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-EsField-EsField', '组合字段', 'EsField', '1', 'EsField', '*', '组合字段');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-EsIndex-EsDocType', '组合的文档类型', 'EsIndex', '1', 'EsDocType', '*', '');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-EtlAnnotateFile-EtlAnnotate', '注释文件组合注释信息', 'EtlAnnotateFile', '1', 'EtlAnnotate', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-EtlAnnotateFile-Schema', '注释文件组合Schmea', 'EtlAnnotateFile', '1', 'Schema', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-FRDataSet-FRInput', '帆软数据集组合帆软数据集输入参数', 'FRDataSet', '1', 'FRInput', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-FRDataSet-FROutput', '帆软数据集组合帆软数据集输出参数', 'FRDataSet', '1', 'FROutput', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-FRFolder-FRFolder', '帆软文件夹组合帆软文件夹', 'FRFolder', '1', 'FRFolder', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-FRFolder-FRReport', '帆软文件夹组合帆软报表', 'FRFolder', '1', 'FRReport', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-FRReport-FRDataSet', '帆软报表组合帆软数据集', 'FRReport', '1', 'FRDataSet', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-FineReport-FRDatasource', '帆软根节点组合帆软数据源', 'FineReport', '1', 'FRDatasource', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-FineReport-FRFolder', '帆软根节点组合帆软文件夹', 'FineReport', '1', 'FRFolder', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-FineReport-FRReport', '帆软根节点组合帆软报表', 'FineReport', '1', 'FRReport', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Folder-Folder', '文件夹组合文件夹', 'Folder', '1', 'Folder', '*', '文件夹组合文件夹');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Folder-Report', '文件夹组合报告', 'Folder', '1', 'Report', '*', '文件夹组合报告');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Kettle-KettleConnection', 'Kettle组合连接', 'Kettle', '1', 'KettleConnection', '*', 'Kettle组合连接');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Kettle-KettleDirectory', 'Kettle组合目录', 'Kettle', '1', 'KettleDirectory', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Kettle-KettleVariable', 'Kettle组合变量', 'Kettle', '1', 'KettleVariable', '*', 'Kettle组合变量');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-KettleDirectory-KettleDirectory', '组合目录', 'KettleDirectory', '1', 'KettleDirectory', '*', '组合目录');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-KettleDirectory-KettleJob', '组合作业', 'KettleDirectory', '1', 'KettleJob', '*', '组合作业');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-KettleDirectory-KettleTransformation', '组合转换', 'KettleDirectory', '1', 'KettleTransformation', '*', '组合转换');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-KettleFile-KettleColumn', 'Kettle文件组合Kettle字段', 'KettleFile', '1', 'KettleColumn', '*', 'Kettle文件组合Kettle字段');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-KettleTransformation-KettleFile', '转换组合文件', 'KettleTransformation', '1', 'KettleFile', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-KeyGroup-KeyGroupMember', '键集合内的键成员', 'KeyGroup', '1', 'KeyGroupMember', '*', '键集合内的键成员');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-NonPersisCls-NonPersisAttribute', '非持久化类组合非持久化属性', 'NonPersisCls', '1', 'NonPersisAttribute', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-NonUniqueKey-KeyGroupMember', '键组包含键成员', 'NonUniqueKey', '1', 'KeyGroupMember', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-OraPackage-Function', '包组合函数', 'OraPackage', '1', 'Function', '*', '包组合函数');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-OraPackage-Procedure', '包组合存储过程', 'OraPackage', '1', 'Procedure', '*', '包组合存储过程');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-PDIndex-PDIndexColumn', 'Comp-PDIndex-PDIndexColumn', 'PDIndex', '1', 'PDIndexColumn', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-PDTable-PDColumn', 'Comp-PowerDesigner-PDColumn', 'PDTable', '1', 'PDColumn', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-PDTable-PDIndex', 'Comp-PowerDesigner-PDIndex', 'PDTable', '1', 'PDIndex', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-PDTable-PDKey', 'Comp-PowerDesigner-PDKey', 'PDTable', '1', 'PDKey', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-PDTable-PDTrigger', 'Comp-PowerDesigner-PDTrigger', 'PDTable', '1', 'PDTrigger', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-PDView-PDViewColumn', 'Comp-PDView-PDViewColumn', 'PDView', '1', 'PDViewColumn', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ParseCatalog-ParseSchema', 'comp-ParseCatalog-ParseSchema', 'ParseCatalog', '1', 'ParseSchema', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ParseSchema-NonPersisCls', 'Schema组合非持久化类', 'ParseSchema', '1', 'NonPersisCls', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ParseSchema-PersisCls', 'Schema组合持久化类', 'ParseSchema', '1', 'PersisCls', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86Catalog-Pc86Schema', 'comp-Pc86Catalog-Pc86Schema', 'Pc86Catalog', '1', 'Pc86Schema', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86ColumnSet-Pc86DbColumn', 'comp-Pc86ColumnSet-Pc86DbColumn', 'Pc86ColumnSet', '1', 'Pc86DbColumn', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86FlatFile-Pc86FlatFileColumn', 'comp-Pc86FlatFile-Pc86FlatFileColumn', 'Pc86FlatFile', '1', 'Pc86FlatFileColumn', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86Folder-Pc86Mapping', 'Comp-Pc86Folder-Pc86Mapping', 'Pc86Folder', '1', 'Pc86Mapping', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86Folder-Pc86Mapplet', 'Comp-Pc86Folder- Pc86Mapplet', 'Pc86Folder', '1', 'Pc86Mapplet', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86Folder-Pc86Source', 'Comp-Pc86Folder-Pc86Source', 'Pc86Folder', '1', 'Pc86Source', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86Folder-Pc86Target', 'Comp-Pc86Folder-Pc86Target', 'Pc86Folder', '1', 'Pc86Target', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86Folder-Pc86Transformation', 'Comp-Pc86Folder-Pc86Transformation', 'Pc86Folder', '1', 'Pc86Transformation', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86Mapping-Pc86MappletInstance', 'Comp-Pc86Mapping-Pc86MappletInstance', 'Pc86Mapping', '1', 'Pc86MappletInstance', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86Mapping-Pc86SourceInstance', 'comp-Pc86Mapping-Pc86SourceInstance', 'Pc86Mapping', '1', 'Pc86SourceInstance', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86Mapping-Pc86TargetInstance', 'comp-Pc86Mapping-Pc86TargetInstance', 'Pc86Mapping', '1', 'Pc86TargetInstance', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86Mapping-Pc86Transformation', 'Comp-Pc86Mapping-Pc86Transformation', 'Pc86Mapping', '1', 'Pc86Transformation', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86Mapping-Pc86TransformationInstance', 'Comp-Pc86Mapping-Pc86TransformationInstance', 'Pc86Mapping', '1', 'Pc86TransformationInstance', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86Mapplet-Pc86SourceInstance', 'comp-Pc86Mapplet-Pc86SourceInstance', 'Pc86Mapplet', '1', 'Pc86SourceInstance', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86Mapplet-Pc86TargetInstance', 'comp-Pc86Mapplet-Pc86TargetInstance', 'Pc86Mapplet', '1', 'Pc86TargetInstance', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86Mapplet-Pc86Transformation', 'Comp-Pc86Mapplet-Pc86Transformation', 'Pc86Mapplet', '1', 'Pc86Transformation', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86Mapplet-Pc86TransformationInstance', 'Comp-Pc86Mapplet-Pc86TransformationInstance', 'Pc86Mapplet', '1', 'Pc86TransformationInstance', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86MappletInstance-Pc86SourceInstance', 'comp-Pc86MappletInstance-Pc86SourceInstance', 'Pc86MappletInstance', '1', 'Pc86SourceInstance', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86MappletInstance-Pc86TargetInstance', 'comp-Pc86MappletInstance-Pc86TargetInstance', 'Pc86MappletInstance', '1', 'Pc86TargetInstance', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86MappletInstance-Pc86Transformation', 'comp-Pc86MappletInstance-Pc86Transformation', 'Pc86MappletInstance', '1', 'Pc86Transformation', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86MappletInstance-Pc86TransformationInstance', 'Comp-Pc86MappletInstance-Pc86TransformationInstance', 'Pc86MappletInstance', '1', 'Pc86TransformationInstance', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86PowerMart-Pc86Repository', 'Comp-Pc86PowerMart-Repository, Pc86Repository', 'Pc86PowerMart', '1', 'Pc86Repository', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86Project-Pc86PowerMart', 'comp-Pc86Project-Pc86PowerMart', 'Pc86Project', '1', 'Pc86PowerMart', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86Repository-Pc86Folder', 'Comp-Pc86Repository-Pc86Folder', 'Pc86Repository', '1', 'Pc86Folder', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86Schema-Pc86ColumnSet', 'comp-Pc86Schema-Pc86ColumnSet', 'Pc86Schema', '1', 'Pc86ColumnSet', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86Source-Pc86Sourcefield', 'Comp-Pc86Source-Pc86Sourcefield', 'Pc86Source', '1', 'Pc86Sourcefield', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86SourceInstance-Pc86Catalog', 'comp-Pc86SourceInstance-Pc86Catalog', 'Pc86SourceInstance', '1', 'Pc86Catalog', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86SourceInstance-Pc86FlatFile', 'comp-Pc86SourceInstance-Pc86FlatFile', 'Pc86SourceInstance', '1', 'Pc86FlatFile', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86Target-Pc86Targetfield', 'Comp-Pc86Target-Pc86Targetfield', 'Pc86Target', '1', 'Pc86Targetfield', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86TargetInstance-Pc86Catalog', 'comp-Pc86TargetInstance-Pc86Catalog', 'Pc86TargetInstance', '1', 'Pc86Catalog', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86TargetInstance-Pc86FlatFile', 'comp-Pc86TargetInstance-Pc86FlatFile', 'Pc86TargetInstance', '1', 'Pc86FlatFile', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86Transformation-Pc86Sourcefield', 'Comp-Pc86Transformation-Pc86Sourcefield', 'Pc86Transformation', '1', 'Pc86Sourcefield', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86Transformation-Pc86Transformfield', 'Comp-Pc86Transformation-Pc86Transformfield', 'Pc86Transformation', '1', 'Pc86Transformfield', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86TransformationInstance-Pc86SourceInstance', 'comp-Pc86TransformationInstance-Pc86SourceInstance', 'Pc86TransformationInstance', '1', 'Pc86SourceInstance', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86TransformationInstance-Pc86TargetInstance', 'comp-Pc86TransformationInstance-Pc86TargetInstance', 'Pc86TransformationInstance', '1', 'Pc86TargetInstance', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Pc86TransformationInstance-Pc86TransformfieldInstance', 'Comp-Pc86TransformationInstance-Pc86TransformfieldInstance', 'Pc86TransformationInstance', '1', 'Pc86TransformfieldInstance', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-PersisCls-PersisAttribute', '持久化类组合持久化属性', 'PersisCls', '1', 'PersisAttribute', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-PowerDesigner-PDProcedure', 'Comp-PowerDesigner-PDProcedure', 'PowerDesigner', '1', 'PDProcedure', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-PowerDesigner-PDSequence', 'Comp-PowerDesigner-PDSequence', 'PowerDesigner', '1', 'PDSequence', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-PowerDesigner-PDTable', 'Comp-PowerDesigner-PDTable', 'PowerDesigner', '1', 'PDTable', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-PowerDesigner-PDTablespace', 'Comp-PowerDesigner-PDTablespace', 'PowerDesigner', '1', 'PDTablespace', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-PowerDesigner-PDView', 'Comp-PowerDesigner-PDView', 'PowerDesigner', '1', 'PDView', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Procedure-ProcedureColumn', '存储过程组合参数', 'Procedure', '1', 'ProcedureColumn', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Procedure-Transformation', '存储过程组合转换', 'Procedure', '1', 'Transformation', '*', '存储过程组合转换');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-PublicCode-CodeItem', '代码标准组合代码项', 'PublicCode', '1', 'CodeItem', '*', '代码标准组合代码项');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Qualifier-QualifierItem', '修饰词组合修饰词代码', 'Qualifier', '1', 'QualifierItem', '*', '修饰词组合修饰词代码');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Redis-RedisDatabase', '相关的数据库', 'Redis', '1', 'RedisDatabase', '*', '');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Redis-RedisNode', '相关的节点', 'Redis', '1', 'RedisNode', '*', '');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-RedisDatabase-RedisHash', '数据库下的Hash', 'RedisDatabase', '1', 'RedisHash', '*', '');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-RedisDatabase-RedisList', '数据库下的List', 'RedisDatabase', '1', 'RedisList', '*', '');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-RedisDatabase-RedisSet', '数据库下的Set', 'RedisDatabase', '1', 'RedisSet', '*', '');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-RedisDatabase-RedisString', '数据库下的String', 'RedisDatabase', '1', 'RedisString', '*', '');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-RedisDatabase-RedisZSet', '数据库下的ZSet', 'RedisDatabase', '1', 'RedisZSet', '*', '');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-RedisNode-RedisNode', '节点下的节点', 'RedisNode', '1', 'RedisNode', '*', '');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Report-ReportItem', '报表组合报表项', 'Report', '1', 'ReportItem', '*', '报表组合报表项');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-ReqParam-ReqParam', '请求参数组合请求参数', 'ReqParam', '1', 'ReqParam', '*', '请求参数组合请求参数');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-RespParam-RespParam', '返回参数组合返回参数', 'RespParam', '1', 'RespParam', '*', '返回参数组合返回参数');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Root-BOFolder', '关联的BO文件夹', 'Root', '1', 'BOFolder', '*', '关联的BO文件夹');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Root-Cognos8_Project', '关联的Cognos', 'Root', '1', 'Cognos8_Project', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Root-Database', '关联的数据库', 'Root', '1', 'Database', '*', '关联的数据库');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Root-Directory', '组合标准目录', 'Root', '1', 'Directory', '*', '');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Root-ETLActivity', '关联的ETL工程', 'Root', '1', 'ETLActivity', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Root-Pc86Project', '关联的PowerCenter', 'Root', '1', 'Pc86Project', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Root-PowerDesigner', '关联的PowerDesigner', 'Root', '1', 'PowerDesigner', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Root-Schema', '根组合Schema', 'Root', '1', 'Schema', '*', '根组合Schema');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Root-System', '根组合系统', 'Root', '1', 'System', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Root-TFMSystem', '关联的转换系统', 'Root', '1', 'TFMSystem', '*', '关联的转换系统');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Root-Transformation', '根组合转换', 'Root', '1', 'Transformation', '*', '根组合转换');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Root-Webapi', '根组合Web应用接口', 'Root', '1', 'Webapi', '*', '根组合Web应用接口');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Schema-Function', 'Schema组合函数', 'Schema', '1', 'Function', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Schema-OraPackage', 'schema组合oraPackage', 'Schema', '1', 'OraPackage', '*', '模式组合包');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Schema-PI', 'Schema组合PI', 'Schema', '1', 'PI', '*', 'Schema组合PI');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Schema-Procedure', 'Schema组合存储过程', 'Schema', '1', 'Procedure', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Schema-Table', 'Schema组合表', 'Schema', '1', 'Table', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Schema-TdMacro', 'Schema组合宏', 'Schema', '1', 'TdMacro', '*', 'Schema组合宏');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Schema-Trigger', 'Schema组合触发器', 'Schema', '1', 'Trigger', '*', '库组合触发器');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Schema-View', 'Schema组合视图', 'Schema', '1', 'View', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-System-Folder', '系统组合文件夹', 'System', '1', 'Folder', '*', '系统组合文件夹');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-System-Kettle', '系统组合kettle', 'System', '1', 'Kettle', '*', '系统组合kettle');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-System-Schema', '系统组合Schema', 'System', '1', 'Schema', '*', '系统组合Schema');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-System-Transformation', '系统组合转换', 'System', '1', 'Transformation', '*', '系统组合转换');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-System-Webapi', '系统组合WebAPI', 'System', '1', 'Webapi', '*', '系统组合WebAPI');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-TFMActivity-TFMActivity', '转换活动组合转换活动', 'TFMActivity', '1', 'TFMActivity', '*', '转换活动组合转换活动');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-TFMActivity-TFMTask', '转换活动组合转换任务', 'TFMActivity', '1', 'TFMTask', '*', '转换活动组合转换任务');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-TFMSystem-TFMActivity', '转换系统组合转换活动', 'TFMSystem', '1', 'TFMActivity', '*', '转换系统组合转换活动');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-TFMSystem-TFMSystem', '转换系统组合转换系统', 'TFMSystem', '1', 'TFMSystem', '*', '转换系统组合转换系统');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-TFMSystem-TFMTask', '转换系统组合转换任务', 'TFMSystem', '1', 'TFMTask', '*', '转换系统组合转换任务');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-TFMTask-Transformation', '转换任务组合转换', 'TFMTask', '1', 'Transformation', '*', '转换任务组合转换');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Table-DistKey', '表组合分布键', 'Table', '1', 'DistKey', '*', '表组合分布键');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Table-ForeignKey', '表组合外键', 'Table', '1', 'ForeignKey', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Table-PI', '相关的PI', 'Table', '1', 'PI', '*', '');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Table-Partition', '表组合表分区', 'Table', '1', 'Partition', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Table-PartitionTable', '表组合分区表', 'Table', '1', 'PartitionTable', '*', '表组合分区表');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Table-PrimaryKey', '表和主键', 'Table', '1', 'PrimaryKey', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Table-SQLIndex', '表和索引', 'Table', '1', 'SQLIndex', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Transformation-Procedure', '转换组合存储过程', 'Transformation', '1', 'Procedure', '*', '转换组合存储过程');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Transformation-Transformation', '转换组合转换', 'Transformation', '1', 'Transformation', '*', '转换组合转换');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Transformation-TransformationMap', '转换组合转换映射', 'Transformation', '1', 'TransformationMap', '*', '转换组合转换映射');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Trigger-Transformation', '触发器组合转换', 'Trigger', '1', 'Transformation', '*', '触发器组合转换');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-View-Column', '视图组合字段', 'View', '1', 'Column', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-View-SQLIndex', '视图组合索引', 'View', '1', 'SQLIndex', '*', '视图组合索引');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Webapi-ReqParam', 'Webapi组合请求参数', 'Webapi', '1', 'ReqParam', '*', 'Webapi组合请求参数');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Comp-Webapi-RespParam', 'Webapi组合返回参数', 'Webapi', '1', 'RespParam', '*', 'Webapi组合返回参数');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('IndexedFeatureInfo', 'IndexedFeatureInfo', 'Index', '1', 'IndexedFeature', '*', 'The IndexedFeatureInfo association connects an Index instance to information about
how the StructuralFeature instances that are constituents of the Index’s key are used by the Index.');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('IndexedFeatures', 'IndexedFeatures', 'IndexedFeature', '1', 'StructuralFeature', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('KeyRelationshipFeatures', 'KeyRelationshipFeatures', 'KeyRelationship', '*', 'StructuralFeature', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('Table_Column_comp', '表组合字段', 'Table', '1', 'Column', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('UniqueFeature', 'UniqueFeature', 'UniqueKey', '1', 'StructuralFeature', '*', null);
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('entityToCandidateKey', 'entityToCandidateKey', 'Entity', '1', 'CandidateKey', '*', 'ER实体包含ER候选键');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('entityToCheckConstraint', 'entityToCheckConstraint', 'Entity', '1', 'CheckConstraint', '*', 'ER实体包含检查约束');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('entityToNonUniqueKey', 'entityToNonUniqueKey', 'Entity', '1', 'NonUniqueKey', '*', 'ER实体包含非唯一键');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('entityToUniqueConstraint', 'entityToUniqueConstraint', 'Entity', '1', 'UniqueConstraint', '*', 'ER实体包含唯一约束');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('modelLibraryToModel', 'modelLibraryToModel', 'ModelLibrary', '1', 'ERModel', '*', 'ER模型库包含ER模型');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('modelLibraryToModelLibrary', 'modelLibraryToModelLibrary', 'ModelLibrary', '1', 'ModelLibrary', '*', 'ER模型库包含子ER模型库');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('modelToDomain', 'modelToDomain', 'ERModel', '1', 'Domain', '*', 'ER模型包含ER公共域');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('modelToEntity', 'modelToEntity', 'ERModel', '1', 'Entity', '*', 'ER模型包含ER实体');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('modelToProcedure', 'modelToProcedure', 'ERModel', '1', 'Procedure', '*', 'ER模型包含Procedure');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('modelToRelationship', 'modelToRelationship', 'ERModel', '1', 'Relationship', '*', 'ER模型包含ER关系');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('modelToSubjectArea', 'modelToSubjectArea', 'ERModel', '1', 'SubjectArea', '*', 'ER模型包含ER主题域');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('modelToValidationRule', 'modelToValidationRule', 'ERModel', '1', 'ValidationRule', '*', 'ER模型包含ER校验规则');
INSERT INTO edgs.t00_relation_comp (rel_id, rel_name, from_classifier_id, from_multiplicity, to_classifier_id, to_multiplicity, description) VALUES ('relationshipToRelationshipEnd', 'relationshipToRelationshipEnd', 'Relationship', '1', 'RelationshipEnd', '*', 'ER关系包含ER关系端');
