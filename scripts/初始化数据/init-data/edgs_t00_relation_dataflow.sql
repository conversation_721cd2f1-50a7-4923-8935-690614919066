INSERT INTO edgs.t00_relation_dataflow (source_classifier_id, target_classifier_id, transform_classifier_id, level) VALUES ('Column', 'Column', 'Procedure', 1);
INSERT INTO edgs.t00_relation_dataflow (source_classifier_id, target_classifier_id, transform_classifier_id, level) VALUES ('Table', 'Table', 'Procedure', 2);
INSERT INTO edgs.t00_relation_dataflow (source_classifier_id, target_classifier_id, transform_classifier_id, level) VALUES ('Table', 'View', 'Procedure', 2);
INSERT INTO edgs.t00_relation_dataflow (source_classifier_id, target_classifier_id, transform_classifier_id, level) VALUES ('View', 'View', 'Procedure', 2);
INSERT INTO edgs.t00_relation_dataflow (source_classifier_id, target_classifier_id, transform_classifier_id, level) VALUES ('View', 'Table', 'Procedure', 2);
INSERT INTO edgs.t00_relation_dataflow (source_classifier_id, target_classifier_id, transform_classifier_id, level) VALUES ('Schema', 'Schema', null, 3);
INSERT INTO edgs.t00_relation_dataflow (source_classifier_id, target_classifier_id, transform_classifier_id, level) VALUES ('System', 'System', null, 4);
INSERT INTO edgs.t00_relation_dataflow (source_classifier_id, target_classifier_id, transform_classifier_id, level) VALUES ('Table', 'Table', 'View', 2);
INSERT INTO edgs.t00_relation_dataflow (source_classifier_id, target_classifier_id, transform_classifier_id, level) VALUES ('Table', 'View', 'View', 2);
INSERT INTO edgs.t00_relation_dataflow (source_classifier_id, target_classifier_id, transform_classifier_id, level) VALUES ('Column', 'Column', 'View', 1);
INSERT INTO edgs.t00_relation_dataflow (source_classifier_id, target_classifier_id, transform_classifier_id, level) VALUES ('View', 'View', 'View', 2);
INSERT INTO edgs.t00_relation_dataflow (source_classifier_id, target_classifier_id, transform_classifier_id, level) VALUES ('View', 'Table', 'View', 2);
INSERT INTO edgs.t00_relation_dataflow (source_classifier_id, target_classifier_id, transform_classifier_id, level) VALUES ('Table', 'Table', 'Transformation', 2);
INSERT INTO edgs.t00_relation_dataflow (source_classifier_id, target_classifier_id, transform_classifier_id, level) VALUES ('Column', 'Column', 'KettleColumn', 1);
INSERT INTO edgs.t00_relation_dataflow (source_classifier_id, target_classifier_id, transform_classifier_id, level) VALUES ('Table', 'Table', 'KettleFile', 2);
INSERT INTO edgs.t00_relation_dataflow (source_classifier_id, target_classifier_id, transform_classifier_id, level) VALUES ('Table', 'FRDataSet', 'FRDataSet', 2);
INSERT INTO edgs.t00_relation_dataflow (source_classifier_id, target_classifier_id, transform_classifier_id, level) VALUES ('Column', 'FROutput', 'FRDataSet', 1);
INSERT INTO edgs.t00_relation_dataflow (source_classifier_id, target_classifier_id, transform_classifier_id, level) VALUES ('FRDataSet', 'FRReport', 'FRDataSet', 2);
INSERT INTO edgs.t00_relation_dataflow (source_classifier_id, target_classifier_id, transform_classifier_id, level) VALUES ('Schema', 'FineReport', null, 3);
INSERT INTO edgs.t00_relation_dataflow (source_classifier_id, target_classifier_id, transform_classifier_id, level) VALUES ('FineReport', 'FineReport', null, 3);
