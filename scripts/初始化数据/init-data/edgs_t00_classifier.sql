INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('AssetCatalog', '资产目录', 'N', 'Y', 'AssetCatalog', 'mm_asset', '资产目录', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Attribute', '属性', 'Y', 'Y', 'Attribute', 'cwm_core', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('BaseAsset', '基础资产', 'N', 'Y', 'BaseAsset', 'mm_ds', '基础资产', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('BaseStandard', '基础标准', 'N', 'Y', 'BaseStandard', 'mm_ds', '基础标准', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('BusinessTerm', '业务术语', 'N', 'Y', 'BusinessTerm', 'mm_ds', '业务术语', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('CandidateKey', '候选键', 'N', 'Y', 'CandidateKey', 'mm_erwin', '又称业务主键，但只有PrimaryKey或其子类可以成为主键。(since 1.4)', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('CdbDim', '维度', 'N', 'Y', 'CdbDim', 'mm_kpi', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('CdbIndex', '指标', 'N', 'Y', 'CdbIndex', 'mm_kpi', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('CdbKpi', 'KPI', 'N', 'Y', 'CdbKpi', 'mm_kpi', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('CdbReport', '报表', 'N', 'Y', 'CdbReport', 'mm_kpi', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('CdbSchema', '库', 'N', 'Y', 'CdbSchema', 'mm_kpi', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('CdbSourceCol', '源字段', 'N', 'Y', 'CdbSourceCol', 'mm_kpi', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('CdbSourceTable', '源表', 'N', 'Y', 'CdbSourceTable', 'mm_kpi', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('CheckConstraint', '检查限制', 'N', 'Y', 'CheckConstraint', 'cwm_relational', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('CheckConstraintUsage', '检查约束用法', 'N', 'Y', 'CheckConstraintUsage', 'mm_erwin', '检查约束用法，分为表级约束检查、字段级约束检查，依赖校验规则。(since 7.1)', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Class', '基本类', 'Y', 'Y', 'Class', 'cwm_core', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Classifier', '表、文件', 'Y', 'Y', 'Classifier', 'cwm_core', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ClassifierMap', '表、文件级映射', 'N', 'Y', 'ClassifierMap', 'cwm_transformation', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('CodeItem', '代码项', 'N', 'Y', 'CodeItem', 'mm_ds', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Cognos8_Calculation', '计算', 'N', 'Y', 'Cognos8_Calculation', 'mm_cognos8', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Cognos8_Catalog', '目录', 'N', 'Y', 'Cognos8_Catalog', 'mm_cognos8', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Cognos8_Column', '字段', 'N', 'Y', 'Cognos8_Column', 'mm_cognos8', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Cognos8_ColumnSet', '视图表', 'N', 'Y', 'Cognos8_ColumnSet', 'mm_cognos8', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Cognos8_DataItem', '数据项', 'N', 'Y', 'Cognos8_DataItem', 'mm_cognos8', 'Cognos8_DataItem', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Cognos8_DataObject', '数据集', 'N', 'Y', 'Cognos8_DataObject', 'mm_cognos8', 'Cognos8_DataObject', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Cognos8_DataSource', '数据源', 'N', 'Y', 'Cognos8_DataSource', 'mm_cognos8', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Cognos8_Filter', '过滤器', 'N', 'Y', 'Cognos8_Filter', 'mm_cognos8', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Cognos8_Folder', '文件夹', 'N', 'Y', 'Cognos8_Folder', 'mm_cognos8', 'Cognos8_Folder', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Cognos8_Model', '模型', 'N', 'Y', 'Cognos8_Model', 'mm_cognos8', 'Cognos8_Model', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Cognos8_Namespace', '名称空间', 'N', 'Y', 'Cognos8_Namespace', 'mm_cognos8', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Cognos8_Package', '包', 'N', 'Y', 'Cognos8_Package', 'mm_cognos8', 'Cognos8_Package', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Cognos8_Parameter', '参数', 'N', 'Y', 'Cognos8_Parameter', 'mm_cognos8', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Cognos8_Project', '工程', 'N', 'Y', 'Cognos8_Project', 'mm_cognos8', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Cognos8_QueryItem', '查询项', 'N', 'Y', 'Cognos8_QueryItem', 'mm_cognos8', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Cognos8_QuerySubject', '查询主题', 'N', 'Y', 'Cognos8_QuerySubject', 'mm_cognos8', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Cognos8_Report', '报表', 'N', 'Y', 'Cognos8_Report', 'mm_cognos8', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Cognos8_ReportField', '报表项', 'N', 'Y', 'Cognos8_ReportField', 'mm_cognos8', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Cognos8_Schema', '数据库', 'N', 'Y', 'Cognos8_Schema', 'mm_cognos8', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Cognos8_ShortCut', '快捷方式', 'N', 'Y', 'Cognos8_ShortCut', 'mm_cognos8', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Cognos8_StoredProcedure', '存储过程', 'N', 'Y', 'Cognos8_StoredProcedure', 'mm_cognos8', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Cognos8_SubReport', '子报表', 'N', 'Y', 'Cognos8_SubReport', 'mm_cognos8', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Column', '字段', 'N', 'Y', 'Column', 'cwm_relational', 'Column', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ColumnFamily', '列族', 'N', 'Y', 'ColumnFamily', 'Hbase', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ColumnQualifier', '列限定名', 'N', 'Y', 'ColumnQualifier', 'Hbase', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ColumnSet', '字段集', 'N', 'Y', 'ColumnSet', 'cwm_relational', 'ColumnSet', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ComputeRule', '统计规则', 'N', 'Y', 'ComputeRule', 'mm_ds', '统计规则', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('DataX', 'DataX', 'N', 'Y', 'DataX', 'mm_datax', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('DataXColumn', '字段', 'N', 'Y', 'DataXColumn', 'mm_datax', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('DataXConnect', '连接', 'N', 'Y', 'DataXConnect', 'mm_datax', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('DataXFile', '文件', 'N', 'Y', 'DataXFile', 'mm_datax', '如果DATAX有多个源表或者目标表，就相当于多个FILE', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('DataXFolder', '目录', 'N', 'N', 'DataXFolder', 'mm_datax', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('DataxJob', 'DataX任务', 'N', 'Y', 'DataxJob', 'mm_datax', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Dimension', '维度标准', 'N', 'Y', 'Dimension', 'mm_ds', 'Dimension', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('DimensionItem', '维度值', 'N', 'Y', 'DimensionItem', 'mm_ds', 'DimensionItem', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Directory', '目录', 'N', 'Y', 'Directory', 'mm_ds', '标准目录', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('DistKey', '分布键', 'N', 'Y', 'DistKey', 'cwm_relational', 'Netezza分布键', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Domain', 'ER公共域', 'N', 'Y', 'Domain', 'mm_erwin', 'Domain instances represent restrictions on data types declared elsewhere and can be used as the type of Attribute instances.(since 1.4)', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ERColumn', '字段', 'N', 'Y', 'ERColumn', 'mm_erwin', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ERForeignKey', '外键', 'N', 'Y', 'ERForeignKey', 'mm_erwin', '外键，继承自CWM-关系型数据库的ForeignKey。(since 1.4)', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ERModel', 'ER模型', 'N', 'Y', 'ERModel', 'mm_erwin', '逻辑数据模型或物理数据模型。(since 1.4)', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ERPrimaryKey', '主键', 'N', 'Y', 'ERPrimaryKey', 'mm_erwin', '主键，继承自CWM-关系数据库的PrimaryKey，用于标识表的主键。(since 1.4)', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ERSequence', '序列', 'N', 'Y', 'ERSequence', 'mm_erwin', '序列。(since 1.4)', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ERTable', 'ER表', 'N', 'Y', 'ERTable', 'mm_erwin', 'ER表', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ERTablespace', '表空间', 'N', 'Y', 'ERTablespace', 'mm_erwin', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ERTrigger', '触发器', 'N', 'Y', 'ERTrigger', 'mm_erwin', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ERView', 'ER视图', 'N', 'Y', 'ERView', 'mm_erwin', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ETLActivity', 'ETL工程', 'N', 'Y', 'ETLActivity', 'cwm_transformation', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ETLConstant', '常量', 'N', 'Y', 'ETLConstant', 'cwm_transformation', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ETLFile', 'ETL文件', 'N', 'Y', 'ETLFile', 'cwm_transformation', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ETLScript', 'ETL脚本', 'N', 'Y', 'ETLScript', 'cwm_transformation', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ETLSubSystem', 'ETL子系统', 'N', 'Y', 'ETLSubSystem', 'cwm_transformation', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ETLTask', 'ETL任务', 'N', 'Y', 'ETLTask', 'cwm_transformation', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ETLTransformer', 'ETL转换', 'N', 'Y', 'ETLTransformer', 'cwm_transformation', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Element', '基本元素', 'Y', 'Y', 'Element', 'cwm_core', '最顶级模型', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Entity', 'ER实体', 'N', 'Y', 'Entity', 'mm_erwin', 'Instances of the Entity class are the primary objects in a ER model. They represent ideas, processes, and things of interest in an application system or tool model.', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('EsCluster', '集群', 'N', 'Y', 'EsCluster', 'Elasticsearch', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('EsDocType', '文档类型', 'N', 'Y', 'EsDocType', 'Elasticsearch', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('EsField', '字段', 'N', 'N', 'EsField', 'Elasticsearch', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('EsIndex', '索引', 'N', 'Y', 'EsIndex', 'Elasticsearch', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('EsNode', '节点', 'N', 'Y', 'EsNode', 'Elasticsearch', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('EtlAnnotate', '注释', 'N', 'Y', 'EtlAnnotate', 'mm_annotate', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('EtlAnnotateFile', '注释文件', 'N', 'Y', 'EtlAnnotateFile', 'mm_annotate', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ExtAsset', '外部资产', 'N', 'Y', 'ExtAsset', 'mm_ds', '外部资产', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ExtData', '外部数据', 'N', 'Y', 'ExtData', 'mm_asset', '外部数据', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ExtResource', '外部资源', 'N', 'Y', 'ExtResource', 'mm_ds', '外部资源', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('FRDataSet', '帆软数据集', 'N', 'Y', 'FRDataSet', 'mm_finereport', 'FRDataSet', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('FRDatasource', '帆软数据源', 'N', 'Y', 'FRDatasource', 'mm_finereport', 'FRDatasource', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('FRFolder', '帆软文件夹', 'Y', 'N', 'FRFolder', 'mm_finereport', 'FRFolder', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('FRInput', '数据集入参', 'N', 'Y', 'FRInput', 'mm_finereport', 'FRInput', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('FROutput', '数据集出参', 'N', 'Y', 'FROutput', 'mm_finereport', 'FROutput', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('FRReport', '帆软报表', 'N', 'Y', 'FRReport', 'mm_finereport', 'FRReport', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Feature', '字段、属性', 'Y', 'Y', 'Feature', 'cwm_core', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('FeatureMap', '字段、属性级映射', 'N', 'Y', 'FeatureMap', 'cwm_transformation', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('FineReport', '帆软根节点', 'N', 'N', 'FineReport', 'mm_finereport', 'FineReport', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Folder', '文件夹', 'N', 'N', 'Folder', 'mm_core', '文件夹', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ForeignKey', '外键', 'N', 'Y', 'ForeignKey', 'cwm_relational', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Function', '函数', 'N', 'Y', 'Function', 'cwm_relational', '函数', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('HbaseNamespace', 'Hbase名称空间', 'N', 'Y', 'HbaseNamespace', 'Hbase', 'Hbase名称空间', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('HbaseTable', '表', 'N', 'Y', 'HbaseTable', 'Hbase', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Index', '索引', 'N', 'Y', 'Index', 'cwm_keyindexes', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('IndexAsset', '指标资产', 'N', 'Y', 'IndexAsset', 'mm_ds', '指标资产', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('IndexStandard', '指标标准', 'N', 'Y', 'IndexStandard', 'mm_ds', '指标标准', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('IndexedFeature', '索引特征', 'N', 'Y', 'IndexedFeature', 'cwm_keyindexes', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('InternalAsset', '内部资产', 'N', 'Y', 'InternalAsset', 'mm_ds', '内部资产', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Kettle', 'Kettle', 'N', 'Y', 'Kettle', 'mm_kettle', 'Kettle', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('KettleColumn', '字段', 'N', 'Y', 'KettleColumn', 'mm_kettle', 'Kettle文件字段', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('KettleConnection', '连接', 'N', 'Y', 'KettleConnection', 'mm_kettle', 'Kettle数据源连接', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('KettleDirectory', '目录', 'N', 'N', 'KettleDirectory', 'mm_kettle', '目录', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('KettleFile', '文件', 'N', 'Y', 'KettleFile', 'mm_kettle', 'Kettle文件', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('KettleJob', '作业', 'N', 'Y', 'KettleJob', 'mm_kettle', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('KettleTransformation', '转换', 'N', 'Y', 'KettleTransformation', 'mm_kettle', 'Kettle转换', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('KettleVariable', '变量', 'N', 'Y', 'KettleVariable', 'mm_kettle', 'Kettle变量', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('KeyGroup', '键集合', 'Y', 'Y', 'KeyGroup', 'mm_erwin', '键集合，提供统一的属性和关系，主键、业务主键、外键、非唯一键皆继承该类。(since 1.4)', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('KeyGroupMember', '键成员', 'N', 'Y', 'KeyGroupMember', 'mm_erwin', '键成员，描述字段作为键的设置和属性。(since 1.4)', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('KeyRelationship', '键关系', 'N', 'Y', 'KeyRelationship', 'cwm_keyindexes', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Lable', '标签', 'N', 'Y', 'Lable', 'mm_asset', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('LifeCycle', '生命周期', 'N', 'Y', 'LifeCycle', 'cwm_relational', '生命周期管理类', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ModelElement', '模型元素', 'Y', 'Y', 'ModelElement', 'cwm_core', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ModelLibrary', 'ER模型库', 'N', 'Y', 'ModelLibrary', 'mm_erwin', 'ER模型库是ER模型或子ER模型库的集合，是Erwin采集的根元数据。(since 1.4)', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('NamedColumnSet', '命名字段集', 'N', 'Y', 'NamedColumnSet', 'cwm_relational', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('NonPersisAttribute', '非持久化字段、属性', 'N', 'Y', 'NonPersisAttribute', 'cwm_transformation', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('NonPersisCls', '非持久化类、文件', 'N', 'Y', 'NonPersisCls', 'cwm_transformation', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('NonUniqueKey', 'ER非唯一键', 'N', 'Y', 'NonUniqueKey', 'mm_erwin', '等同于CWM中Index，非唯一键的字段不是必须的或unique。(since 1.4)', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('OraPackage', '包', 'N', 'Y', 'OraPackage', 'cwm_relational', 'Oracle包', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('PDColumn', 'PDColumn', 'N', 'Y', 'PDColumn', 'mm_powerdesigner', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('PDIndex', 'PDIndex', 'N', 'Y', 'PDIndex', 'mm_powerdesigner', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('PDIndexColumn', 'PDIndexColumn', 'N', 'Y', 'PDIndexColumn', 'mm_powerdesigner', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('PDKey', 'PDKey', 'N', 'Y', 'PDKey', 'mm_powerdesigner', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('PDProcedure', 'PDProcedure', 'N', 'Y', 'PDProcedure', 'mm_powerdesigner', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('PDSequence', 'PDSequence', 'N', 'Y', 'PDSequence', 'mm_powerdesigner', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('PDTable', 'PDTable', 'N', 'Y', 'PDTable', 'mm_powerdesigner', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('PDTablespace', 'PDTablespace', 'N', 'Y', 'PDTablespace', 'mm_powerdesigner', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('PDTrigger', 'PDTrigger', 'N', 'Y', 'PDTrigger', 'mm_powerdesigner', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('PDView', 'PDView', 'N', 'Y', 'PDView', 'mm_powerdesigner', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('PDViewColumn', 'PDViewColumn', 'N', 'Y', 'PDViewColumn', 'mm_powerdesigner', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('PERLScript', 'PERL脚本', 'N', 'Y', 'PERLScript', 'cwm_transformation', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('PI', 'PI', 'N', 'Y', 'PI', 'cwm_relational', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ParseCatalog', '目录', 'N', 'Y', 'ParseCatalog', 'cwm_transformation', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ParseSchema', '库', 'N', 'Y', 'ParseSchema', 'cwm_transformation', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Partition', '表分区', 'N', 'Y', 'Partition', 'cwm_relational', '表分区', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('PartitionTable', '分区表', 'N', 'Y', 'PartitionTable', 'cwm_relational', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Pc86Catalog', '目录', 'N', 'Y', 'Pc86Catalog', 'mm_powercenter', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Pc86ColumnSet', '视图表', 'N', 'Y', 'Pc86ColumnSet', 'mm_powercenter', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Pc86DbColumn', '字段', 'N', 'Y', 'Pc86DbColumn', 'mm_powercenter', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Pc86FlatFile', '接口文件', 'N', 'Y', 'Pc86FlatFile', 'mm_powercenter', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Pc86FlatFileColumn', '接口文件属性', 'N', 'Y', 'Pc86FlatFileColumn', 'mm_powercenter', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Pc86Folder', '文件夹', 'N', 'Y', 'Pc86Folder', 'mm_powercenter', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Pc86Instance', '实例', 'N', 'Y', 'Pc86Instance', 'mm_powercenter', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Pc86Mapping', '映射', 'N', 'Y', 'Pc86Mapping', 'mm_powercenter', 'Mapping', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Pc86Mapplet', '映射片段', 'N', 'Y', 'Pc86Mapplet', 'mm_powercenter', 'Mapplet', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Pc86MappletInstance', '映射片段实例', 'N', 'Y', 'Pc86MappletInstance', 'mm_powercenter', 'Mapplet实例', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Pc86PowerMart', '集市', 'N', 'Y', 'Pc86PowerMart', 'mm_powercenter', 'powerCenter工程', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Pc86Project', '工程', 'N', 'Y', 'Pc86Project', 'mm_powercenter', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Pc86Repository', '资源库', 'N', 'Y', 'Pc86Repository', 'mm_powercenter', 'powerCenter工程', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Pc86Schema', '库', 'N', 'Y', 'Pc86Schema', 'mm_powercenter', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Pc86Source', '源', 'N', 'Y', 'Pc86Source', 'mm_powercenter', '源', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Pc86SourceInstance', '源实例', 'N', 'Y', 'Pc86SourceInstance', 'mm_powercenter', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Pc86Sourcefield', '源字段', 'N', 'Y', 'Pc86Sourcefield', 'mm_powercenter', '源字段', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Pc86Target', '目标', 'N', 'Y', 'Pc86Target', 'mm_powercenter', '目标', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Pc86TargetInstance', '目标实例', 'N', 'Y', 'Pc86TargetInstance', 'mm_powercenter', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Pc86Targetfield', '目标字段', 'N', 'Y', 'Pc86Targetfield', 'mm_powercenter', '目标字段', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Pc86Transformation', '转换', 'N', 'Y', 'Pc86Transformation', 'mm_powercenter', '转换', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Pc86TransformationInstance', '转换实例', 'N', 'Y', 'Pc86TransformationInstance', 'mm_powercenter', '转换实例', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Pc86Transformfield', '转换字段', 'N', 'Y', 'Pc86Transformfield', 'mm_powercenter', '转换字段', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Pc86TransformfieldInstance', '转换字段实例', 'N', 'Y', 'Pc86TransformfieldInstance', 'mm_powercenter', '转换字段实例', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('PersisAttribute', '持久化字段、属性', 'N', 'Y', 'PersisAttribute', 'cwm_transformation', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('PersisCls', '持久化类、文件', 'N', 'Y', 'PersisCls', 'cwm_transformation', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('PowerDesigner', 'PowerDesigner', 'N', 'Y', 'PowerDesigner', 'mm_powerdesigner', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('PrimaryKey', '主键', 'N', 'Y', 'PrimaryKey', 'cwm_relational', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Procedure', '存储过程', 'N', 'Y', 'Procedure', 'cwm_relational', '存储过程元模型；', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ProcedureColumn', '存储过程参数', 'N', 'Y', 'ProcedureColumn', 'cwm_relational', '存储过程参数', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('PublicCode', '代码标准', 'N', 'Y', 'PublicCode', 'mm_ds', '代码标准', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Qualifier', '修饰词', 'N', 'Y', 'Qualifier', 'mm_ds', '修饰词', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('QualifierItem', '修饰词代码', 'N', 'Y', 'QualifierItem', 'mm_ds', '修饰词代码', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Redis', 'Redis', 'N', 'Y', 'Redis', 'mm_redis', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('RedisDatabase', '数据库', 'N', 'Y', 'RedisDatabase', 'mm_redis', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('RedisHash', 'Hash', 'N', 'Y', 'RedisHash', 'mm_redis', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('RedisHashField', '字段', 'N', 'Y', 'RedisHashField', 'mm_redis', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('RedisList', 'List', 'N', 'Y', 'RedisList', 'mm_redis', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('RedisNode', '节点', 'N', 'Y', 'RedisNode', 'mm_redis', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('RedisSet', 'Set', 'N', 'Y', 'RedisSet', 'mm_redis', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('RedisString', 'String', 'N', 'Y', 'RedisString', 'mm_redis', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('RedisZSet', 'ZSet', 'N', 'Y', 'RedisZSet', 'mm_redis', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Relationship', 'ER关系', 'N', 'Y', 'Relationship', 'mm_erwin', 'ER Relationship instances represent links between Entity instances.(since 1.4)', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('RelationshipEnd', 'ER关系端', 'N', 'Y', 'RelationshipEnd', 'mm_erwin', 'The RelationshipEnd class extends CWM’s AssociationEnd class to permit the definition of separate delete, update, and insert rules on each end of a Relationship. An ER model Relationship instance owns two or more RelationshipEnds via an inherited CWM association between the Association and AssociationEnd classes.', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Report', '报表', 'N', 'N', 'Report', 'mm_asset', '报表', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ReportItem', '报表项', 'N', 'Y', 'ReportItem', 'mm_asset', '报表项', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ReqParam', '请求参数', 'N', 'Y', 'ReqParam', 'mm_api', '请求参数', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('RespParam', '返回参数', 'N', 'Y', 'RespParam', 'mm_api', '返回参数', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Root', '根', 'N', 'Y', 'Root', 'mm_core', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('SHELLScript', 'Shell脚本', 'N', 'Y', 'SHELLScript', 'cwm_transformation', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('SQLIndex', '索引', 'N', 'Y', 'SQLIndex', 'cwm_relational', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('SQLScript', 'SQL脚本', 'N', 'Y', 'SQLScript', 'cwm_transformation', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Schema', '模式', 'N', 'Y', 'Schema', 'cwm_relational', 'Schema', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('StructuralFeature', '具有结构的字段、属性', 'Y', 'Y', 'StructuralFeature', 'cwm_core', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('SubjectArea', 'ER主题域', 'N', 'Y', 'SubjectArea', 'mm_erwin', '主题是ER实体的集合。(since 1.4)', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('System', '系统', 'N', 'Y', 'System', 'mm_system', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('TFMActivity', '转换活动', 'N', 'Y', 'TFMActivity', 'cwm_transformation', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('TFMSystem', 'ETL系统', 'N', 'Y', 'TFMSystem', 'cwm_transformation', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('TFMTask', '转换任务', 'N', 'Y', 'TFMTask', 'cwm_transformation', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Table', '表', 'N', 'N', 'Table', 'cwm_relational', 'Table', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Tablespace', '表空间', 'N', 'Y', 'Tablespace', 'cwm_relational', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('TdMacro', '宏', 'N', 'Y', 'TdMacro', 'cwm_relational', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('TeradataMacro', '宏', 'N', 'Y', 'TeradataMacro', 'mm_erwin', 'Teradata数据库的宏，类似存储过程或函数。(since 7.1)', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Transformation', '转换', 'N', 'Y', 'Transformation', 'cwm_transformation', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('TransformationMap', '转换映射', 'N', 'Y', 'TransformationMap', 'cwm_transformation', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Trigger', '触发器', 'N', 'Y', 'Trigger', 'cwm_relational', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('UniqueConstraint', '唯一约束', 'N', 'Y', 'UniqueConstraint', 'cwm_relational', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('UniqueKey', '唯一键', 'N', 'Y', 'UniqueKey', 'cwm_keyindexes', null, 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('ValidationRule', 'ER校验规则', 'N', 'Y', 'ValidationRule', 'mm_erwin', 'Contains an expression that describes the valid values for this attribute. If the baseType reference is not empty, the expression restricts the values of the base type indicated by it.', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('View', '视图', 'N', 'Y', 'View', 'cwm_relational', 'View', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('Webapi', '接口', 'N', 'Y', 'Webapi', 'mm_api', 'web应用接口1', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('dfgdfgdf', 'dfg', 'N', 'Y', 'dfgdfgdf', 'dfd', '', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('wd', 'wd2', 'N', 'Y', 'wd', 'ow_test', '很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述很长的测试描述', 't01_instance', 'N');
INSERT INTO edgs.t00_classifier (classifier_id, classifier_name, is_abstract, show_child, dis_icon, owner_pid, description, instance_table, enable_create_view) VALUES ('类是什么', '雷鸣', 'N', 'Y', '类是什么', 'Package@向潮的测试包', '', 't01_instance', 'N');
