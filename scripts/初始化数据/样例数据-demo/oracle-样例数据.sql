CREATE USER demo IDENTIFIED BY "Demo@Wcompass";
<PERSON><PERSON><PERSON> connect TO demo;
GRANT UNLIMITED TABLESPACE TO demo;
CREATE TABLE DEMO.fund (
    FCODE     VARCHAR2(10)  NOT NULL,
    SHORTNAME VARCHAR2(100) NOT NULL,
    FULLNAME  VARCHAR2(200) NOT NULL,
    FTYPE     VARCHAR2(20),
    ESTABDATE VARCHAR2(10)  NOT NULL,
    CONSTRAINT PK_FUND PRIMARY KEY (FCODE)
);
COMMENT ON TABLE DEMO.fund IS '基金信息表';
COMMENT ON COLUMN DEMO.fund.FCODE IS '基金代码';
COMMENT ON COLUMN DEMO.fund.SHORTNAME IS '基金简称';
COMMENT ON COLUMN DEMO.fund.FULLNAME IS '基金全称';
COMMENT ON COLUMN DEMO.fund.FTYPE IS '基金类型';
COMMENT ON COLUMN DEMO.fund.ESTABDATE IS '成立日期';

INSERT INTO DEMO.FUND (FCODE, SHORTNAME, FULLNAME, FTYPE, ESTABDATE) VALUES ('000004', 'ZH可转债债券C', 'ZH可转换债券债券型证券投资基金', '债券型', '2013-03-20');
INSERT INTO DEMO.FUND (FCODE, SHORTNAME, FULLNAME, FTYPE, ESTABDATE) VALUES ('000006', 'XB利得量化成长混合A', 'XB利得量化成长混合型发起式证券投资基金', '混合型', '2019-03-19');
INSERT INTO DEMO.FUND (FCODE, SHORTNAME, FULLNAME, FTYPE, ESTABDATE) VALUES ('000008', 'JSZZ500ETF联接A', 'JSZZ500交易型开放式指数证券投资基金联接基金', '联接基金', '2013-03-22');
INSERT INTO DEMO.FUND (FCODE, SHORTNAME, FULLNAME, FTYPE, ESTABDATE) VALUES ('000011', 'HX大盘精选混合', 'HX大盘精选证券投资基金', '混合型', '2004-08-11');
INSERT INTO DEMO.FUND (FCODE, SHORTNAME, FULLNAME, FTYPE, ESTABDATE) VALUES ('000014', 'HX聚利债券', 'HX聚利债券型证券投资基金', '债券型', '2013-03-19');
INSERT INTO DEMO.FUND (FCODE, SHORTNAME, FULLNAME, FTYPE, ESTABDATE) VALUES ('000015', 'HX纯债债券A', 'HX纯债债券型证券投资基金', '债券型', '2013-03-08');
INSERT INTO DEMO.FUND (FCODE, SHORTNAME, FULLNAME, FTYPE, ESTABDATE) VALUES ('000016', 'HX纯债债券C', 'HX纯债债券型证券投资基金', '债券型', '2013-03-08');
INSERT INTO DEMO.FUND (FCODE, SHORTNAME, FULLNAME, FTYPE, ESTABDATE) VALUES ('000017', 'CT可持续混合', 'CT可持续发展主题混合型证券投资基金', '混合型', '2013-03-27');
INSERT INTO DEMO.FUND (FCODE, SHORTNAME, FULLNAME, FTYPE, ESTABDATE) VALUES ('000020', 'JSCC品质投资混合', 'JSCC品质投资混合型证券投资基金', '混合型', '2013-03-19');
INSERT INTO DEMO.FUND (FCODE, SHORTNAME, FULLNAME, FTYPE, ESTABDATE) VALUES ('000021', 'HX优势增长混合', 'HX优势增长混合型证券投资基金', '混合型', '2006-11-24');

-- 创建运行日志表
CREATE TABLE DEMO.log (
    id          VARCHAR2(64)                        NOT NULL,
    tablename   VARCHAR2(32)                        NOT NULL,
    uniquelable VARCHAR2(100),
    indate      TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT log_pk PRIMARY KEY (id)
);

COMMENT ON TABLE DEMO.log IS '运行日志';

INSERT INTO DEMO.LOG (ID, TABLENAME, UNIQUELABLE, INDATE) VALUES ('00050bdb6cd711ebb81200e04c68268f', 'fund', '{''FCODE'': ''008104''}', TO_TIMESTAMP('2021-02-12 10:07:03.000000', 'YYYY-MM-DD HH24:MI:SS.FF6'));
INSERT INTO DEMO.LOG (ID, TABLENAME, UNIQUELABLE, INDATE) VALUES ('0007e3726cd611ebb81200e04c68268f', 'fund', '{''FCODE'': ''001955''}', TO_TIMESTAMP('2021-02-12 09:59:54.000000', 'YYYY-MM-DD HH24:MI:SS.FF6'));
INSERT INTO DEMO.LOG (ID, TABLENAME, UNIQUELABLE, INDATE) VALUES ('001ea50c6cd711ebb81200e04c68268f', 'fund', '{''FCODE'': ''008105''}', TO_TIMESTAMP('2021-02-12 10:07:04.000000', 'YYYY-MM-DD HH24:MI:SS.FF6'));
INSERT INTO DEMO.LOG (ID, TABLENAME, UNIQUELABLE, INDATE) VALUES ('0020215b6cd611ebb81200e04c68268f', 'fundmangerhis', '{''FCODE'': ''004236''}', TO_TIMESTAMP('2021-02-12 09:59:54.000000', 'YYYY-MM-DD HH24:MI:SS.FF6'));
INSERT INTO DEMO.LOG (ID, TABLENAME, UNIQUELABLE, INDATE) VALUES ('00365c6e6cd711ebb81200e04c68268f', 'fundmangerhis', '{''FCODE'': ''008156''}', TO_TIMESTAMP('2021-02-12 10:07:04.000000', 'YYYY-MM-DD HH24:MI:SS.FF6'));
INSERT INTO DEMO.LOG (ID, TABLENAME, UNIQUELABLE, INDATE) VALUES ('003787db6cd611ebb81200e04c68268f', 'fundmangerhis', '{''FCODE'': ''005421''}', TO_TIMESTAMP('2021-02-12 09:59:54.000000', 'YYYY-MM-DD HH24:MI:SS.FF6'));
INSERT INTO DEMO.LOG (ID, TABLENAME, UNIQUELABLE, INDATE) VALUES ('004c2c866cd611ebb81200e04c68268f', 'fund', '{''FCODE'': ''009210''}', TO_TIMESTAMP('2021-02-12 09:59:54.000000', 'YYYY-MM-DD HH24:MI:SS.FF6'));
INSERT INTO DEMO.LOG (ID, TABLENAME, UNIQUELABLE, INDATE) VALUES ('004df8036cd711ebb81200e04c68268f', 'manager', '{''FCODE'': ''008157''}', TO_TIMESTAMP('2021-02-12 10:07:04.000000', 'YYYY-MM-DD HH24:MI:SS.FF6'));
INSERT INTO DEMO.LOG (ID, TABLENAME, UNIQUELABLE, INDATE) VALUES ('00667c2b6cd711ebb81200e04c68268f', 'manager', '{''FCODE'': ''009451''}', TO_TIMESTAMP('2021-02-12 10:07:04.000000', 'YYYY-MM-DD HH24:MI:SS.FF6'));
INSERT INTO DEMO.LOG (ID, TABLENAME, UNIQUELABLE, INDATE) VALUES ('007764fc6cd611ebb81200e04c68268f', 'manager', '{''FCODE'': ''010678''}', TO_TIMESTAMP('2021-02-12 09:59:55.000000', 'YYYY-MM-DD HH24:MI:SS.FF6'));
INSERT INTO DEMO.LOG (ID, TABLENAME, UNIQUELABLE, INDATE) VALUES ('008039316cd711ebb81200e04c68268f', 'fund', '{''FCODE'': ''001983''}', TO_TIMESTAMP('2021-02-12 10:07:04.000000', 'YYYY-MM-DD HH24:MI:SS.FF6'));

-- 创建基金管理历史表
CREATE TABLE DEMO.fundmangerhis (
    MGRID       NUMBER                         NOT NULL,
    FCODE       VARCHAR2(200)                  NOT NULL,
    SHORTNAME   VARCHAR2(300)                  NOT NULL,
    FTYPE       VARCHAR2(10)                   NOT NULL,
    CONSTRAINT fundmangerhis_pk PRIMARY KEY (MGRID)
);

COMMENT ON TABLE DEMO.fundmangerhis IS '基金管理历史表';
INSERT INTO DEMO.FUNDMANGERHIS (MGRID, FCODE, SHORTNAME, FTYPE) VALUES (1, '000004', 'ZH可转债债券C', '债券型');
INSERT INTO DEMO.FUNDMANGERHIS (MGRID, FCODE, SHORTNAME, FTYPE) VALUES (2, '000006', 'XB利得量化成长混合A', '混合型');
INSERT INTO DEMO.FUNDMANGERHIS (MGRID, FCODE, SHORTNAME, FTYPE) VALUES (3, '000008', 'JSZZ500ETF联接A', '联接基');
INSERT INTO DEMO.FUNDMANGERHIS (MGRID, FCODE, SHORTNAME, FTYPE) VALUES (4, '000011', 'HX大盘精选混合', '混合型');
INSERT INTO DEMO.FUNDMANGERHIS (MGRID, FCODE, SHORTNAME, FTYPE) VALUES (5, '000014', 'HX聚利债券', '债券型');
INSERT INTO DEMO.FUNDMANGERHIS (MGRID, FCODE, SHORTNAME, FTYPE) VALUES (6, '000015', 'HX纯债债券A', '债券型');
INSERT INTO DEMO.FUNDMANGERHIS (MGRID, FCODE, SHORTNAME, FTYPE) VALUES (7, '000016', 'HX纯债债券C', '债券型');
INSERT INTO DEMO.FUNDMANGERHIS (MGRID, FCODE, SHORTNAME, FTYPE) VALUES (8, '000017', 'CT可持续混合', '混合型');
INSERT INTO DEMO.FUNDMANGERHIS (MGRID, FCODE, SHORTNAME, FTYPE) VALUES (9, '000020', 'JSCC品质投资混合', '混合型');
INSERT INTO DEMO.FUNDMANGERHIS (MGRID, FCODE, SHORTNAME, FTYPE) VALUES (10, '000021', 'HX优势增长混合', '混合型');

-- 创建基金经理信息表
CREATE TABLE DEMO.manager (
    MGRID   NUMBER                         NOT NULL,
    MGRNAME VARCHAR2(100)                  NOT NULL,
    MFTYPE  VARCHAR2(10),
    JJGS    VARCHAR2(100),
    JJGSID  VARCHAR2(10),
    CONSTRAINT manager_pk PRIMARY KEY (MGRID)
);

COMMENT ON TABLE DEMO.manager IS '基金经理信息表';

INSERT INTO DEMO.MANAGER (MGRID, MGRNAME, MFTYPE, JJGS) VALUES (1, '基金人001', '1', 'HX大盘');
INSERT INTO DEMO.MANAGER (MGRID, MGRNAME, MFTYPE, JJGS) VALUES (2, '基金人002', '2', 'HX纯债');
INSERT INTO DEMO.MANAGER (MGRID, MGRNAME, MFTYPE, JJGS) VALUES (3, '基金人003', '4', 'ZY证券');
INSERT INTO DEMO.MANAGER (MGRID, MGRNAME, MFTYPE, JJGS) VALUES (4, '基金人004', '2', 'WJ基金');
INSERT INTO DEMO.MANAGER (MGRID, MGRNAME, MFTYPE, JJGS) VALUES (5, '基金人005', '3', 'HTF基金');
INSERT INTO DEMO.MANAGER (MGRID, MGRNAME, MFTYPE, JJGS) VALUES (6, '基金人006', '3', 'HFT基金');
INSERT INTO DEMO.MANAGER (MGRID, MGRNAME, MFTYPE, JJGS) VALUES (7, '基金人007', '2', 'NF基金');
INSERT INTO DEMO.MANAGER (MGRID, MGRNAME, MFTYPE, JJGS) VALUES (8, '基金人008', '1', 'WK证券');
INSERT INTO DEMO.MANAGER (MGRID, MGRNAME, MFTYPE, JJGS) VALUES (9, '基金人009', '4', 'XX证券');
INSERT INTO DEMO.MANAGER (MGRID, MGRNAME, MFTYPE, JJGS) VALUES (10, '基金人010', '2', 'WF基金');

create view DEMO.fund_info as
select t1.FCODE,t1.SHORTNAME,t1.FULLNAME,t1.FTYPE,t3.MGRID,t3.MGRNAME,t3.MFTYPE,t3.JJGS
from DEMO.fund t1
inner join DEMO.fundmangerhis t2 on t1.FCODE = t2.FCODE
inner join DEMO.manager t3 on t2.MGRID = t3.MGRID;

-- 包规范
CREATE OR REPLACE PACKAGE DEMO.demo_package IS
    -- 存储过程的声明
    PROCEDURE your_procedure;

    -- 其他声明

END demo_package;
-- 包体
CREATE OR REPLACE PACKAGE BODY DEMO.demo_package IS
    -- 存储过程的实现
    PROCEDURE your_procedure IS
    BEGIN
        -- 存储过程的逻辑实现代码
         DECLARE
        update_query VARCHAR2(4000);
    BEGIN
        update_query := 'INSERT INTO demo.fund (FCODE, SHORTNAME, FULLNAME, FTYPE, ESTABDATE)
VALUES (''100004'', ''ZH可转债债券C'', ''ZH可转换债券债券型证券投资基金'', ''债券型'', TO_DATE(''2013-03-20'', ''YYYY-MM-DD''))';

        EXECUTE IMMEDIATE update_query;
    END;
    END your_procedure;

    -- 其他功能的实现
END demo_package;


CREATE OR REPLACE PROCEDURE DEMO.insert_data IS
BEGIN
    DECLARE
        update_query VARCHAR2(4000);
    BEGIN
        update_query := 'INSERT INTO demo.fund (FCODE, SHORTNAME, FULLNAME, FTYPE, ESTABDATE)
VALUES (''100004'', ''ZH可转债债券C'', ''ZH可转换债券债券型证券投资基金'', ''债券型'', TO_DATE(''2013-03-20'', ''YYYY-MM-DD''))';

        EXECUTE IMMEDIATE update_query;
    END;
END;