#!/usr/bin/env bash

# 默认配置
edgs_host="*************"
user_name="edgs"
edgs_deploy_base="/opt/edgs"


script_dir_path=$(dirname "$0")
cd "${script_dir_path}/../" || exit
base_dir_path=$(pwd)


# 解析参数
while getopts ":m:h:u:" opt
do
  case "$opt" in
    m)
      IFS="," read -ra mods <<< "$OPTARG"
    ;;
    u)
      user_name=$OPTARG
    ;;
    h)
      edgs_host="192.168.10.${OPTARG}"
    ;;
    *)
    ;;
  esac
done

deploy_all() {
  deploy_auth
  deploy_audit_agent
  deploy_extractor_agent
  deploy_bpm
  deploy_data_explorer
  deploy_data_quality
  deploy_gateway
  deploy_metadata
  deploy_metamodel
  deploy_scheduler
  deploy_standard
  deploy_wiki
  deploy_api_server
  deploy_sql_parser
  deploy_data_catalog
}

# 部署环境检测
check_requirements() {
  if ssh "${user_name}@${edgs_host}" [ ! -d "${edgs_deploy_base}/deploy/lib" ]; then
    ssh "${user_name}@${edgs_host}" "mkdir -p ${edgs_deploy_base}/deploy/lib"
  fi

  if ssh "${user_name}@${edgs_host}" [ ! -d "${edgs_deploy_base}/deploy/html" ]; then
    ssh "${user_name}@${edgs_host}" "mkdir -p ${edgs_deploy_base}/deploy/html"
  fi

  if ssh "${user_name}@${edgs_host}" [ ! -d "${edgs_deploy_base}/logs" ]; then
    ssh "${user_name}@${edgs_host}" "mkdir -p ${edgs_deploy_base}/logs"
  fi

  if ssh "${user_name}@${edgs_host}" [ ! -d "${edgs_deploy_base}/html" ]; then
    ssh "${user_name}@${edgs_host}" "mkdir -p ${edgs_deploy_base}/html"
  fi

  if ssh "${user_name}@${edgs_host}" [ ! -d "${edgs_deploy_base}/lib" ]; then
    ssh "${user_name}@${edgs_host}" "mkdir -p ${edgs_deploy_base}/lib"
  fi
}

check_requirements

# 删除旧的部署包
remove_old_deploy_jar() {
  if ssh "${user_name}@${edgs_host}" [ -f "${edgs_deploy_base}/deploy/lib/$1" ]; then
    ssh "${user_name}@${edgs_host}" "rm ${edgs_deploy_base}/deploy/lib/$1"
  fi
}

# 上传部署包
upload_deploy_jar() {
  scp "$1" "${user_name}@${edgs_host}:/opt/edgs/deploy/lib"
}

# 部署jar
deploy() {
  ssh "${user_name}@${edgs_host}" "sh /opt/edgs/bin/edgs.sh deploy-cluster $1"
}

deploy_data_catalog() {
  remove_old_deploy_jar "edgs-data-catalog.jar"
  upload_deploy_jar "${base_dir_path}/edgs-data-catalog/target/edgs-data-catalog.jar"
  deploy "data-catalog"
}

deploy_sql_parser() {
  remove_old_deploy_jar "edgs-sql-parser.jar"
  upload_deploy_jar "${base_dir_path}/edgs-sql-parser/target/edgs-sql-parser.jar"
  deploy "sql-parser"
}

deploy_auth() {
  remove_old_deploy_jar "edgs-auth.jar"
  upload_deploy_jar "${base_dir_path}/edgs-auth/target/edgs-auth.jar"
  deploy "auth"
}

deploy_admin_server() {
  remove_old_deploy_jar "edgs-admin-server.jar"
  upload_deploy_jar "${base_dir_path}/edgs-admin-server/target/edgs-admin-server.jar"
  deploy "admin-server"
}

deploy_audit_agent() {
  remove_old_deploy_jar "edgs-audit-agent.jar"
  upload_deploy_jar "${base_dir_path}/edgs-core/edgs-agent/edgs-audit-agent/target/edgs-audit-agent.jar"
  deploy "audit-agent"
}

deploy_extractor_agent() {
  remove_old_deploy_jar "edgs-extractor-agent.jar"
  upload_deploy_jar "${base_dir_path}/edgs-core/edgs-agent/edgs-extractor-agent/target/edgs-extractor-agent.jar"
  deploy "extractor-agent"
}

deploy_bpm() {
  remove_old_deploy_jar "edgs-bpm.jar"
  upload_deploy_jar "${base_dir_path}/edgs-bpm/target/edgs-bpm.jar"
  deploy "bpm"
}

deploy_data_explorer() {
  remove_old_deploy_jar "edgs-data-explorer.jar"
  upload_deploy_jar "${base_dir_path}/edgs-data-explorer/target/edgs-data-explorer.jar"
  deploy "data-explorer"
}

deploy_data_quality() {
  remove_old_deploy_jar "edgs-data-quality.jar"
  upload_deploy_jar "${base_dir_path}/edgs-data-quality/target/edgs-data-quality.jar"
  deploy "data-quality"
}

deploy_gateway() {
  remove_old_deploy_jar "edgs-gateway.jar"
  upload_deploy_jar "${base_dir_path}/edgs-gateway/target/edgs-gateway.jar"
  deploy "gateway"
}

deploy_metadata() {
  remove_old_deploy_jar "edgs-metadata.jar"
  upload_deploy_jar "${base_dir_path}/edgs-metadata/target/edgs-metadata.jar"
  deploy "metadata"
}

deploy_metamodel() {
  remove_old_deploy_jar "edgs-metamodel.jar"
  upload_deploy_jar "${base_dir_path}/edgs-metamodel/target/edgs-metamodel.jar"
  deploy "metamodel"
}

deploy_scheduler() {
  remove_old_deploy_jar "edgs-scheduler.jar"
  upload_deploy_jar "${base_dir_path}/edgs-scheduler/target/edgs-scheduler.jar"
  deploy "scheduler"
}

deploy_standard() {
  remove_old_deploy_jar "edgs-standard.jar"
  upload_deploy_jar "${base_dir_path}/edgs-standard/target/edgs-standard.jar"
  deploy "standard"
}

deploy_wiki() {
  remove_old_deploy_jar "edgs-wiki.jar"
  upload_deploy_jar "${base_dir_path}/edgs-wiki/target/edgs-wiki.jar"
  deploy "wiki"
}

deploy_api_server() {
  remove_old_deploy_jar "edgs-api-server.jar"
  upload_deploy_jar "${base_dir_path}/edgs-api-server/target/edgs-api-server.jar"
  deploy "api-server"
}

deploy_extractor_sql() {
  ssh ${user_name}@${edgs_host} "rm -rf /opt/edgs/extractor/*"
  scp -r "${base_dir_path}/edgs-core/edgs-extractor/extractor"/* ${user_name}@${edgs_host}:/opt/edgs/extractor
}

deploy_driver() {
  ssh ${user_name}@${edgs_host} "rm -rf /opt/edgs/driver/*"
  scp -r "${base_dir_path}/edgs-core/edgs-agent/driver"/* ${user_name}@${edgs_host}:/opt/edgs/driver
}

deploy_prompt() {
  ssh ${user_name}@${edgs_host} "rm -rf /opt/edgs/ai/prompts/*"
  scp -r "${base_dir_path}/edgs-data-catalog/src/main/resources/prompts"/* ${user_name}@${edgs_host}:/opt/edgs/ai/prompts
}

deploy_local_driver() {
  cd "${base_dir_path}/../" || exit
  rm -rf driver/*
  cp -r "${base_dir_path}/edgs-core/edgs-agent/driver"/* driver
}

deploy_local_sql() {
  cd "${base_dir_path}/../" || exit
  if [ ! -d extractor ]; then
      mkdir extractor
  fi
  rm -rf extractor/*
  cp -r "${base_dir_path}/edgs-core/edgs-extractor/extractor"/* extractor
}

if [ "${#mods[@]}" -eq 0 ]; then
  echo "all"
  deploy_all
else
  for mod in "${mods[@]}" ; do
      case "$mod" in
      auth)
        deploy_auth
        ;;
      admin-server)
        deploy_admin_server
        ;;
      aduit-agent)
        deploy_audit_agent
        ;;
      extractor-agent)
        deploy_extractor_agent
        ;;
      bpm)
        deploy_bpm
        ;;
      data-explorer)
        deploy_data_explorer
        ;;
      data-quality)
        deploy_data_quality
        ;;
      gateway)
        deploy_gateway
        ;;
      metadata)
        deploy_metadata
        ;;
      metamodel)
        deploy_metamodel
        ;;
      scheduler)
        deploy_scheduler
        ;;
      standard)
        deploy_standard
        ;;
      wiki)
        deploy_wiki
        ;;
      api-server)
        deploy_api_server
        ;;
      sql-parser)
        deploy_sql_parser
        ;;
      sql)
        deploy_extractor_sql
        ;;
      driver)
        deploy_driver
        ;;
      prompt)
        deploy_prompt
        ;;
      local-sql)
        deploy_local_sql
        ;;
      local-driver)
        deploy_local_driver
        ;;
      data-catalog)
        deploy_data_catalog
        ;;
      *)
        echo "无效的模块名: ${mod}"
        ;;
      esac
  done
fi

exit 0























