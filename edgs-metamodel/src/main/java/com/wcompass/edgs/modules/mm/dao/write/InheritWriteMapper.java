package com.wcompass.edgs.modules.mm.dao.write;

import com.wcompass.edgs.modules.mm.entity.Inherit;
import org.apache.ibatis.annotations.Param;

public interface InheritWriteMapper {
    int deleteByPrimaryKey(@Param("classifierId") String classifierId, @Param("ownerClassifierId") String ownerClassifierId);

    int insert(Inherit record);

    int insertSelective(Inherit record);

    int updateByPrimaryKeySelective(Inherit record);

    int updateByPrimaryKey(Inherit record);

    void deleteByClassifierId(@Param("classifierId") String classifierId);
}