package com.wcompass.edgs.modules.mm.model;

import com.wcompass.edgs.modules.mm.entity.RelationDep;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date Created on 2021/2/6
 */
@Data
@Schema(description = "依赖模型")
public class DepRelationVO {

    @Schema(description = "关系ID")
    private String relId;

    /**
     * 关系名称
     */
    @Schema(description = "关系名")
    @NotBlank(message = "关系名称不能为空")
    private String relName;

    /**
     * 源分类器ID
     */
    @Schema(description = "依赖端类代码")
    @NotBlank(message = "依赖端类代码不能为空")
    private String fromClassifierId;

    @Schema(description = "依赖端类名称")
    private String fromClassifierName;

    /**
     * 源角色ID
     */
    @Schema(description = "依赖端角色")
    private String fromRoleId;

    /**
     * 目的分类器ID
     */
    @Schema(description = "被依赖端代码")
    @NotBlank(message = "被依赖端类代码不能为空")
    private String toClassifierId;

    @Schema(description = "被依赖端名称")
    private String toClassifierName;

    /**
     * 目的角色ID
     */
    @Schema(description = "被依赖端角色")
    private String toRoleId;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    public RelationDep to() {
        RelationDep relationDep = new RelationDep();
        relationDep.setRelId(getRelId());
        relationDep.setRelName(getRelName());
        relationDep.setFromClassifierId(getFromClassifierId());
        relationDep.setFromRoleId(getFromRoleId());
        relationDep.setToClassifierId(getToClassifierId());
        relationDep.setToRoleId(getToRoleId());
        relationDep.setDescription(getDescription());
        return relationDep;
    }
}
