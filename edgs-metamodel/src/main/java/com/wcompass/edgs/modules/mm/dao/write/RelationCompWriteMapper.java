package com.wcompass.edgs.modules.mm.dao.write;

import com.wcompass.edgs.modules.mm.entity.RelationComp;
import org.apache.ibatis.annotations.Param;

public interface RelationCompWriteMapper {

    void insert(RelationComp relationComp);

    void deleteByPrimaryKey(@Param("relId") String relId);

    void updateByPrimaryKey(RelationComp relationComp);

    void deleteDepByClassifierId(@Param("classifierId") String classifierId);
}