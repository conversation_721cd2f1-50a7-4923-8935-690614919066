<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wcompass.edgs.modules.mm.dao.write.RelationDepWriteMapper">
  <resultMap id="BaseResultMap" type="com.wcompass.edgs.modules.mm.entity.RelationDep">
    <id column="REL_ID" jdbcType="VARCHAR" property="relId" />
    <result column="REL_NAME" jdbcType="VARCHAR" property="relName" />
    <result column="FROM_CLASSIFIER_ID" jdbcType="VARCHAR" property="fromClassifierId" />
    <result column="FROM_ROLE_ID" jdbcType="VARCHAR" property="fromRoleId" />
    <result column="TO_CLASSIFIER_ID" jdbcType="VARCHAR" property="toClassifierId" />
    <result column="TO_ROLE_ID" jdbcType="VARCHAR" property="toRoleId" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
  </resultMap>
  <sql id="Base_Column_List">
    REL_ID, REL_NAME, FROM_CLASSIFIER_ID, FROM_ROLE_ID, TO_CLASSIFIER_ID, TO_ROLE_ID, 
    DESCRIPTION
  </sql>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from t00_relation_dep
    where REL_ID = #{relId,jdbcType=VARCHAR}
  </delete>
    <delete id="deleteCompByClassifierId">
      delete from t00_relation_dep
      where FROM_CLASSIFIER_ID = #{classifierId} or TO_CLASSIFIER_ID = #{classifierId}
    </delete>
    <insert id="insert" parameterType="com.wcompass.edgs.modules.mm.entity.RelationDep">
    insert into t00_relation_dep (REL_ID, REL_NAME, FROM_CLASSIFIER_ID,
      FROM_ROLE_ID, TO_CLASSIFIER_ID, TO_ROLE_ID, 
      DESCRIPTION)
    values (#{relId,jdbcType=VARCHAR}, #{relName,jdbcType=VARCHAR}, #{fromClassifierId,jdbcType=VARCHAR}, 
      #{fromRoleId,jdbcType=VARCHAR}, #{toClassifierId,jdbcType=VARCHAR}, #{toRoleId,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.wcompass.edgs.modules.mm.entity.RelationDep">
    insert into t00_relation_dep
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="relId != null">
        REL_ID,
      </if>
      <if test="relName != null">
        REL_NAME,
      </if>
      <if test="fromClassifierId != null">
        FROM_CLASSIFIER_ID,
      </if>
      <if test="fromRoleId != null">
        FROM_ROLE_ID,
      </if>
      <if test="toClassifierId != null">
        TO_CLASSIFIER_ID,
      </if>
      <if test="toRoleId != null">
        TO_ROLE_ID,
      </if>
      <if test="description != null">
        DESCRIPTION,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="relId != null">
        #{relId,jdbcType=VARCHAR},
      </if>
      <if test="relName != null">
        #{relName,jdbcType=VARCHAR},
      </if>
      <if test="fromClassifierId != null">
        #{fromClassifierId,jdbcType=VARCHAR},
      </if>
      <if test="fromRoleId != null">
        #{fromRoleId,jdbcType=VARCHAR},
      </if>
      <if test="toClassifierId != null">
        #{toClassifierId,jdbcType=VARCHAR},
      </if>
      <if test="toRoleId != null">
        #{toRoleId,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wcompass.edgs.modules.mm.entity.RelationDep">
    update t00_relation_dep
    <set>
      <if test="relName != null">
        REL_NAME = #{relName,jdbcType=VARCHAR},
      </if>
      <if test="fromClassifierId != null">
        FROM_CLASSIFIER_ID = #{fromClassifierId,jdbcType=VARCHAR},
      </if>
      <if test="fromRoleId != null">
        FROM_ROLE_ID = #{fromRoleId,jdbcType=VARCHAR},
      </if>
      <if test="toClassifierId != null">
        TO_CLASSIFIER_ID = #{toClassifierId,jdbcType=VARCHAR},
      </if>
      <if test="toRoleId != null">
        TO_ROLE_ID = #{toRoleId,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        DESCRIPTION = #{description,jdbcType=VARCHAR},
      </if>
    </set>
    where REL_ID = #{relId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wcompass.edgs.modules.mm.entity.RelationDep">
    update t00_relation_dep
    set REL_NAME = #{relName,jdbcType=VARCHAR},
      FROM_CLASSIFIER_ID = #{fromClassifierId,jdbcType=VARCHAR},
      FROM_ROLE_ID = #{fromRoleId,jdbcType=VARCHAR},
      TO_CLASSIFIER_ID = #{toClassifierId,jdbcType=VARCHAR},
      TO_ROLE_ID = #{toRoleId,jdbcType=VARCHAR},
      DESCRIPTION = #{description,jdbcType=VARCHAR}
    where REL_ID = #{relId,jdbcType=VARCHAR}
  </update>
</mapper>