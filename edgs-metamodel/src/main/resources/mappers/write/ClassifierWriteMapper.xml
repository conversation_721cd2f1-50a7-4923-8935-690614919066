<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wcompass.edgs.modules.mm.dao.write.ClassifierWriteMapper">
  <sql id="Base_Column_List">
    CLASSIFIER_ID, CLASSIFIER_NAME, IS_ABSTRACT, SHOW_CHILD, DIS_ICON, OWNER_PID, DESCRIPTION
  </sql>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from t00_classifier
    where CLASSIFIER_ID = #{classifierId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.wcompass.edgs.modules.mm.entity.Classifier">
    insert into t00_classifier (CLASSIFIER_ID, CLASSIFIER_NAME, IS_ABSTRACT, SHOW_CHILD,
      DIS_ICON, OWNER_PID,
      DESCRIPTION, INSTANCE_TABLE, ENABLE_CREATE_VIEW, supported_db_types)
    values (#{classifierId,jdbcType=VARCHAR}, #{classifierName,jdbcType=VARCHAR}, #{isAbstract,jdbcType=CHAR}, #{showChild},
      #{disIcon,jdbcType=VARCHAR}, #{ownerPid,jdbcType=VARCHAR},
      #{description,jdbcType=VARCHAR}, #{instanceTable}, #{enableCreateView}, #{supportedDbTypes})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.wcompass.edgs.modules.mm.entity.Classifier">
    update t00_classifier
    set CLASSIFIER_NAME = #{classifierName,jdbcType=VARCHAR},
      IS_ABSTRACT = #{isAbstract,jdbcType=CHAR},
      SHOW_CHILD = #{showChild},
      DIS_ICON = #{disIcon,jdbcType=VARCHAR},
      OWNER_PID = #{ownerPid,jdbcType=VARCHAR},
      DESCRIPTION = #{description,jdbcType=VARCHAR},
      INSTANCE_TABLE = #{instanceTable,jdbcType=VARCHAR},
      ENABLE_CREATE_VIEW = #{enableCreateView,jdbcType=VARCHAR},
      supported_db_types = #{supportedDbTypes}
    where CLASSIFIER_ID = #{classifierId,jdbcType=VARCHAR}
  </update>
</mapper>
