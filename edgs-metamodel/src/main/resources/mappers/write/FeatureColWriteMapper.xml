<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wcompass.edgs.modules.mm.dao.write.FeatureColWriteMapper">
  <resultMap id="BaseResultMap" type="com.wcompass.edgs.modules.mm.entity.FeatureCol">
    <id column="ATT_ID" jdbcType="VARCHAR" property="attId" />
    <id column="CLASSIFIER_ID" jdbcType="VARCHAR" property="classifierId" />
    <result column="ATT_STORE" jdbcType="VARCHAR" property="attStore" />
  </resultMap>
  <sql id="Base_Column_List">
    ATT_ID, CLASSIFIER_ID, ATT_STORE
  </sql>
  <insert id="insert" parameterType="com.wcompass.edgs.modules.mm.entity.FeatureCol">
    insert into t00_feature_col (ATT_ID, CLASSIFIER_ID, ATT_STORE) values (#{attId}, #{classifierId}, #{attStore})
  </insert>
  <delete id="deleteByAttId">
    delete from t00_feature_col t1 where t1.ATT_ID = #{attId}
  </delete>
  <delete id="deleteByClassifierIdAndAttId">
    delete from t00_feature_col t1 where t1.ATT_ID = #{attId} and t1.CLASSIFIER_ID = #{classifierId}
  </delete>
  <delete id="deleteAssociateFeatureByClassifierId">
    delete from t00_feature_col t1
    where exists (
      select 0
      from t00_feature t2
      where t2.classifier_id = #{classifierId}
      and t1.att_id = t2.att_id
    )
  </delete>
  <select id="existInheritFeature" resultType="java.lang.Boolean">
    select case when count(0) > 0 then 1 else 0 end
    from t00_feature_col t1 where t1.CLASSIFIER_ID = #{classifierId} and t1.ATT_ID = #{attId}
  </select>
</mapper>
