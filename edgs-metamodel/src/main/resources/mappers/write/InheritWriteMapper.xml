<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wcompass.edgs.modules.mm.dao.write.InheritWriteMapper">
  <resultMap id="BaseResultMap" type="com.wcompass.edgs.modules.mm.entity.Inherit">
    <id column="CLASSIFIER_ID" jdbcType="VARCHAR" property="classifierId" />
    <id column="OWNER_CLASSIFIER_ID" jdbcType="VARCHAR" property="ownerClassifierId" />
    <result column="IS_PARENT" jdbcType="CHAR" property="isParent" />
    <result column="PATH_NUM" jdbcType="TINYINT" property="pathNum" />
  </resultMap>
  <sql id="Base_Column_List">
    CLASSIFIER_ID, OWNER_CLASSIFIER_ID, IS_PARENT, PATH_NUM
  </sql>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from t00_inherit
    where CLASSIFIER_ID = #{classifierId,jdbcType=VARCHAR}
      and OWNER_CLASSIFIER_ID = #{ownerClassifierId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByClassifierId">
    delete from t00_inherit
    where CLASSIFIER_ID = #{classifierId,jdbcType=VARCHAR}
      or OWNER_CLASSIFIER_ID = #{classifierId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="com.wcompass.edgs.modules.mm.entity.Inherit">
    insert into t00_inherit (CLASSIFIER_ID, OWNER_CLASSIFIER_ID,
      IS_PARENT, PATH_NUM)
    values (#{classifierId,jdbcType=VARCHAR}, #{ownerClassifierId,jdbcType=VARCHAR}, 
      #{isParent,jdbcType=CHAR}, #{pathNum,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.wcompass.edgs.modules.mm.entity.Inherit">
    insert into t00_inherit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="classifierId != null">
        CLASSIFIER_ID,
      </if>
      <if test="ownerClassifierId != null">
        OWNER_CLASSIFIER_ID,
      </if>
      <if test="isParent != null">
        IS_PARENT,
      </if>
      <if test="pathNum != null">
        PATH_NUM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="classifierId != null">
        #{classifierId,jdbcType=VARCHAR},
      </if>
      <if test="ownerClassifierId != null">
        #{ownerClassifierId,jdbcType=VARCHAR},
      </if>
      <if test="isParent != null">
        #{isParent,jdbcType=CHAR},
      </if>
      <if test="pathNum != null">
        #{pathNum,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wcompass.edgs.modules.mm.entity.Inherit">
    update t00_inherit
    <set>
      <if test="isParent != null">
        IS_PARENT = #{isParent,jdbcType=CHAR},
      </if>
      <if test="pathNum != null">
        PATH_NUM = #{pathNum,jdbcType=TINYINT},
      </if>
    </set>
    where CLASSIFIER_ID = #{classifierId,jdbcType=VARCHAR}
      and OWNER_CLASSIFIER_ID = #{ownerClassifierId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wcompass.edgs.modules.mm.entity.Inherit">
    update t00_inherit
    set IS_PARENT = #{isParent,jdbcType=CHAR},
      PATH_NUM = #{pathNum,jdbcType=TINYINT}
    where CLASSIFIER_ID = #{classifierId,jdbcType=VARCHAR}
      and OWNER_CLASSIFIER_ID = #{ownerClassifierId,jdbcType=VARCHAR}
  </update>
</mapper>