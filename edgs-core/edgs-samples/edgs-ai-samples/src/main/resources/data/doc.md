一、虚拟机硬盘压缩需求
刚好最近有一个Esxi虚拟机Windows 10系统，之前使用的是厚置备，一开始分配了350G（精简置备也可缩小，下面也有对应方法），因为系统实际占用空间大约80G左右，现在想把它复制到其它数据存储器，感觉占用的空间非常大，想先把虚拟机硬盘缩小，而Esxi操作界面上，磁盘是可以扩容，但不能直接缩小，所以需要使用Esxi的 vmkfstools 来进行转换或压缩。

在压缩回收虚拟磁盘空间之前，我们先来掌握一些基础知识。。

二、介绍一下三种虚拟磁盘类型
1. 厚置备延迟置零（zeroed thick） #默认选项
在创建时为虚拟磁盘分配所需空间（真实地占用物理存储空间），当虚拟机在有写入的时，再按量将其置零。

2 厚置备置零（eager zeroed thick）
在创建时为虚拟磁盘分配所需空间（真实地占用物理存储空间），创建过程中作置零操作，所以创建磁盘时间长。

3 精简置备（thin)
无论磁盘分配多大，实际用多少就占用物理存储多少，当虚拟机真正写入数据时，才进行分配空间及置零的操作，需等待分配空间和置备完成后才能进行操作，对于IO频繁造成性能会有所下降，但它的好处是节省了存储空间，不过使用这种方式，虚拟磁盘vmdk文件随着置备量会只增不减。

精简置备只增不减：精简置备vmdk文件容量是随着写入置备增加，如虚拟机曾经使用磁盘达500GB，虽然你删除300GB文件后，甚至格式化磁盘，删除磁盘分区，但vmdk文件依然是500GB。所以需要加收空间

三、回收空间准备
1、首先要准备足够大物理存储空间，因为转换时，需要同时保存旧文件及新文件及一些临时空间
2、如果vmdk文件非常大，转换及迁移需要时间也会非常长，我转换了350G的文件 大约40多分钟，如果是笔记本，需要接上外接电源



四、回收空间思路及详细步骤
1、先将虚拟机系统中不需要的文件删除 清空，甚至可以对虚拟机的磁盘进行压缩
小辣椒高效Office：Windows系统压缩卷时提示磁盘上没有足够的空间完成此操作或压缩空间远小于实际剩余空间解决办法
12 赞同 · 2 评论文章
2、再使用sdelete工具进行填0（置零操作）
小辣椒高效Office：Windows下如何安全的永久不可恢复地删除文件-SDelete工具的使用
6 赞同 · 0 评论文章
3、最后再使用ESXI工具vmkfstools移除置零的块，实现收缩VMDK空间：
未处理之前，虚拟机磁盘文件是350G左右


4、完整操作步骤：

1)先将虚拟机系统中的不需要的临时文件，安装文件，系统更新文件或不需要的档案全部删除，

2)再对磁盘进行碎片整理

3)再进行磁盘压缩

4)再使用SDelete工具将未使用空间置零

5)将虚拟机关机

6)打开esxi 的ssh 及放行端口（处理完成后要关闭它，以保证安全）

7)使用Win10 cmd命令行，直接 输入 ssh root@***********(你的esxi电脑的IP地址

8)如果是精简置备，可以使用 vmkfstools -K win10.vmdk 压缩未使用的空间

如果是厚置备，使用上述命令则会提示失败：

vmkfstools Hole Punching: 0% done.Could not punch hole in disk Function not implemente

则需要使用 将厚置备转为精简置备的命令

vmkfstools -i win10.vmdk -d thin win10new.vmdk

Vmkfstools命令格式

# vmkfstools -i<source-disk-name.vmdk>-d {thin|thick}<destination-disk-name.vmdk>

参数解释:

-i <source-disk-name.vmdk>参数:原vmdk磁盘名

-d {thin|thick}<destination-disk-name.vmdk> :目标磁盘的格式,thin或thick; <destination-disk-name.vmdk>为要生成的目标vmdk磁盘名;

转换后，需要将名称 更改为 对应到旧的名称


转换完成后

vmkfstools -i win10.vmdk -d thin win10new.vm

dk

Destination disk format: VMFS thin-provisioned

Cloning disk 'win10.vmdk'...

Clone: 100% done.

做完这一步，如果使用ls -l 看到转换后的vmdk的文件大小并没有改变，而实际上虽然文件大小没有改变，但占用空间大大缩小了，可以使用 du -ah * 命令来查看


9) 将旧的win10.vmdk win10-flat.vmdk 文件 改名

mv win10.vmdk win10bak.vmdk

mv win10-flat.vmdk win10-flatbak.vmdk

再把转换后的新文件改名：

mv win10new.vmdk win10.vmdk

mv win10new-flat.vmdk win10-flat.vmdk

10) 再修改win10.vmdk 文件

vi win10.vmdk

搜索filename="win10.vmdk" 再将filename="win10.vmdk" 这个路径检查一下，是否对应你转换后的新的文件名





11) 再把Extent description 下的

RW 后面的数据更改

如我想把原来的350G 改小为 100G，就是100 X 1024 X 1024 X (1024/512) = 209715200

保存即可

再 :wq 保存
这时看到虚拟机硬盘还是350G


12) 再取消注册些虚拟机，再重新注册虚拟机即可


再新建或注册虚拟机，选择现有虚拟机，找到对应目录下的win.vmx




再检查虚拟机硬盘，变成100G了



13) 最后再检查 win10.vmx文件
vi win10.vmx
看看里面是否对应的是 win10.vmdk ，如果路径是对的。就可开启虚拟机电源了

14) 开启虚拟机电源, 进入虚拟机系统查看 计算机管理 磁盘管理


发现 C盘 本身分配是是81G，100G-81G=18.36G 还有18.36G 未分配，是正确的

当然也可将win10.vmdk 中的 rw后面数字 真正缩小为81G 多。（但一定要大于C盘实际分配的大小 ）

15) 使用一段时间后，如果确定没有问题后，可以删除 旧的vmdk 2个文件。

4、如果是Linux虚拟机，则空间置零方法稍有不同：
1）将虚拟机内所有未使用的空间归零：

dd if=/dev/zero bs=1048576 of=/zero ; sync ; rm /zero

2）对其他安装点，交换分区等执行相同的操作。

3）关闭虚拟机。

4）SSH到ESXi，然后发出以下命令：

vmkfstools -K /vmfs/volumes/volumename/vmname/vmname.vmdk

这将需要一些时间... vmkfstools在VMDK内部“打孔”，例如，释放所有填充有零的块，从而有效地缩小了VMDK。



五、使用Vmware vCenter Converter 工具进行万能转换（转换及压缩磁盘）
如果发现上面方法不方便或太复杂或转换后的磁盘文件 win10-flat.vmdk 显示的文件大小还是不变，可以使用

Vmware vCenter Converter 工具（从官网下载 安装 6.2.0版本）选择独立安装


选择源虚拟机后点下一步会等待比较久的时间，工具需要从源机器提取相应的信息。一般要3-10多分钟，根据虚拟机的磁盘大小而有所不同。

选择目标还是相同的Esxi 服务器，但可以选择不同的数据存储，登录服务器时会提示ssl验证，选择igore忽略即可。



在option中 记得设置 为 thin 精简置备 及新的数据存储


如果要改变目标磁盘的大小 ，可以这样来设置


可保留原大小 ，最小，手工输入按GB，手工输入按MB。我选择最小尺寸即可。但要留意目标数据存储有足够容纳的空间。

有可能会提示 目标不支持 3个socket, 要修改目标 为1个socket ，才能继续转换

然后就是真正的转换过程，大约需要 一个多小时，耐心等转换即可


但发现实际转换时间远超过预估时间，结果差不多花了3个小时才完成。一般是20G左右转换需要一个小时，100G 可能需要2-3小时。具体要看你电脑的性能而定。


转换后，文件缩小到100G左右了


所以，最终还是发现使用 Vmware vCenter Converter 工具比较靠谱，缺点就是需要额外的数据存储空间以及等等的时间。Convert适用于P2V、V2V （物理机转虚拟机 虚拟机转虚拟机 均非常方便，缺点就是转换时间比较久），和是否有vCenter无关的 。

六、使用VMware vCenter Server管理层迁移（Migrate）或者复制VM到另外一个datastore
VMware ESXi、VMware vCenter Server 和 vSphere Client，它们分别是 vSphere 的虚拟化层、管理层和接口层。作为接口层的vSphere Client客户端并不提供克隆虚拟机的功能，需要安装VMware vCenter Server管理层，ESXi才有这一功能。（评估版是不带这个功能的）

前面置零的步骤一样，只是使用VMware vCenter Server管理层迁移（Migrate）或者复制VM到另外一个datastore。 VMDK 被移动之后，它的size就变小了，自动去除多余的空间。

七、使用ghost或其它方法进行硬盘克隆，就是使用虚拟机克隆里面系统的方法
Ghost ABR SSR等工具均可实现备份还原，将来有时间将来再折腾一下。

AcronisBackupAdvanced 我自己使用还挺顺手的。支持多核及速度比较快。

八、网友们其它方法
有些网友说使用 vmkfstools -K 压缩后 flat文件显示大小 不缩小（实际占用缩小了）
可用 通过vmkfstools先转换成厚置备模式，然后在转换精简置备模式即可
或 先迁移到另外一个盘符，模式更改成厚置备模式 ，然后再迁移回来，模式改为精简置备模式

命令格式：

精简置备转换至厚置备置零：

vmkfstools --inflatedisk /vmfs/volumes/DatastoreName/VMName/VMName.vmdk

厚置备延迟置零转换至精简备置：

vmkfstools --thin /vmfs/volumes/DatastoreName/VMName/VMName.vmdk

还有这个网友的方法也不错：VMware虚拟机，从厚置备改成精简置备，并减小硬盘的实际占用空间 - saszhuqing - 博客园

关于使用 vmkfstools -K 压缩精简置备后 du -ah 显示缩小，而ls -l 文件大小不变，是否可同时调整 vmdk文件中的 RW 与 ddb.geometry.cylinders 2个值后来来尝试下
# Extent description
RW 38677202 VMFS "VM-Test-A-flat.vmdk"

# The Disk Data Base

#DDB

ddb.adapterType = "lsilogic"

ddb.geometry.cylinders = "48912"

九、相关知识点
以下内容摘自 csdn博主 endzhi 的文章 ：原文地址

虚拟磁盘类型转换 vmkfstools实战

移除置零的块，转换至精简备置：

命令 vmkfstools -K A.vmdk

使用 vmkfstools -K 将精简、zeroedthick 或 eagerzeroedthick 虚拟磁盘转换成移除了置零块的精简磁盘，解除分配所有置零的块，并仅保留含有效数据的块，得到虚拟磁盘为精简格式，也达到了收缩VMDK容量作用。

精简置备转换至厚置备置零：

命令 vmkfstools --inflatedisk A.vmdk

--inflatedisk 可将磁盘未置零部分都置零。vmdk文件容量将达到分配空间最大值。

厚置备延迟置零转换至厚置备置零：

命令 vmkfstools --eagerzero A.vmdk

虚拟磁盘改名

命令 vmkfstools -E A.vmdk B.vmdk （别对受快照磁盘操作）

删除虚拟磁盘

命令 vmkfstools -U C.vmdk

创建虚拟磁盘

命令 vmkfstools -c 8G E.vmdk （默认厚置备延迟置零格式）

命令 vmkfstools -c 8G -d thin Athin.vmdk （容量单位mkg不分大少写；-d 可选zeroedthick|eagerzeroedthick|thin）

初始化虚拟磁盘

命令 vmkfstools -w A.vmdk

（写入零数据以将其虚拟机磁盘清空，达到初始化磁盘，磁盘分区也会被删除，完毕后，磁盘类型为厚置备置零)

扩展虚拟磁盘vmdk

命令 vmkfstools -X 88g D.vmdk （切勿对受快照的磁盘操作，会直接损坏磁盘） 只能往大调。

克隆vmdk磁盘

命令 Vmkfstools -i A.vmdk -d thin A_new.vmdk

（注意-d thin关键参数,如原磁盘为thick不加就克隆出原thick磁盘模式，当原是thin不加就克隆出zeroedthick）

成功的克隆出A_new.vmdk 和 A_new-flat.vmdk两个文件，是直接可用的磁盘了。

克隆方式从厚置备转换为精简置备:

如需要使用A_new.vmdk 这个thin盘代替原来就以下操作:

# cat VMName-new.vmdk

可以看到 # Extent description 中是指向A_new-flat.vmdk

先将原两文件A.vmdk和A-flat.vmdk 备份到别的地方，

# mkdir oldvmdk

# mv A.vmdk ./oldvmdk

# mv A-flat.vmdk ./oldvmdk

将这新的A_new.vmdk 和 A_new-flat.vmdk两个文件改为旧的名字，并修改Extent description内容，开机测试。

注意:

转换了磁盘类型或扩展了磁盘容量，清单并不会自动刷新，需将该磁盘暂时从清单中移除，再次添加。

在虚拟机系统除了dd 、SDelete还有哪里操作可以将vmdk置零

1. 分区助手的“擦除磁盘/分区/数据”功能也是填0操作（只能对整个分区操作，并该分区是已经删除分区的）。

2. DiskGenius “清除扇区数据”除了默认填0操作，还可自定义，如填FF(十六进制），vmkfstools -K清理空间就无效了，只好再次填0再清。

3. window磁盘管理中，先将磁盘转换为动态磁盘，并组建软riad。

ESXI 在SSH中磁盘操作的一些结果

移动mv

当磁盘是Thin类型，使用mv 在同一个存储移动位置，不会改变磁盘类型。

# vmkfstools -c 1G -d thin 1thin.vmdk

# mv 1thin.vmdk /vmfs/volumes/480GB-SSD/

# mv 1thin-flat.vmdk /vmfs/volumes/480GB-SSD/

# du -ah /vmfs/volumes/480GB-SSD/1thin-flat.vmdk

0 /vmfs/volumes/480GB-SSD/1thin-flat.vmdk

但mv移动至其他存储会改变为厚置备置零。

mv /vmfs/volumes/480GB-SSD/1thin-flat.vmdk /vmfs/volumes/2T-HDD/

mv /vmfs/volumes/480GB-SSD/1thin.vmdk /vmfs/volumes/2T-HDD/

du -ah /vmfs/volumes/2T-HDD/1thin-flat.vmdk

1.0G /vmfs/volumes/2T-HDD/1thin-flat.vmdk

复制cp

vmkfstools -c 1G -d thin 1thin.vmdk

cp 1thin.vmdk ../

cp 1thin-flat.vmdk ../

du -ah ../1thin-flat.vmdk

1G ../1thin-flat.vmdk

得出: cp 是会将thin转换为 厚置备置零

ESXI 在数据存储浏览器中磁盘操作的一些结果

复制/剪切/移至 thin的vmdk，同/不同一个存储都不会改变磁盘类型。

从数据存储浏览器下载thin的分配了1GB的vmdk，但需要真实的下载1GB到本地.

十、其它技巧
1、 vmkfstools -K并不能对快照vmdk文件如 Win10-000002.vmdk清零操作，需考虑将快照移除再操作。
2、 迁移压缩其它方法： vmkfstools -i克隆、OVF方式、直接复制vmdk文件、直接“移至”、 Trueimage partion 分区助手，DiskGenius克隆分区， host等。

3、群晖里的删除文件，空间也不会释放，也需要使用存储管理器里的 碎片处理 来释放空间