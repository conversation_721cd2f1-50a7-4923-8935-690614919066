package com.wcompass.edgs.ai.openai.api;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wcompass.edgs.utils.JsonUtil;
import org.springframework.ai.openai.api.common.OpenAiApiException;
import org.springframework.util.Assert;
import org.springframework.web.client.RestClient;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class OpenAiApi extends org.springframework.ai.openai.api.OpenAiApi {

	public static final String CHAT_DOC_API = "chatDocApi";

	public static final String TEXT_TO_SQL_API = "text2sqlApi";

	public static final String EMBEDDING_API = "embeddingApi";

    private final String baseUrl;

	private final String apiKey;

	public OpenAiApi(String baseUrl, String openAiToken) {
        super(baseUrl, openAiToken);
		this.baseUrl = baseUrl;
		this.apiKey = openAiToken;
    }

    public OpenAiApi(String baseUrl, String openAiToken, RestClient.Builder builder) {
        super(baseUrl, openAiToken, builder);
		this.baseUrl = baseUrl;
		this.apiKey = openAiToken;
    }

    public TokenCheckResponse tokenCheck(TokenCheckRequest tokenCheckRequest) {
		Assert.notNull(tokenCheckRequest, "The request body can not be null.");
		String url = UriComponentsBuilder.fromHttpUrl(this.baseUrl)
				.path("/api/v1/token_check")
				.build()
				.toString();

		HttpRequest httpRequest = HttpUtil.createPost(url)
				.bearerAuth(this.apiKey)
				.body(JsonUtil.toJsonString(
						Map.of("prompts", List.of(tokenCheckRequest))
				));

		try(HttpResponse httpResponse = httpRequest.execute()){
			TokenCheckResponseList tokenCheckResponses = handleResponse(httpResponse, new TypeReference<>() {});
			return tokenCheckResponses.prompts.get(0);
		}
	}

    @JsonInclude(JsonInclude.Include.NON_NULL)
	public record TokenCheckRequest(
			String model,
			String prompt,
			@JsonProperty("max_tokens") Long maxTokens
	) {
		public TokenCheckRequest(String model, String prompt) {
			this(model, prompt, 0L);
		}
	}

	@JsonInclude(JsonInclude.Include.NON_NULL)
	public record TokenCheckResponseList(
			List<TokenCheckResponse> prompts
	) {}

	@JsonInclude(JsonInclude.Include.NON_NULL)
	public record TokenCheckResponse(
			Boolean fits,
			Long tokenCount,
			Long contextLength
	) {}

	 private <T> T handleResponse(HttpResponse httpResponse, TypeReference<T> typeReference) {
        if (httpResponse.isOk()) {
            return JsonUtil.parseObject(httpResponse.body(), typeReference);
        } else {
            throw new OpenAiApiException(String.format("%s - %s", httpResponse.getStatus(),
                    JsonUtil.parseObject(httpResponse.body(), ResponseError.class)));
        }
    }

	@JsonInclude(JsonInclude.Include.NON_NULL)
	public record ResponseError(
			String object,
			String message,
			Integer code
	) {}
}
