/*
 * Copyright 2023-2023 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.wcompass.edgs.ai.autoconfigure.vectorstore.pgvector;

import cn.hutool.db.Db;
import com.wcompass.edgs.ai.autoconfigure.AiAutoConfiguration;
import com.wcompass.edgs.ai.vectorstore.PgVectorStore;
import com.wcompass.edgs.ai.vectorstore.VectorStore;
import com.wcompass.edgs.utils.EncryptUtil;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.ai.embedding.EmbeddingClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR> Tzolov
 */
@Configuration
@EnableConfigurationProperties(PgVectorStoreProperties.class)
@ConditionalOnProperty(prefix = AiAutoConfiguration.EDGS_AI_PREFIX, name = "enable", havingValue = "true", matchIfMissing = true)
public class PgVectorStoreAutoConfiguration {

    @Value("${encrypt.key:123456}")
    private String encryptKey;

    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = VectorStore.VECTOR_STORE_PREFIX, name = "platform", havingValue = "pgvector")
    public VectorStore vectorStore(Db db, EmbeddingClient embeddingClient, PgVectorStoreProperties properties) {

        return new PgVectorStore(db, embeddingClient, properties.getDimensions(),
                properties.getDistanceType(), properties.isRemoveExistingVectorStoreTable(), properties.getIndexType());
    }


    @Bean
    @ConditionalOnProperty(prefix = VectorStore.VECTOR_STORE_PREFIX, name = "platform", havingValue = "pgvector")
    public Db db(PgVectorStoreProperties properties) {
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName(properties.getDriverClassName());
        dataSource.setJdbcUrl(properties.getJdbcUrl());
        dataSource.setUsername(properties.getUsername());

        String password = properties.getPassword();
        password = EncryptUtil.aesDecrypt(password, encryptKey);
        dataSource.setPassword(password);

        dataSource.setMinimumIdle(properties.getMinimumIdle());
        dataSource.setMaximumPoolSize(properties.getMaximumPoolSize());

        return new CustomDb(dataSource);
    }
}
