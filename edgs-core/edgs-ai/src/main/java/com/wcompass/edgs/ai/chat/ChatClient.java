package com.wcompass.edgs.ai.chat;


import org.springframework.ai.chat.StreamingChatClient;

public interface ChatClient extends org.springframework.ai.chat.ChatClient, StreamingChatClient {

    String TEXT_TO_SQL_CLIENT = "text2sqlClient";

    String CHAT_DOC_CLIENT = "chatDocClient";


    /**
     * 校验提示词的token
     *
     * @param prompt
     * @return
     */
    TokenResponse checkToken(String prompt);

}
