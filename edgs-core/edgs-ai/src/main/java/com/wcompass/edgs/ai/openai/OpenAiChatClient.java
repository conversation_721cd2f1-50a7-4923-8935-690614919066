package com.wcompass.edgs.ai.openai;

import com.wcompass.edgs.ai.chat.ChatClient;
import com.wcompass.edgs.ai.chat.TokenResponse;
import org.springframework.ai.openai.OpenAiChatOptions;

public class OpenAiChatClient extends org.springframework.ai.openai.OpenAiChatClient implements ChatClient {

    private final OpenAiChatOptions defaultOptions;

    public final com.wcompass.edgs.ai.openai.api.OpenAiApi openAiApi;

    public OpenAiChatClient(com.wcompass.edgs.ai.openai.api.OpenAiApi openAiApi, OpenAiChatOptions defaultOptions) {
        super(openAiApi, defaultOptions);
        this.defaultOptions = defaultOptions;
        this.openAiApi = openAiApi;
    }

    @Override
    public TokenResponse checkToken(String prompt) {
        com.wcompass.edgs.ai.openai.api.OpenAiApi.TokenCheckRequest request = new com.wcompass.edgs.ai.openai.api.OpenAiApi.TokenCheckRequest(this.defaultOptions.getModel(), prompt);
        com.wcompass.edgs.ai.openai.api.OpenAiApi.TokenCheckResponse tokenCheckResponse = openAiApi.tokenCheck(request);
        TokenResponse tokenResponse = new TokenResponse();
        tokenResponse.setFits(tokenCheckResponse.fits());
        tokenResponse.setTokenCount(tokenCheckResponse.tokenCount());
        tokenResponse.setContextLength(tokenCheckResponse.contextLength());
        return tokenResponse;
    }
}
