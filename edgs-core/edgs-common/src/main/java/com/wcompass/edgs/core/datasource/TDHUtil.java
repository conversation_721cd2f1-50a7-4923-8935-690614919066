package com.wcompass.edgs.core.datasource;

import com.wcompass.edgs.core.classloader.DriverClassLoader;
import com.wcompass.edgs.core.classloader.DriverClassLoaderFactory;
import com.wcompass.edgs.exception.SystemException;
import com.wcompass.edgs.utils.FileUtil;
import com.wcompass.edgs.utils.ReflectUtil;
import com.wcompass.edgs.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.net.MalformedURLException;
import java.sql.Connection;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2023/5/31
 */
@Slf4j
public class TDHUtil {

    public static DriverClassLoader getTDHDriverClassLoader(String toolkitVersion) {
        return DriverClassLoaderFactory.get("tdh", toolkitVersion);
    }

    public static boolean testTDHConnection(String toolkitVersion, String driverClassName, String url, String userName, String password) throws MalformedURLException, ClassNotFoundException {
        try {
            try (Connection connection = getJdbcConnection(toolkitVersion, driverClassName, url,
                    userName,
                    password,
                    null,
                    null,
                    null,
                    null)) {
                connection.getMetaData().getSchemas();
                return connection.isValid(9);
            }
        } catch (Exception e) {
            throw SystemException.wrap(e);
        }
    }

    public static Connection getJdbcConnection(String toolkitVersion, String driverClassName, String url,
                                               String userName,
                                               String password) {
        return getJdbcConnection(toolkitVersion, driverClassName, url, userName,
                password,
                null,
                null,
                null,
                null);
    }

    public static Connection getJdbcConnection(String toolkitVersion, String driverClassName, String url,
                                               String principal,
                                               String kuser,
                                               byte[] krb5confFileBytes,
                                               byte[] keytabFileBytes) {
        return getJdbcConnection(toolkitVersion, driverClassName, url, null, null, principal, kuser, krb5confFileBytes, keytabFileBytes);
    }

    public static Connection getJdbcConnection(String toolkitVersion, String driverClassName, String url,
                                               String userName,
                                               String password,
                                               String principal,
                                               String kuser,
                                               byte[] krb5confFileBytes,
                                               byte[] keytabFileBytes) {
        try {
            DriverClassLoader driverClassLoader = getTDHDriverClassLoader(toolkitVersion);
            Class<?> driverClazz = driverClassLoader.loadClass(driverClassName);
            Constructor<?> constructor = driverClazz.getConstructor();
            Object tdh = constructor.newInstance();

            Properties properties = new Properties();
            if (userName != null) {
                properties.put("user", userName);
            }
            if (password != null) {
                properties.put("password", password);
            }
            if (keytabFileBytes != null) {
                url = StringUtil.join(url, ";authentication=kerberos");

                String tmpDirPath = System.getProperty("java.io.tmpdir");

                File confTempFile = FileUtil.createTempFile(new File(tmpDirPath));
                FileUtil.writeBytes(krb5confFileBytes, confTempFile);
                url = StringUtil.join(url,  ";krb5conf=", FileUtil.getUnixLikePath(confTempFile));

                File keytabTempFile = FileUtil.createTempFile(new File(tmpDirPath));
                FileUtil.writeBytes(keytabFileBytes, keytabTempFile);
                url = StringUtil.join(url,  ";keytab=", FileUtil.getUnixLikePath(keytabTempFile));

                if (StringUtil.isNotBlank(principal)) {
                    url = StringUtil.join(url, ";principal=", principal);

                }
                if (StringUtil.isNotBlank(kuser)) {
                    url = StringUtil.join(url, ";kuser=", kuser);
                }
            }
            return ReflectUtil.invoke(tdh, "connect", url, properties);
        } catch (Exception e) {
            throw SystemException.wrap(e);
        }
    }

    public static boolean testTDHConnectionByKerberos(String toolkitVersion, String driverClassName, String url,
                                                      String principal,
                                                      String kuser,
                                                      byte[] krb5confFileBytes,
                                                      byte[] keytabFileBytes) {
        try {
            try (Connection connection = getJdbcConnection(toolkitVersion, driverClassName, url,
                    null,
                    null,
                    principal,
                    kuser,
                    krb5confFileBytes,
                    keytabFileBytes)) {
                connection.getMetaData().getSchemas();
                return connection.isValid(9);
            }
        } catch (Exception e) {
            throw SystemException.wrap(e);
        }
    }
}
