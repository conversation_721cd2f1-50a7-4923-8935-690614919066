package com.wcompass.edgs.core.log;

import com.wcompass.edgs.exception.SystemException;
import com.wcompass.edgs.utils.StringUtil;
import lombok.Getter;

import java.util.Locale;

/**
 * 日志级别枚举
 * <AUTHOR>
 * @date 2021/5/24
 */
@Getter
public enum LogLevelEnum {

    /**
     * debug
     */
    DEBUG("1"),

    INFO("2"),

    WARN("3"),

    ERROR("4")

    ;

    private String value;

    public static LogLevelEnum match(String value) {
        for (LogLevelEnum logLevelEnum : LogLevelEnum.values()) {
            if(StringUtil.equals(logLevelEnum.getValue(), value)) {
                return logLevelEnum;
            }
        }
        throw SystemException.wrap("无效的日志类型 {}", value);
    }

    LogLevelEnum(String value) {
        this.value = value;
    }

    public static String describe(String logLevel) {
        return match(logLevel).name();
    }
}
