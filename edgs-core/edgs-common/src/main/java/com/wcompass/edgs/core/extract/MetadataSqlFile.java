package com.wcompass.edgs.core.extract;

import com.wcompass.edgs.utils.StringUtil;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/6/18
 */
@Getter
public enum MetadataSqlFile {

    SCHEMA("schema", "Schema", ".sql", ".json"),

    MONGO_DATABASE("mongoDatabase", MetaModel.MONGO_DATABASE, ".sql", ".json"),

    /**
     * 表
     */
    TABLES("table", "Table", ".sql", ".csv"),

    TABLES_COLUMN_CLAUSE("tableColumnClause", "Table", ".sql", ".csv"),

    TABLE_SOURCES("tableSource", "Table", ".sql", ".csv"),

    NORMAL_TABLE_DDL("normalTableDDL", "Table", ".sql", ".csv"),

    EXTERNAL_TABLE_DDL("externalTableDDL", "Table", ".sql", ".csv"),

    TABLE_COLUMNS("tableColumn", "Column", ".sql", ".csv"),

    TD_MACROS("tdMacro", "TdMacro", ".sql", ".csv"),

    VIEWS("view", "View", ".sql", ".csv"),

    DICTIONARY("dictionary", MetaModel.DICTIONARY, ".sql", ".json"),

    DICTIONARY_COLUMNS("dictionaryColumn", MetaModel.COLUMN, ".sql", ".json"),

    VIEW_DDL("viewDDL", "View", ".sql", ".csv"),

    MATERIALIZED_VIEW_DDL("materializedViewDDL", "View", ".sql", ".csv"),

    VIEW_SOURCES("viewSource", "View", ".sql", ".csv"),

    VIEW_COLUMNS("viewColumn", "Column",".sql", ".csv"),

    PRIMARY_KEYS("primaryKey", "PrimaryKey",".sql", ".csv"),

    PRIMARY_KEY_COLUMNS("primaryKeyColumn",  "Column",".sql", ".csv"),

    FOREIGN_KEYS("foreignKey", "ForeignKey",".sql", ".csv"),

    FOREIGN_KEY_COLUMNS("foreignKeyColumn", "Column",".sql", ".csv"),

    FUNCTIONS("function", "Function",".sql", ".csv"),

    FUNCTION_DDL("functionDDL", "Function",".sql", ".csv"),

    FUNCTION_SOURCES("functionSource", "Function",".sql", ".csv"),

    PROCEDURES("procedure", "Procedure",".sql", ".csv"),

    PROCEDURE_DDL("procedureDDL", "Procedure",".sql", ".csv"),

    PROCEDURE_SOURCES("procedureSource", "Procedure",".sql", ".csv"),

    ORA_PACKAGES("oraPackage",  "OraPackage",".sql", ".csv"),

    SYNONYM("synonym",  "Synonym",".sql", ".csv"),

    SYNONYM_COLUMNS("synonymColumn", "Column",".sql", ".csv"),

    DATABASE_LINK("databaseLink",  "DatabaseLink",".sql", ".csv"),

    SLICE_ORA_PACKAGES("sliceOraPackage",  "OraPackage",".sql", ".csv"),

    SLICE_ORA_PACKAGE_SOURCES("sliceOraPackageSource",  "OraPackage",".sql", ".csv"),

    ORA_PACKAGE_SOURCES("oraPackageSource",  "OraPackage",".sql", ".csv"),

    ORA_PACKAGE_BODY_SOURCES("oraPackageBodySource",  "OraPackage",".sql", ".csv"),

    ORA_PACKAGE_FUNCTIONS("oraPackageFunction",  "Function",".sql", ".csv"),

    ORA_PACKAGE_PROCEDURES("oraPackageProcedure", "Procedure",".sql", ".csv"),

    INDEXES("index", "SQLIndex",".sql", ".csv"),

    INDEX_COLUMNS("indexColumn", "Column",".sql", ".csv"),

    TRIGGERS("trigger",  "Trigger",".sql", ".csv"),

    TRIGGER_DDL("triggerDDL",  "Trigger",".sql", ".csv"),

    TRIGGER_SOURCES("triggerSource",  "Trigger",".sql", ".csv"),

    PARTITIONS("partition", "Partition",".sql", ".csv"),

    PARTITION_TABLES("partitionTable", "PartitionTable",".sql", ".csv"),

    DIST_KEYS("distKey", "DistKey",".sql", ".csv"),

    DIST_KEY_COLUMNS("distKeyColumn", "Column",".sql", ".csv"),

    PIS("pi", "PI", ".sql", ".csv"),

    PI_COLUMNS("piColumn", "PI", ".sql", ".csv"),

    /**
     * Hbase
     */
    HBASE_TABLE("hbaseTable", "HbaseTable",".sql", ".csv"),

    COLUMN_FAMILY("columnFamily", "ColumnFamily",".sql", ".csv"),

    COLUMN_QUALIFIER("columnQualifier", "ColumnQualifier",".sql", ".csv"),

    FINE_REPORT("fineReport", MetaModel.FINE_REPORT, null, ".json"),
    /**
     * FR
     */
    FR_DATABASE("FRDatasource","FRDatasource",".sql", ".csv"),
    FR_REPORT("FRReport","FRReport",".sql",".csv"),
    FR_FOLDEAR("FRFolder","FRFolder",".sql",".csv"),
    FR_DATASET("FRDataSet","FRDataSet",".sql",".csv"),
    FR_INPUT("FRInput","FRInput",".sql",".csv"),
    FR_OUTPUT("FROutput","FROutput",".sql",".csv"),


    /**
     * Kettle
     */
    KETTLE_TRANSFORMATION("kettleTransformation", "KettleTransformation", ".sql", ".csv"),

    KETTLE_TRANSFORMATION_FILE("kettleTransformationFile", "KettleFile", ".sql", ".csv"),

    KETTLE_STEP("kettleStep", "KettleFile", ".sql", ".csv"),

    KETTLE_STEP_ATTRIBUTE("kettleStepAttribute", "KettleFile", ".sql", ".csv"),

    KETTLE_STEP_DATABASE("kettleStepDatabase", "KettleFile", ".sql", ".csv"),

    KETTLE_SCRIPT("kettleScript", "KettleFile", ".sql", ".csv"),

    KETTLE_CONNECTION("kettleConnection", "KettleConnection", ".sql", ".csv"),

    KETTLE_COLUMN("kettleColumn", "KettleColumn", ".sql", ".csv"),

    KETTLE_DIRECTORY("kettleDirectory", "KettleDirectory", ".sql", ".csv"),

    KETTLE_JOB("kettleJob", "KettleJob", ".sql", ".csv"),

    KETTLE_JOB_SQL("kettleJobSql", "KettleJobSQL", ".sql", ".csv"),

    KETTLE_JOB_SQL_CONNECTION("kettleJobSqlConnection", "KettleJobSQL", ".sql", ".csv"),

    KETTLE_TRANSFORMATION_JOB("kettleTransformationJob", "KettleJob", ".sql", ".csv"),

    KETTLE_DIR_PATH("kettleDirPath", "KettleJob", ".sql", ".csv"),

    KETTLE_FILE_CONNECTION("kettleFileConnection", "KettleFile", ".sql", ".csv"),

    KETTLE_SCRIPT_CONNECTION("kettleScriptConnection", "KettleFile", ".sql", ".csv"),

    /**
     * es
     */
    ES_CLUSTER("esCluster", MetaModel.ES_CLUSTER, ".sql", ".json"),

    ES_NODE("esNode", MetaModel.ES_NODE, ".sql", ".csv"),

    ES_INDEX("esIndex", MetaModel.ES_INDEX, ".sql", ".csv"),

    ES_DOC_TYPE("esDocType", MetaModel.ES_DOC_TYPE, ".sql", ".csv"),

    ES_FIELD("esField", MetaModel.ES_FIELD, ".sql", ".csv"),

    REDIS("redis", MetaModel.REDIS, ".sql", ".json"),

    REDIS_DATABASE("redisDatabase", MetaModel.REDIS_DATABASE, ".sql", ".json"),

    REDIS_NODE("redisNode", MetaModel.REDIS_NODE, ".sql", ".json"),

    REDIS_ZSET("redisZSet", MetaModel.REDIS_ZSET, ".sql", ".json"),
    REDIS_STRING("redisString", MetaModel.REDIS_STRING, ".sql", ".json"),
    REDIS_SET("redisSet", MetaModel.REDIS_SET, ".sql", ".json"),
    REDIS_LIST("redisList", MetaModel.REDIS_LIST, ".sql", ".json"),
    REDIS_HASH("redisHash", MetaModel.REDIS_HASH, ".sql", ".json"),
    REDIS_HASH_FIELD("redisHashField", MetaModel.REDIS_HASH_FIELD, ".sql", ".json"),

    PERL_SCRIPT_SET("perlScriptSet", MetaModel.PERL_SCRIPT_SET, ".sql", ".json"),

    SCRIPT_SET("scriptSet", MetaModel.SCRIPT_SET, ".sql", ".json"),

    COLLECTION_SPACE("", MetaModel.COLLECTION_SPACE, ".sql", ".json"),

    INFORMATICA("Informatica", "Informatica", ".sql", ".csv"),
    INFA_FOLDER("InfaFolder", "InfaFolder", ".sql", ".csv"),
    INFA_SESSION("InfaSession", "InfaSession", ".sql", ".csv"),
    INFA_CONNECTION("InfaConnection", "InfaConnection", ".sql", ".csv"),
    INFA_MAPPING_CONN_PORT("InfaMappingConnPort", "InfaMappingConnPort", ".sql", ".csv"),
    INFA_COLUMN("InfaColumn", "InfaColumn", ".sql", ".csv"),
    INFA_SOURCE("InfaSource", "InfaSource", ".sql", ".csv"),
    INFA_TARGET("InfaTarget", "InfaTarget", ".sql", ".csv"),
    INFA_TRANSFORM("InfaTransform", "InfaTransform", ".sql", ".csv"),
    INFA_MAPPING("InfaMapping", "InfaMapping", ".sql", ".csv"),

    TABLEAU_SITE("site", MetaModel.TABLEAU_SITE, ".sql", ".json"),

    TABLEAU_PROJECT("project", MetaModel.TABLEAU_PROJECT, ".sql", ".json"),

    TABLEAU_DATA_CONNECTION("dataConnection", MetaModel.TABLEAU_CONNECTION, ".sql", ".json"),

    TABLEAU_DATA_CONNECTION_DATASOURCE("dataConnectionDatasource", MetaModel.TABLEAU_CONNECTION, ".sql", ".json"),

    TABLEAU_DATASOURCE("datasource", MetaModel.TABLEAU_DATASOURCE, ".sql", ".json"),

    TABLEAU_WORKBOOK("workbook", MetaModel.TABLEAU_WORKBOOK, ".sql", ".json"),

    TABLEAU_WORKBOOK_DATASOURCE("workbookDatasource", MetaModel.TABLEAU_DATASOURCE, ".sql", ".json"),

    TABLEAU_WORKSHEET("worksheet", MetaModel.TABLEAU_WORKSHEET, ".sql", ".json"),

    TABLEAU_DASHBOARD("dashboard", MetaModel.TABLEAU_DASHBOARD, ".sql", ".json"),

    TABLEAU_STORYBOARD("storyboard", MetaModel.TABLEAU_STORYBOARD, ".sql", ".json"),

    TABLEAU_VIRTUAL_CONNECTION("virtualConnection", MetaModel.TABLEAU_VIRTUAL_CONNECTION, ".sql", ".json"),

    TABLEAU_PUBLISHED_TABLE("publishedTable", MetaModel.TABLEAU_TABLE, ".sql", ".json"),

    TABLEAU_LOAD_CONNECTION_PROPERTIES("loadConnectionProperties", null, ".sql", ".json"),

    TABLEAU_LOAD_TABLE_PROPERTIES("loadTableProperties", null, ".sql", ".json"),

    TABLEAU_PROJECT_CONTENT("projectContent", null, ".sql", ".json"),

    DATAEASE("dataease", MetaModel.DATAEASE, ".sql", ".json"),

    DE_DATASOURCE("deDatasource", MetaModel.DE_DATASOURCE, ".sql", ".json"),

    DE_DATASET_GROUP("deDatasetGroup", MetaModel.DE_DATASET_GROUP, ".sql", ".json"),

    DE_SQL_DATASET("deSQLDataset", MetaModel.DE_SQL_DATASET, ".sql", ".json"),

    DE_EXCEL_DATASET("deExcelDataset", MetaModel.DE_EXCEL_DATASET, ".sql", ".json"),

    DE_TABLE_DATASET("deTableDataset", MetaModel.DE_TABLE_DATASET, ".sql", ".json"),

    DE_FIELD("deField", MetaModel.DE_FIELD, ".sql", ".json"),

    DE_PANEL_FOLDER("dePanelFolder", MetaModel.DE_PANEL_FOLDER, ".sql", ".json"),

    DE_PANEL("dePanel", MetaModel.DE_PANEL, ".sql", ".json"),

    DE_PANEL_VIEW("dePanelView", MetaModel.DE_PANEL_VIEW, ".sql", ".json"),

    DE_PANEL_VIEW_FIELD("dePanelViewField", null, ".sql", ".json"),

    SMART_BI("smartBI", MetaModel.SMART_BI, ".sql", ".json"),

    SMART_BI_RES_TREE("resTree", MetaModel.SMART_BI, ".sql", ".json"),

    SMART_BI_DATASOURCE("datasource", MetaModel.ST_DATASOURCE, ".sql", ".json"),

    SMART_BI_FIELD("field", MetaModel.ST_FIELD, ".sql", ".json"),

    SMART_BI_SQL_QUERY("sqlQuery", MetaModel.ST_SQL_QUERY, ".sql", ".json"),

    SMART_BI_TABLE_LINK("tablelink", MetaModel.ST_SQL_QUERY, ".sql", ".json"),

    SMART_BI_PROCESS_DAG("processdag", MetaModel.ST_DATAPROCESS, ".sql", ".json"),

    YONG_HONG("yonghong", MetaModel.YONG_HONG, ".sql", ".json"),

    DATA_WORKS("dataWorks", MetaModel.DW_PROJECT, ".sql", ".json"),

    ABI_BI_SET("biSet",MetaModel.ABI_BI_SET,".sql", ".json"),

    ABI_BI_PROJECT("project",MetaModel.ABI_BI_PROJECT,".sql", ".json"),

    ABI_BI_SUBJECT_SET("subjectSet",MetaModel.ABI_BI_SUBJECT_SET,".sql", ".json"),

    ABI_BI_FOLDER("folder",MetaModel.ABI_BI_FOLDER,".sql", ".json"),

    ABI_BI_REPORT("report",MetaModel.ABI_BI_REPORT,".sql", ".json"),

    ABI_ETL_SET("etlSet",MetaModel.ABI_ETL_SET,".sql", ".json"),

    ABI_ETL_FOLDER("folder",MetaModel.ABI_ETL_FOLDER,".sql", ".json"),

    ABI_ETL_CONNECTION("connection",MetaModel.ABI_ETL_CONNECTION,".sql", ".json"),

    ABI_ETL_JOB("job",MetaModel.ABI_ETL_JOB,".sql", ".json"),

    SQOOP_JOB("sqoopJob", MetaModel.SQOOP_JOB, ".sql", ".csv"),


    GUAN_BI_DATA_USER("dataUser", MetaModel.GUAN_DATA_USER, ".sql", ".json"),


    GUAN_BI_FOLDER("folder",MetaModel.GUAN_FOLDER ,".sql" ,".json" ),

    GUAN_BI_DATA_SET("dataSet",MetaModel.GUAN_DATA_SET,".sql" ,".json" ),

    GUAN_BI_DASHBOARD("dashboard",MetaModel.GUAN_DASHBOARD,".sql" ,".json" ),

    GUAN_BI_BIG_DATA_SCREEN("bigDataScreen",MetaModel.GUAN_BIG_DATA_SCREEN,".sql" ,".json" ),

    GUAN_BI_FIELD_DATA_SET_RELATIONSHIP("field",MetaModel.GUAN_BI,".sql",".json"),

    GUAN_BI_REPORT_ITEM("reportItem",MetaModel.GUAN_REPORT_ITEM,".sql",".json"),

    GUAN_BI_CARD("card",MetaModel.GUAN_BI,".sql",".json"),

    AZURE_DATABRICKS_VOLUME("volume", MetaModel.AZURE_DB_VOLUME, ".sql", ".json"),

    DOLPHIN_PROJECT("dolphinProject",MetaModel.DOLPHIN_PROJECT,".sql", ".json"),

    DOLPHIN_PROCESS_DEF("dolphinProcessDef",MetaModel.DOLPHIN_PROCESS_DEF,".sql", ".json"),

    DOLPHIN_TASK_DEF("dolphinTaskDef",MetaModel.DOLPHIN_TASK_DEF,".sql", ".json"),

    DOLPHIN_PROCESS_TASK_RELATION("dolphinProcessTaskRelation",MetaModel.DOLPHIN_PROCESS_TASK_RELATION,".sql", ".json"),

    DOLPHIN_DATASOURCE("dolphinDatasource",MetaModel.DOLPHIN_DATASOURCE,".sql", ".json"),

    TASK("task", MetaModel.TASK, ".sql", ".json"),

    FILE("file", MetaModel.DATA_FILE_FOLDER, ".sql", ".json"),
    UNIQUE_CONSTRAINTS("uniqueConstraint", "UniqueConstraint", ".sql", ".csv"),

    CHECK_CONSTRAINTS("checkConstraint", "CheckConstraint", ".sql", ".csv"),

    COLUMN_CONSTRAINTS("columnConstraints", "ColumnConstraint", ".sql", ".csv"),
    ;



    private String name;

    private String classifierId;

    private String sqlFileSuffix;

    private String dataFileSuffix;

    public String getSqlFileName() {
        return StringUtil.join(this.name, this.sqlFileSuffix);
    }

    public String getDataFileName() {
        return StringUtil.join(this.name, this.dataFileSuffix);
    }

    MetadataSqlFile(String name, String classifierId, String sqlFileSuffix, String dataFileSuffix) {
        this.name = name;
        this.classifierId = classifierId;
        this.sqlFileSuffix = sqlFileSuffix;
        this.dataFileSuffix = dataFileSuffix;
    }
}
