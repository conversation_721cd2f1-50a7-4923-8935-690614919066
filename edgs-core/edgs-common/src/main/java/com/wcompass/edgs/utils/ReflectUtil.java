package com.wcompass.edgs.utils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.stream.Stream;

/**
 * 反射的工具类
 *
 * <AUTHOR>
 * @date Created on 2019/8/21
 */
public final class ReflectUtil extends cn.hutool.core.util.ReflectUtil {

    /**
     * 获取当前类，父类所有的属性，但是排除了final、static修饰的属性
     *
     * @param beanClass
     * @return
     */
    public static Field[] getOrdinaryFields(Class<?> beanClass) {
        return Stream.of(getFields(beanClass))
                .filter(field -> !Modifier.isFinal(field.getModifiers()))
                .filter(field -> !Modifier.isStatic(field.getModifiers()))
                .peek(field -> field.setAccessible(true))
                .toArray(Field[]::new);
    }

    public static String getMethodFullName(Method method) {
        return StringUtil.join(method.getDeclaringClass().getName(), ".", method.getName());
    }
}
