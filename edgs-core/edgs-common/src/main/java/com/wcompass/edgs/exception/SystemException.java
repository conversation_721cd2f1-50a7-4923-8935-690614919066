package com.wcompass.edgs.exception;

import com.wcompass.edgs.utils.ExceptionUtil;
import lombok.EqualsAndHashCode;
import org.slf4j.helpers.MessageFormatter;

import java.util.HashMap;
import java.util.Map;

/**
 * 系统异常
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = false)
public class SystemException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    private int code;

    private String msg;

    private Map<String, Object> extras;

    public SystemException addExtras(String key, Object value) {
        if (this.extras == null) {
            this.extras = new HashMap<>(0);
        }
        this.extras.put(key, value);
        return this;
    }

    public Map<String, Object> getExtras() {
        return extras;
    }

    public SystemException(ExceptionEnum exception) {
        super(exception.getMsg());
        this.code = exception.getCode();
        this.msg = exception.getMsg();
    }

    public SystemException(int code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }

    public SystemException(String message) {
        super(message);
        this.code = ExceptionEnum.PURE_EX.getCode();
        this.msg = message;
    }

    public SystemException(Throwable e) {
        super(e);
        this.code = ExceptionEnum.PURE_EX.getCode();
        this.msg = getMessage();
    }

     public SystemException(String message, Throwable e) {
        super(message, e);
        this.code = ExceptionEnum.PURE_EX.getCode();
        this.msg = message;
    }

    public static SystemException wrap(String messagePattern, Object... arguments) {
        String message = MessageFormatter.arrayFormat(messagePattern, arguments).getMessage();
        return new SystemException(message);
    }

    public static SystemException wrap(int code, String messagePattern, Object... arguments) {
        String message = MessageFormatter.arrayFormat(messagePattern, arguments).getMessage();
        return new SystemException(code, message);
    }

    public static SystemException wrap(Throwable e) {
        if (e instanceof SystemException) {
            return (SystemException) e;
        }
        return new SystemException(e);
    }

    public static SystemException wrap(String message, Throwable e) {
        if (e instanceof SystemException systemException) {
            systemException.setMsg(message);
            return systemException;
        }
        return new SystemException(message, e);
    }

    /**
     * 抛出一个警告异常，通常该异常是正常的业务逻辑处理抛出的错误，前端需要使用橙色提示
     * @return
     */
    public static SystemException warn(String messagePattern, Object... arguments) {
        String message = MessageFormatter.arrayFormat(messagePattern, arguments).getMessage();
        return new SystemException(ExceptionEnum.WARN.getCode(), message);
    }

    public static SystemException wrap(ExceptionEnum exceptionEnum) {
        return new SystemException(exceptionEnum);
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
