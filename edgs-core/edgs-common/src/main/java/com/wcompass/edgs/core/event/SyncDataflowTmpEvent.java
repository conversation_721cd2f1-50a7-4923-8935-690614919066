package com.wcompass.edgs.core.event;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

import java.io.Serial;

/**
 * <AUTHOR>
 * @date 2023年05月29日8:11
 */
@Getter
@Setter
public class SyncDataflowTmpEvent extends ApplicationEvent {

    @Serial
    private static final long serialVersionUID = 1620559840835758248L;
    private String metadataId;

    private long createTime;


    public SyncDataflowTmpEvent(String metadataId, long createTime) {
        super("SyncDataflowTmpEvent");
        this.metadataId = metadataId;
        this.createTime = createTime;
    }
}
