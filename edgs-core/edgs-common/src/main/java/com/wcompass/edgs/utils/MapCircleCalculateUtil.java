package com.wcompass.edgs.utils;

import cn.hutool.core.util.NumberUtil;
import com.wcompass.edgs.core.circle.CircleCoordinateCalculate;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * @Auther: xwy
 * @Date: 2022/8/8 15:32
 * @Description: 地图圆坐标计算工具类
 */
@Slf4j
public class MapCircleCalculateUtil {
    //初始缓存5级最大圆个数
    static Map<Integer, Integer> maxNumberCache = new HashMap<>();

    //缓存最大层级
    static final int cacheLevel = 5;

    static {
        for (int i = 1; i <= cacheLevel; i++) {
            maxNumberCache.put(i, range(i));
        }
        log.debug("缓存层级数据：" + maxNumberCache.toString());
    }

    /**
     * 计算并排序嵌套圆坐标
     *
     * @param radius  最小单元圆半径
     * @param width   画布宽
     * @param height  画布高
     * @param initMap 待计算坐标圆数据，key->comboId，value->每个combo中圆个数
     * @return 每个combo中小圆的圆心坐标值
     */
    public static Map<String, List<CircleCoordinateCalculate>> circleCalculate(
            int radius, int width, int height, Map<String, Integer> initMap
    ) {
        Map<String, List<CircleCoordinateCalculate>> resultMap = new HashMap<>();
        for (Map.Entry<String, Integer> entry : initMap.entrySet()) {
            //内部圆个数超出上限后不排序
            if (entry.getValue() >= CircleCoordinateCalculate.getMaxNum()) {
                return resultMap;
            }
        }
        //圆半径
//        log.debug("初始圆半径：" + radius);
//        log.debug("初始圆坐标：【{},{}】", radius, radius);
        //限定圆分布范围
//        log.debug("画布范围，width【{}】，height【{}】", width, height);
        //每行最多放入圆个数
        int rowMax = width / (2 * radius);
//        log.debug("每行最多放入圆个数:" + rowMax);
        //每列最多放入圆个数
        int columnMax = height / radius;
//        log.debug("每列最多放入圆个数:" + columnMax);
        //初始圆
//        log.debug("初始化圆：" + initMap.toString());
        Map<String, Integer> circleMap = new HashMap<>();
        //将单个圆收集在最后单独处理，放一排
        List<Map.Entry<String, Integer>> level_1_circle = new ArrayList<>();
        List<List<Map.Entry<String, Integer>>> level_1_circle_lsit = new ArrayList<>();

        List<Map.Entry<String, Integer>> initEntryList = new ArrayList<>(initMap.entrySet());
        for (int i = 0; i < initEntryList.size(); i++) {
            Map.Entry<String, Integer> entry = initEntryList.get(i);
            if (entry.getValue() == 1) {
                level_1_circle.add(entry);
            } else {
                circleMap.put(entry.getKey(), entry.getValue());
            }
            if (CollectionUtil.isNotEmpty(level_1_circle)
                    && (level_1_circle.size() >= rowMax || i == initEntryList.size() - 1)) {
                level_1_circle_lsit.add(level_1_circle);
                level_1_circle = new ArrayList<>();
            }
        }
//        log.debug("单个圆收集:{}", level_1_circle_lsit.toString());
        //排序
        List<Map.Entry<String, Integer>> entryList = new ArrayList<>(circleMap.entrySet());
        entryList.sort(new Comparator<Map.Entry<String, Integer>>() {
            @Override
            public int compare(Map.Entry<String, Integer> o1, Map.Entry<String, Integer> o2) {
                return o2.getValue().compareTo(o1.getValue());
            }
        });
//        log.debug("排序后的个数大于1的圆集合：" + entryList.toString());
        //按每行最多圆给圆分层
        List<List<Map.Entry<String, Integer>>> levels = new ArrayList<>();
        //当前行宽
        double legnthRow = 0;
        List<Map.Entry<String, Integer>> tempList = new ArrayList<>();
        for (int i = 0; i < entryList.size(); i++) {
            Map.Entry<String, Integer> entry = entryList.get(i);
            Integer cirlceNum = entry.getValue();
            double diameter = 2 * getExterRadius(cirlceNum, radius);
            legnthRow = legnthRow + diameter;
            //当当前行放不下这么多圆时，另起一行
            if (legnthRow <= width) {
                tempList.add(entry);
                if (i == entryList.size() - 1) {
                    levels.add(tempList);
                }
            } else {
                levels.add(tempList);
                legnthRow = diameter;
                tempList = new ArrayList<>();
                tempList.add(entry);
                if (i == entryList.size() - 1) {
                    levels.add(tempList);
                }
            }
        }
//        log.debug("按每行最多圆给圆分层:{}", levels.toString());

        //分配大圆圆心
        //给每行圆设置y轴坐标，按照上一行最大圆坐标偏移给定
        double levelYAxis = 0;
        double preYAis = 0;
        double preYRadius = 0;
        List<Double> yAxisList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(level_1_circle_lsit)) {
            levels.addAll(level_1_circle_lsit);
        }
        for (List<Map.Entry<String, Integer>> level : levels) {
            Map.Entry<String, Integer> entry = level.get(0);
            Integer circleNum = entry.getValue();
            double radiusNow = getExterRadius(circleNum, radius);
            levelYAxis = preYAis + preYRadius + radiusNow;
            preYAis = levelYAxis;
            preYRadius = radiusNow;
            yAxisList.add(levelYAxis);
        }
//        log.debug("每行圆y轴坐标值：" + yAxisList.toString());

        //计算各圆坐标值
        for (int i = 0; i < levels.size(); i++) {
            List<Map.Entry<String, Integer>> level = levels.get(i);
            Double yAxis = yAxisList.get(i);
            double xAxis = 0;
            double preXAis = 0;
            double preRadius = 0;
            for (Map.Entry<String, Integer> entry : level) {
                String id = entry.getKey();
                Integer circleNum = entry.getValue();
                double radiusNow = getExterRadius(circleNum, radius);
                xAxis = preXAis + preRadius + radiusNow;
                preXAis = xAxis;
                preRadius = radiusNow;
                List<CircleCoordinateCalculate> circleCoordinateCalculates = CircleCoordinateCalculate.initCircleCoordinateCalculate(xAxis, yAxis, radius);
                circleCoordinateCalculates = circleCoordinateCalculates.subList(0, circleNum);
                resultMap.put(id, circleCoordinateCalculates);
                /*for (CircleCoordinateCalculate circleCoordinateCalculate : circleCoordinateCalculates) {
                    log.debug("构造好的圆,圆心坐标【{},{}】：{}", xAxis, yAxis, circleCoordinateCalculate.toString());
                }*/
            }
        }

        return resultMap;
    }

    /**
     * 判断2圆是否相交
     *
     * @param x1 圆一圆心x坐标
     * @param y1 圆一圆心y坐标
     * @param r1 圆一半径
     * @param x2 圆二圆心x坐标
     * @param y2 圆二圆心y坐标
     * @param r2 圆二半径
     * @return true：相交，false：未相交
     */
    public static boolean isIntersect(double x1, double y1, double r1, double x2, double y2, double r2) {
        checkRadius(r1);
        checkRadius(r2);
        if (r1 > x1 || r1 > y1 || r2 > x2 || r2 > y2) {
            throw new RuntimeException("圆半径不能比x轴坐标值或y轴坐标值小");
        }
        return (x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1) < (r2 + r1) * (r2 + r1);
    }

    /**
     * 根据蜂窝圆层数获取最大圆数量
     *
     * @param level 蜂窝圆层数
     * @return 最大圆数量
     */
    static int range(int level) {
        checkLevel(level);
        //阶段算法
        int total = 1 + 6 * level * (level - 1) / 2;
        return total;
    }

    /**
     * 根据圆数量，获取蜂窝层数
     *
     * @param num 圆数量
     * @return 蜂窝圆层数
     */
    static int getLevel(int num) {
        checkNum(num);
        for (Map.Entry<Integer, Integer> entry : maxNumberCache.entrySet()) {
            if (entry.getValue() >= num) {
                return entry.getKey();
            }
        }
        int level = cacheLevel + 1;
        while (num > range(level)) {
            level++;
        }
        return level;
    }

    /**
     * 校验蜂窝层数，须大于0
     *
     * @param level 蜂窝层数
     * @return true：校验通过
     */
    private static boolean checkLevel(int level) {
        if (level < 1) {
            throw new RuntimeException("蜂窝层数须大于0");
        }
        return true;
    }

    /**
     * 校验圆数量，须大于0
     *
     * @param num 圆数量
     * @return true：校验通过
     */
    private static boolean checkNum(int num) {
        if (num < 1) {
            throw new RuntimeException("圆数量须大于0");
        }
        return true;
    }

    /**
     * 校验圆半径，须大于1
     *
     * @param radius 圆半径
     * @return true：校验通过
     */
    private static boolean checkRadius(double radius) {
        if (radius < 1) {
            throw new RuntimeException("圆半径须大于0");
        }
        return true;
    }

    /**
     * 获取最小外部圆半径
     *
     * @param num               圆数量
     * @param innerCircleRadius 圆半径
     * @return 最小外部圆半径
     */
    static double getExterRadius(int num, double innerCircleRadius) {
        checkNum(num);
        checkRadius(innerCircleRadius);
        double exterCircleRadius = (1 + (getLevel(num) - 1) * 2) * innerCircleRadius;
        return exterCircleRadius;
    }

    /**
     * 获取最小外部圆面积
     *
     * @param level             蜂窝圆层数
     * @param innerCircleRadius 每个内部圆半径
     * @return 最小外部圆面积
     */
    static double getExterArea(int level, double innerCircleRadius) {
        double exterCircleRadius = (1 + (level - 1) * 2) * innerCircleRadius;
        return getCircleArea(exterCircleRadius);
    }

    /**
     * 获取圆面积
     *
     * @param radius 半径
     * @return 圆面积
     */
    static double getCircleArea(double radius) {
        return NumberUtil.mul(radius * radius, Math.PI);
    }

}
