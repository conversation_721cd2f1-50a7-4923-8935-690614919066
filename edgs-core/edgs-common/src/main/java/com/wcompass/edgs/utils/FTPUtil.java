package com.wcompass.edgs.utils;


import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.wcompass.edgs.exception.SystemException;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Properties;

/**
 * FTP服务器工具类
 * <AUTHOR>
 * @date 2020/7/17
 */
@Slf4j
public class FTPUtil {

    /**
     * 从FTP服务器下载文件至本地
     *
     * @param url
     *   服务器IP地址
     * @param port
     *   服务器端口
     * @param userName
     *   用户登录名
     * @param password
     *   用户登录密码
     * @param remotePath
     *   服务器文件存储路径
     * @param fileName
     *   服务器文件存储名称
     * @param localPath
     *   本地文件存储路径
     * @return
     *   <b>true</b>：下载成功
     *   <br/>
     *   <b>false</b>：下载失败
     */
    /*public static boolean retrieveFile (String url, int port, String userName, String password, String remotePath, String fileName, String localPath) {
        boolean result = false;
        FTPClient ftp = new FTPClient();
        // 设置字符编码
        ftp.setControlEncoding("UTF-8");
        OutputStream os = null;
        try {
            // 连接至服务器
            ftp.connect(url ,port);
            ftp.enterRemotePassiveMode();
            // 登录服务器
            ftp.login(userName, password);
            int replyCode = ftp.getReplyCode(); //是否成功登录服务器
            // 判断返回码是否合法
            if (!FTPReply.isPositiveCompletion(ftp.getReplyCode())) {
                // 不合法时断开连接
                ftp.disconnect();
                // 结束程序
                return result;
            }
            // 设置文件操作目录
            ftp.changeWorkingDirectory(remotePath);
            // 设置文件类型，二进制
            ftp.setFileType(FTPClient.BINARY_FILE_TYPE);
            // 设置缓冲区大小
            ftp.setBufferSize(3072);
            // 设置字符编码
            ftp.setControlEncoding("UTF-8");
            // 构造本地文件对象
            File localFile = new File(localPath + "/" + fileName);
            // 获取文件操作目录下所有文件名称
            String[] remoteNames = ftp.listNames();
            // 循环比对文件名称，判断是否含有当前要下载的文件名
            for (String remoteName: remoteNames) {
                if (fileName.equals(remoteName)) {
                    result = true;
                }
            }
            // 文件名称比对成功时，进入下载流程
            if (result) {
                // 构造文件输出流
                os = new FileOutputStream(localFile);
                // 下载文件
                result = ftp.retrieveFile(fileName, os);
                // 关闭输出流
                os.close();
            }
            // 登出服务器
            ftp.logout();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                // 判断输出流是否存在
                if (null != os) {
                    // 关闭输出流
                    os.close();
                }
                // 判断连接是否存在
                if (ftp.isConnected()) {
                    // 断开连接
                    ftp.disconnect();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }*/

   /* private static List<String> listFileNames(String host, int port, String username, final String password, String dir) {
        List<String> list = new ArrayList<String>();
        ChannelSftp sftp = null;
        Channel channel = null;
        Session sshSession = null;
        try {
            JSch jsch = new JSch();
            jsch.getSession(username, host, port);
            sshSession = jsch.getSession(username, host, port);
            sshSession.setPassword(password);
            Properties sshConfig = new Properties();
            sshConfig.put("StrictHostKeyChecking", "no");
            sshSession.setConfig(sshConfig);
            sshSession.connect();
            LOG.debug("Session connected!");
            channel = sshSession.openChannel("sftp");
            channel.connect();
            LOG.debug("Channel connected!");
            sftp = (ChannelSftp) channel;
            Vector<?> vector = sftp.ls(dir);
            for (Object item:vector) {
                LsEntry entry = (LsEntry) item;
                System.out.println(entry.getFilename());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeChannel(sftp);
            closeChannel(channel);
            closeSession(sshSession);
        }
        return list;
    }

    private static void closeChannel(Channel channel) {
        if (channel != null) {
            if (channel.isConnected()) {
                channel.disconnect();
            }
        }
    }

    private static void closeSession(Session session) {
        if (session != null) {
            if (session.isConnected()) {
                session.disconnect();
            }
        }
    }*/


        /**
         * FTPClient对象
         **/
        private static ChannelSftp ftpClient = null;
        /**
         *
         */
        private static Session sshSession = null;

        /**
         * 连接服务器
         * @param host
         * @param port
         * @param userName
         * @param password
         * @return
         * @throws Exception
         */
        public static ChannelSftp getConnect(String host, String port, String userName, String password)
                throws Exception {
            try {
                JSch jsch = new JSch();
                // 获取sshSession
                sshSession = jsch.getSession(userName, host, Integer.parseInt(port));
                // 新增s密码
                sshSession.setPassword(password);
                Properties sshConfig = new Properties();
                sshConfig.put("StrictHostKeyChecking", "no");
                sshSession.setConfig(sshConfig);
                // 开启sshSession链接
                sshSession.connect();
                // 获取sftp通道
                ftpClient = (ChannelSftp) sshSession.openChannel("sftp");
                // 开启
                ftpClient.connect();
                log.debug("success ..........");
            } catch (Exception e) {
                e.printStackTrace();
                throw new Exception("连接sftp服务器异常。。。。。。。。");
            }
            return ftpClient;
        }

        /**
         * 下载文件
         * @param ftp_path	服务器文件路径
         * @param save_path	下载保存路径
         * @param oldFileName	服务器上文件名
         * @param newFileName	保存后新文件名
         * @throws Exception
         */
        public static void download(String ftp_path, String save_path, String oldFileName, String newFileName)
                throws Exception {
            FileOutputStream fos = null;
            try {
                ftpClient.cd(ftp_path);
                File file = new File(save_path);
                if (!file.exists()) {
                    file.mkdirs();
                }
                String saveFile = save_path + File.separator +  newFileName;
                File file1 = new File(saveFile);
                fos = new FileOutputStream(file1);
                ftpClient.get(oldFileName, fos);
            } catch (Exception e) {
                log.error("下载文件异常............", e);
                throw new SystemException("download file error............");
            } finally {
                if (fos != null) {
                    try {
                        fos.close();
                    } catch (Exception e) {
                        log.error("", e);
                    }
                }
            }
        }

        /**
         * 上传
         * @param upload_path 上传文件路径
         * @param ftp_path	服务器保存路径
         * @param newFileName	新文件名
         * @throws Exception
         */
        public static void uploadFile(String upload_path, String ftp_path, String newFileName) throws Exception {
            FileInputStream fis = null;
            try {
                fis = new FileInputStream(new File(upload_path));
                ftpClient.cd(ftp_path);
                ftpClient.put(fis, newFileName);
            } catch (Exception e) {
                e.printStackTrace();
                throw new Exception("Upload file error.");
            } finally {
                if (fis != null) {
                    try {
                        fis.close();
                    } catch (IOException e) {
                        log.error("", e);
                    }
                }
            }
        }

        /**
         * 关闭
         *
         * @throws Exception
         */
        public static void close() throws Exception {
            log.debug("close............");
            try {
                ftpClient.disconnect();
                sshSession.disconnect();
            } catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
                throw new Exception("close stream error.");
            }
        }
}
