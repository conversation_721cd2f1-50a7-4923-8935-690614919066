package com.wcompass.edgs.core.validation.validator;

import com.wcompass.edgs.core.id.IdNumberParser;
import com.wcompass.edgs.core.validation.constraints.IdNumber;
import com.wcompass.edgs.exception.SystemException;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

/**
 * 身份证号校验器
 *
 * <AUTHOR>
 * @date 2023/2/9
 */
public class IdNumberValidator implements ConstraintValidator<IdNumber, String> {
    @Override
    public boolean isValid(String val, ConstraintValidatorContext context) {
        if (val == null) {
            return true;
        }
        try {
            IdNumberParser.validateIdNumber(val);
            return true;
        } catch (SystemException e) {
            return false;
        }
    }

    @Override
    public void initialize(IdNumber constraintAnnotation) {
        // do nothing
    }
}
