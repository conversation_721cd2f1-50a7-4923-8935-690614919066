package com.wcompass.edgs.utils;

import com.wcompass.edgs.exception.ExceptionEnum;
import com.wcompass.edgs.exception.SystemException;
import org.apache.commons.lang3.time.DateUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 日期操作的工具类，
 * 使用java8日期API编写
 *
 * <AUTHOR>
 * @date Created on 2019/3/20
 */
public class DateUtil {

    /**
     * 最小时间1900-1-31
     */
    private final static long minTimeInMillis = -2206425952001L;

    /**
     * 最大时间2099-12-31
     */
    private final static long maxTimeInMillis = 4102416000000L;


    private static Map<String, DateTimeFormatter> FORMATTER_CACHE = new ConcurrentHashMap<>();

    /**
     * 24小时制小时占位符
     */
    private static String HOURS = "H";

    /**
     * 12小时制小时占位符
     */
    private static String HOURS_12 = "h";

    /**
     * 分钟站位符
     */
    private static String MINUTES = "m";

    /**
     * 秒占位符
     */
    private static String SECONDS = "s";

    /**
     * 中国位于东8区
     */
    private static int ZONE_OFF_SET = 8;

    /**
     * 中国时区偏移
     */
    public static ZoneOffset DEFAULT_ZONE_OFF_SET = ZoneOffset.ofHours(ZONE_OFF_SET);

    /**
     * 获取本周的第一天
     *
     * @return
     */
    public static Date getFirstDayOfCurrentWeek() {

        return getFirstDayOfWeek(LocalDate.now());
    }

    public static Date getFirstDayOfWeek(LocalDate localDate) {
        LocalDate date = localDate;
        DayOfWeek dayOfWeek = date.getDayOfWeek();
        if (dayOfWeek.equals(DayOfWeek.MONDAY)) {
            return toDate(date);
        }
        date = date.minus(dayOfWeek.minus(1).getValue(), ChronoUnit.DAYS);
        return toDate(date);
    }

    public static Date getFirstDayOfWeek(Date date) {
        return getFirstDayOfWeek(toLocalDate(date));
    }

    /**
     * 获取当月的第一天
     *
     * @return
     */
    public static Date getFirstDayOfCurrentMonth() {
        return getFirstDayOfMonth(LocalDate.now());
    }

    public static Date getFirstDayOfMonth(LocalDate localDate) {
        LocalDate date = localDate;
        int dayOfMonth = date.getDayOfMonth();
        date = date.minus(dayOfMonth - 1L, ChronoUnit.DAYS);
        return toDate(date);
    }

    public static Date getFirstDayOfMonth(Date date) {
        return getFirstDayOfMonth(toLocalDate(date));
    }

    /**
     * 获取当年的第一天
     *
     * @return
     */
    public static Date getFirstDayOfCurrentYear() {
        return getFirstDayOfYear(LocalDate.now());
    }

    public static Date getFirstDayOfYear(LocalDate localDate) {
        LocalDate date = localDate;
        int dayOfYear = date.getDayOfYear();
        date = date.minus(dayOfYear - 1L, ChronoUnit.DAYS);
        return toDate(date);
    }

    public static Date getFirstDayOfYear(Date date) {
        return getFirstDayOfYear(toLocalDate(date));
    }

    /**
     * 日期字符串解析
     *
     * @param source  字符串源
     * @param pattern 格式
     * @return
     */
    public static Date parse(String source, String pattern) {
        DateTimeFormatter formatter = getFormatter(pattern);
        if (pattern.contains(HOURS) || pattern.contains(HOURS_12) || pattern.contains(MINUTES)
                || pattern.contains(SECONDS)) {
            return toDate(LocalDateTime.parse(source, formatter));
        } else {
            return toDate(LocalDate.parse(source, formatter));
        }
    }

    /**
     * 格式化日期
     *
     * @return
     */
    public static String format(Date date, String pattern) {
        DateTimeFormatter formatter = getFormatter(pattern);
        return formatter.format(toLocalDateTime(date));
    }

    public static String format(LocalDate localDate, String pattern) {
        DateTimeFormatter formatter = getFormatter(pattern);
        return formatter.format(localDate);
    }

    public static String format(LocalDateTime localDateTime, String pattern) {
        return format(toDate(localDateTime), pattern);
    }

    public static String format(LocalTime localTime, String pattern) {
        DateTimeFormatter formatter = getFormatter(pattern);
        return formatter.format(localTime);
    }

    /**
     * 偏移指定时间段的时间，不改变原来的时间，返回新的时间对象
     *
     * @param date
     * @param duration
     * @param chronoUnit
     * @return
     */
    public static Date offSet(Date date, long duration, ChronoUnit chronoUnit) {
        LocalDateTime localDateTime = toLocalDateTime(date);
        return toDate(localDateTime.plus(duration, chronoUnit));
    }

    public static LocalDateTime offSet(LocalDateTime localDateTime, long duration, ChronoUnit chronoUnit) {
        return localDateTime.plus(duration, chronoUnit);
    }

    /**
     * 获得两个日期之间的时间间隔
     *
     * @param start
     * @param end
     * @param chronoUnit
     * @return 返回以chronoUnit为时间单位的数值
     */
    public static long between(Date start, Date end, ChronoUnit chronoUnit) {
        return chronoUnit.between(toLocalDateTime(start), toLocalDateTime(end));
    }

    /**
     * 获取系统所在时区
     *
     * @return
     */
    private static ZoneId getSystemDefaultZoneId() {
        return ZoneOffset.systemDefault();
    }

    public static Date toDate(Instant instant) {
        return Date.from(instant);
    }

    public static Date toDate(LocalDateTime localDateTime) {
        return toDate(localDateTime.atZone(getSystemDefaultZoneId()).toInstant());
    }

    public static long toMilli(String str, String pattern) {
        Date parse = DateUtil.parse(str, pattern);
        LocalDateTime localDateTime = DateUtil.toLocalDateTime(parse);
        return localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    public static Date toDate(LocalDate localDate) {
        return toDate(localDate.atStartOfDay(getSystemDefaultZoneId()).toInstant());
    }

    public static LocalDateTime toLocalDateTime(Date date) {
        return LocalDateTime.ofInstant(date.toInstant(), getSystemDefaultZoneId());
    }

    public static LocalDate toLocalDate(Date date) {
        return LocalDate.from(toLocalDateTime(date));
    }

    public static LocalTime toLocalTime(Date date) {
        DateTimeFormatter formatter = getFormatter("HH:mm:ss");
        return LocalTime.parse(LocalTime.from(toLocalDateTime(date)).format(formatter));
    }

    public static DateTimeFormatter getFormatter(String pattern) {
        DateTimeFormatter formatter = FORMATTER_CACHE.get(pattern);
        if (formatter == null) {
            formatter = DateTimeFormatter.ofPattern(pattern);
            FORMATTER_CACHE.put(pattern, formatter);
        }
        return formatter;
    }

    public static long currentTimeMillis() {
        return System.currentTimeMillis();
    }

    public static Date ofTime(long timeMillis) {
        return new Date(timeMillis);
    }

    /**
     * 获取下周的第一天的日期
     *
     * @return java.util.Date
     * @Author: SANMEL
     * @Date: 2019/8/16 17:04
     */
    public static Date getFirstDayOfNextWeek() {
        return getFirstDayOfNextWeek(LocalDate.now());
    }

    public static Date getFirstDayOfNextWeek(LocalDate localDate) {
        LocalDate date = localDate;
        DayOfWeek dayOfWeek = date.getDayOfWeek();
        if (dayOfWeek.equals(DayOfWeek.MONDAY)) {
            date = date.plusWeeks(1);
            return toDate(date);
        }
        date = date.minus(dayOfWeek.minus(1).getValue(), ChronoUnit.DAYS);
        date = date.plusWeeks(1);
        return toDate(date);
    }

    /**
     * 获取下一个月的第一天的日期
     *
     * @return java.util.Date
     * @Author: SANMEL
     * @Date: 2019/8/16 17:04
     */
    public static Date getFirstDayOfNextMonth() {
        return getFirstDayOfNextMonth(LocalDate.now());
    }

    public static Date getFirstDayOfNextMonth(LocalDate localDate) {
        LocalDate date = localDate;
        int dayOfMonth = date.getDayOfMonth();
        date = date.minus(dayOfMonth - 1L, ChronoUnit.DAYS);
        date = date.plusMonths(1);
        return toDate(date);
    }


    /**
     * 获取下一年的第一天的日期
     *
     * @return java.util.Date
     * @Author: SANMEL
     * @Date: 2019/8/16 17:04
     */
    public static Date getFirstDayOfNextYear() {
        return getFirstDayOfNextYear(LocalDate.now());
    }

    public static Date getFirstDayOfNextYear(LocalDate localDate) {
        LocalDate date = localDate;
        int dayOfYear = date.getDayOfYear();
        date = date.minus(dayOfYear - 1L, ChronoUnit.DAYS);
        date = date.plusYears(1);
        return toDate(date);
    }

    /**
     * 获取当前日期
     *
     * @return java.util.Date
     * @Author: SANMEL
     * @Date: 2019/8/16 17:04
     */
    public static Date localDate() {
        return toDate(LocalDate.now());
    }

    public static Date now() {
        return new Date();
    }

    /**
     * 获取当前时间
     *
     * @return java.util.Date
     * @Author: SANMEL
     * @Date: 2019/9/2 18:55
     */
    public static Date localDateTime() {
        return toDate(LocalDateTime.now());
    }

    /**
     * 获取（包括今天）多少天以前的日期
     *
     * @param days
     * @return
     */
    public static Date getDaysAgo(Integer days) {
        LocalDate date = LocalDate.now();
        if (days != null && days >= 2) {
            date = date.minusDays(days - 1L);
        }
        return toDate(date);
    }

    /**
     * @param src
     * @return String:yyyy-MM-dd
     */
    public static String formatDate(Date src) {
        return format(src, "yyyy-MM-dd");
    }

    /**
     * @param src
     * @return String:yyyy-MM-dd HH:mm:ss
     */
    public static String formatDatetime(Date src) {
        return format(src, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * @param src
     * @return String:yyyyMMddHHmmssSSS
     */
    public static String formatDatetime2(Date src) {

        return format(src, "yyyyMMddHHmmssSSS");
    }

    /**
     * @param src
     * @return String:yyyyMMddHHmmss
     */
    public static String formatDatetime3(Date src) {
        return format(src, "yyyyMMddHHmmss");
    }

    /**
     * @param src
     * @return String:yyyyMMddHH
     */
    public static String formatDatetime4(Date src) {
        return format(src, "yyyyMMddHH");
    }

    /**
     * @param src
     * @return String:yyyyMMdd
     */
    public static String formatDatetime5(Date src) {
        return format(src, "yyyyMMdd");
    }

    /**
     * @param src
     * @return String: HH:mm
     */
    public static String formatDatetime6(Date src) {
        return format(src, "HH:mm");
    }

    /**
     * @param src
     * @return String: HH:mm
     */
    public static String formatDatetime7(Date src) {
        return format(src, "HH" + ":00");
    }

    /**
     * @param src
     * @return Date:yyyy-MM-dd
     */
    public static Date parseDate(String src) {
        return parse(src, "yyyy-MM-dd");
    }

    /**
     * @param src
     * @return Date:yyyyMMddhHH
     */
    public static Date parseDate2(String src) {
        return parse(src, "yyyyMMddHH");
    }

    /**
     * @param src
     * @return Date:yyyy-MM-dd HH:mm:ss
     */
    public static Date parseDatetime(String src) {
        return parse(src, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 将毫秒转化（转换成*天*小时*分钟*秒）
     *
     * @param millis
     * @return
     * @throws ParseException
     */
    public static String parseMillis(long millis) {
        Duration duration = Duration.ofMillis(millis);
        long days = duration.toDays();
        duration = duration.minusDays(days);
        long hours = duration.toHours();
        duration = duration.minusHours(hours);
        long minutes = duration.toMinutes();
        duration = duration.minusMinutes(minutes);
        float secs = duration.toMillis() / 1000f;
        StringBuilder sb = new StringBuilder();
        if (days > 0) {
            sb.append(days).append("天");
        }
        if (hours > 0) {
            sb.append(hours).append("小时");
        }
        if (minutes > 0) {
            sb.append(minutes).append("分");
        }
        if (secs > 0) {
            sb.append(secs).append("秒");
        }
        return sb.toString();
    }

    /**
     * 将毫秒转化（转换成*天*小时*分钟*秒）
     * 秒取整数显示
     */
    public static String parseMillis2(long millis) {
        Duration duration = Duration.ofMillis(millis);
        long days = duration.toDays();
        duration = duration.minusDays(days);
        long hours = duration.toHours();
        duration = duration.minusHours(hours);
        long minutes = duration.toMinutes();
        duration = duration.minusMinutes(minutes);
        int secs = (int) (duration.toMillis() / 1000);
        StringBuilder sb = new StringBuilder();
        if (days > 0) {
            sb.append(days).append("天");
        }
        if (hours > 0) {
            sb.append(hours).append("小时");
        }
        if (minutes > 0) {
            sb.append(minutes).append("分");
        }
        if (secs > 0) {
            sb.append(secs).append("秒");
        }
        return sb.toString();
    }

    /**
     * 获取今天的日期 开始Date
     *
     * @Author: SANMEL
     * @Date: 2019/11/12 19:16
     */
    public static Date getTodayStartDate() {
        return localDate();
    }

    /**
     * 获取下一天的日期 开始Date
     *
     * @param localDate
     * @return java.util.Date
     * @Author: SANMEL
     * @Date: 2019/11/12 19:21
     */
    public static Date getNextDayStartDate(LocalDate localDate) {
        LocalDate date = localDate;
        date = date.plusDays(1);
        return toDate(date);
    }

    /**
     * 获取某个日期后的日期,23:59:59
     */
    public static Date getDayAfterSomeDate(Integer days) {
        LocalDate date = LocalDate.now();
        date = date.plusDays(days);
        LocalDateTime localDateTime = date.atTime(23, 59, 59);
        return toDate(localDateTime);
    }

    /**
     * 获取昨天的日期 开始Date
     *
     * @param localDate
     * @return java.util.Date
     */
    public static Date getYesterdayDate(LocalDate localDate) {
        LocalDate date = localDate;
        date = date.minusDays(1);
        return toDate(date);
    }

    /**
     * 获取明天的日期 开始Date
     *
     * @return java.util.Date
     * @Author: SANMEL
     * @Date: 2019/11/12 19:21
     */
    public static Date getNextDayStartDate() {
        return getNextDayStartDate(LocalDate.now());
    }


    /**
     * 将开始时间字符串转为Date(一天中的开始)
     *
     * @param startDate 开始时间字符串
     * @return java.util.Date
     * @Author: hl
     * @Date: 2019/11/16
     */
    public static Date transStrToOneDayStartDate(String startDate) {
        Date date = null;
        if (startDate != null && !startDate.isEmpty()) {
            try {
                date = DateUtil.parse(startDate + " 00:00:00.000", "yyyy-MM-dd HH:mm:ss.SSS");
            } catch (Exception e) {
                throw new SystemException(ExceptionEnum.DATE_FORMAT_ERROR);
            }
        }
        return date;
    }

    /**
     * 将结束时间字符串转为Date（一天中的结束）
     *
     * @param endDate 结束时间字符串
     * @return java.util.Date
     * @Author: hl
     * @Date: 2019/11/16
     */
    public static Date transStrToOneDayEndDate(String endDate) {
        Date date = null;
        if (endDate != null && !endDate.isEmpty()) {
            try {
                date = DateUtil.parse(endDate + " 23:59:59.999", "yyyy-MM-dd HH:mm:ss.SSS");
            } catch (Exception e) {
                throw new SystemException(ExceptionEnum.DATE_FORMAT_ERROR);
            }
        }
        return date;
    }

    /**
     * 获取某一日期前多少天的日期
     *
     * @param date
     * @param days
     * @return
     */
    public static Date getDateBeforeDays(Date date, int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DATE, days);
        return calendar.getTime();
    }

    public static int getDaysOfCurrentMonth() {
        Calendar aCalendar = Calendar.getInstance(Locale.CHINA);
        return aCalendar.getActualMaximum(Calendar.DATE);
    }

    /**
     * 获取小时列表
     *
     * @return
     */
    public static LinkedList<String> getHourLinkedList() {
        LinkedList<String> list = new LinkedList<>();
        list.add("0:00");
        list.add("1:00");
        list.add("2:00");
        list.add("3:00");
        list.add("4:00");
        list.add("5:00");
        list.add("6:00");
        list.add("7:00");
        list.add("8:00");
        list.add("9:00");
        list.add("10:00");
        list.add("11:00");
        list.add("12:00");
        list.add("13:00");
        list.add("14:00");
        list.add("15:00");
        list.add("16:00");
        list.add("17:00");
        list.add("18:00");
        list.add("19:00");
        list.add("20:00");
        list.add("21:00");
        list.add("22:00");
        list.add("23:00");
        return list;
    }

    public static LinkedList<String> getDateList(LocalDate startDate, LocalDate endDate) {
        LinkedList<String> list = new LinkedList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        while (!startDate.isAfter(endDate)) {
            list.add(startDate.format(formatter));
            startDate = startDate.plusDays(1);
        }
        return list;
    }

    public static LinkedList<String> getThisMonthDays() {
        LinkedList<String> list = new LinkedList<>();
        YearMonth yearMonth = YearMonth.now(); // 当前年月
        int daysInMonth = yearMonth.lengthOfMonth(); // 本月天数
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd"); // 格式 MM-dd
        for (int day = 1; day <= daysInMonth; day++) {
            list.add(yearMonth.atDay(day).format(formatter));
        }
        return list;
    }

    public static List<String> getLastMonthDays() {
        List<String> list = new ArrayList<>();
        YearMonth lastMonth = YearMonth.now().minusMonths(1); // 上个月
        int daysInMonth = lastMonth.lengthOfMonth(); // 上月天数
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd"); // MM-dd格式
        for (int day = 1; day <= daysInMonth; day++) {
            list.add(lastMonth.atDay(day).format(formatter));
        }
        return list;
    }

    /**
     * 将时间戳转换为 yyyy-MM-dd HH:mm:ss 格式
     *
     * @param timestamp 时间戳
     * @return
     */
    public static String timestampTransfer(String timestamp) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(Long.parseLong(timestamp));
    }

    /**
     * 将时间戳转换为 yyyy-MM-dd HH:mm:ss.SSS 格式
     *
     * @param timestamp 时间戳
     * @return
     */
    public static String timestampTransfer2(String timestamp) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        return sdf.format(Long.parseLong(timestamp));
    }

    public static Date beginDateTime(Date startTime) {
        return toDate(toLocalDate(startTime));
    }

    /**
     * 判断 String类型的时间格式是否满足 format形式
     *
     * @param date
     * @return
     */
    public static Boolean dateTimeFormat(String date) {
        if (StringUtil.isBlank(date)) {
            return false;
        }
        try {
            parseDatetime(date);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    public static Date endDateTime(Date startTime) {
        return toDate(LocalDateTime.of(toLocalDate(startTime), LocalTime.MAX));
    }

    /**
     * 格式化毫秒
     *
     * @param milliseconds
     * @return
     */
    public static String formatMilliseconds(long milliseconds) {
        long seconds = milliseconds / 1000;
        long millis = milliseconds % 1000;
        if (seconds > 0 && millis > 0) {
            return StringUtil.format("{}.{}s", seconds, millis);
        } else if (seconds > 0) {
            return StringUtil.format("{}s", seconds);
        } else {
            return StringUtil.format("{}ms", millis);
        }
    }

    public static void main(String[] args) {
//        String str = "Tue Jun 08 02:34:11 UTC 2021";
//        System.out.println(new Date());
////        System.out.println(parse("Tue Jun 08 11:10:00 CST 2021", "EEE MMM dd HH:mm:ss zzz yyyy"));
//        SimpleDateFormat us = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.forLanguageTag("us"));
//        Date parse = us.parse(str);
//        System.out.println(parse);

    }

    /**
     * 将UTC转换成指定时区时间
     *
     * @param date 待转换时间字符串,格式：2024-04-22T06:02:29.000000+0000
     * @param zone 指定时区，北京时间：ZoneId.of("Asia/Shanghai")
     */
    public static String formatUTCWithZone(String date, ZoneId zone) {
        ZonedDateTime parse2 = ZonedDateTime.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSSZ"));
        ZonedDateTime zonedDateTime = parse2.withZoneSameInstant(zone);
        return zonedDateTime.toString();
    }
}
