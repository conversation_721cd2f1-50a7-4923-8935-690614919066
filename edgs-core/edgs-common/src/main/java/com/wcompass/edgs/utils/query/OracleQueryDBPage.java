package com.wcompass.edgs.utils.query;

import com.wcompass.edgs.core.Page;
import com.wcompass.edgs.core.extract.MetaModel;
import com.wcompass.edgs.exception.SystemException;
import com.wcompass.edgs.utils.CollectionUtil;
import com.wcompass.edgs.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR>
 * @date 2021/5/11
 */
@Slf4j
public class OracleQueryDBPage extends BaseQueryDBPage {

    @Override
    public Page<String> querySchema(Page<String> page, Connection connection, String keyword) {
        long size = page.getSize();
        long current = page.getCurrent();
        String keywordSql = "";
        if (StringUtil.isNotBlank(keyword)) {
            keywordSql = " and upper(USERNAME) like '%'|| upper('" + keyword + "')||'%'";
        }
        String countSql = "select count(1) from ALL_USERS where 1 = 1 " + keywordSql;
        log.info(countSql);
        String querySql = "select t2.USERNAME as schemaName from (select USERNAME, ROWNUM as RN from (select USERNAME from ALL_USERS union all select 'PUBLIC' from dual where 1=1 " + keywordSql + " order by USERNAME )t1 where ROWNUM <= ?" +
                "  ) t2  where t2.RN >= ?";
        log.info(querySql);
        PreparedStatement countStatement = null;
        ResultSet countResultSet = null;
        PreparedStatement queryStatement = null;
        ResultSet queryResultSet = null;
        try {
            countStatement = connection.prepareStatement(countSql);
            countResultSet = countStatement.executeQuery();
            countResultSet.next();
            long total = countResultSet.getLong(1);
            page.setTotal(total);
            if (total == 0) {
                return page;
            }
            // oracle 从1开始
            int start = (int) (size * (current - 1)) + 1;
            int end = (int) (start + size) - 1;
            if (start > total) {
                return page;
            }

            queryStatement = connection.prepareStatement(querySql);
            queryStatement.setInt(1, end);
            queryStatement.setInt(2, start);
            queryResultSet = queryStatement.executeQuery();
            List<String> schemaList = new ArrayList<>();
            while (queryResultSet.next()) {
                schemaList.add(queryResultSet.getString("schemaName"));
            }
            page.setRecords(schemaList);
        } catch (SQLException e) {
            log.error("", e);
            throw new SystemException("查询异常");
        } finally {
            if (countResultSet != null) {
                try {
                    countResultSet.close();
                } catch (SQLException e) {
                    log.error("", e);
                }
            }
            if (countStatement != null) {
                try {
                    countStatement.close();
                } catch (SQLException e) {
                    log.error("", e);
                }
            }

            if (queryResultSet != null) {
                try {
                    queryResultSet.close();
                } catch (SQLException e) {
                    log.error("", e);
                }
            }
            if (queryStatement != null) {
                try {
                    queryStatement.close();
                } catch (SQLException e) {
                    log.error("", e);
                }
            }
        }
        return page;
    }

    @Override
    public Page<Table> queryTable(Page<Table> page, Connection connection, String schema, int type, String keyword, List<String> tableAndView) {
        long size = page.getSize();
        long current = page.getCurrent();
        if (StringUtil.isNotEmpty(schema)) {
            String typesql = "";
            String keywordSql = "";
            String tableNameSql = "";
            if (1 == type) {
                typesql = " and t1.TABLE_TYPE ='TABLE' ";
            } else if (2 == type) {
                typesql = " and t1.TABLE_TYPE = 'VIEW'";
            }
            if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(tableAndView) && tableAndView.size() > 0) {
                String tableNames = StringUtil.join(tableAndView, "','");
                tableNameSql = " and t1.TABLE_NAME not in('" + tableNames + "') ";
            }

            if (StringUtil.isNotBlank(keyword)) {
                keywordSql = " and (upper(TABLE_NAME) like '%'|| upper('" + keyword + "')||'%' or (upper(COMMENTS) like '%'|| upper('" + keyword + "') || '%'))";
            }
            String countSql = "select count(1) from DBA_TAB_COMMENTS t1 inner join ALL_OBJECTS t2 on t1.OWNER = t2.OWNER and t1.TABLE_NAME = t2.OBJECT_NAME " +
                    "   and t1.TABLE_TYPE = t2.OBJECT_TYPE where  t1.OWNER = '" + schema + "'" + typesql + tableNameSql + keywordSql;
            log.info(countSql);
            String querySql = "select OWNER,TABLE_NAME,TABLE_TYPE,COMMENTS  from ( select t1.OWNER,t1.TABLE_NAME,t1.TABLE_TYPE,t1.COMMENTS,ROWNUM as RN from (select " +
                    "t1.OWNER,t1.TABLE_NAME,t1.TABLE_TYPE,t1.COMMENTS from ALL_TAB_COMMENTS t1 inner join ALL_OBJECTS t2 on t1.OWNER = t2.OWNER and t1.TABLE_NAME = t2.OBJECT_NAME\n" +
                    "            and t1.TABLE_TYPE = t2.OBJECT_TYPE where  t1.OWNER = '" + schema + "'" + typesql + tableNameSql + keywordSql +
                    " order by t1.TABLE_NAME )t1 where ROWNUM <= ?  ) t2  where t2.RN >= ?";
            log.info(querySql);
            PreparedStatement countStatement = null;
            ResultSet countResultSet = null;
            PreparedStatement queryStatement = null;
            ResultSet queryResultSet = null;
            try {
                countStatement = connection.prepareStatement(countSql);
                countResultSet = countStatement.executeQuery();
                countResultSet.next();
                long total = countResultSet.getLong(1);
                page.setTotal(total);
                if (total == 0) {
                    return page;
                }
                // oracle 从1开始
                int start = (int) (size * (current - 1)) + 1;
                int end = (int) (start + size) - 1;
                if (start > total) {
                    return page;
                }
                queryStatement = connection.prepareStatement(querySql);
                queryStatement.setInt(1, end);
                queryStatement.setInt(2, start);
                queryResultSet = queryStatement.executeQuery();
                List<Table> tableList = new ArrayList<>();
                while (queryResultSet.next()) {
                    Table table = new Table();
                    table.setSchemaName(queryResultSet.getString("OWNER"));
                    if (queryResultSet.getString("TABLE_TYPE").contains("TABLE")) {
                        table.setType("Table");
                    } else {
                        table.setType("View");
                    }
                    table.setTableName(queryResultSet.getString("TABLE_NAME"));
                    table.setTableCnName(StringUtil.isEmpty(queryResultSet.getString("COMMENTS")) ? queryResultSet.getString("TABLE_NAME") : queryResultSet.getString("COMMENTS"));
                    tableList.add(table);
                }
                page.setRecords(tableList);
            } catch (SQLException e) {
                log.error("", e);
                throw new SystemException("查询异常");
            } finally {
                if (countResultSet != null) {
                    try {
                        countResultSet.close();
                    } catch (SQLException e) {
                        log.error("", e);
                    }
                }
                if (countStatement != null) {
                    try {
                        countStatement.close();
                    } catch (SQLException e) {
                        log.error("", e);
                    }
                }

                if (queryResultSet != null) {
                    try {
                        queryResultSet.close();
                    } catch (SQLException e) {
                        log.error("", e);
                    }
                }
                if (queryStatement != null) {
                    try {
                        queryStatement.close();
                    } catch (SQLException e) {
                        log.error("", e);
                    }
                }
            }
        }

        return page;
    }

    @Override
    public Page<Column> queryColumn(Page<Column> page, Connection connection, String schema, List<String> tableAndView, String keyword) throws SQLException {
        long size = page.getSize();
        long current = page.getCurrent();

        if (StringUtil.isNotEmpty(schema) && CollectionUtil.isNotEmpty(tableAndView)) {
            String keywordSql = "";
            String tableNames = StringUtil.join(tableAndView, "','");
            String tableNameSql = "'" + tableNames + "'";

            if (StringUtil.isNotBlank(keyword)) {
                keywordSql = " and  ((upper(COLUMN_NAME)) like '%'||upper('" + keyword + "')||'%' or" +
                        " (upper(t1.COMMENTS) like '%'||upper('" + keyword + "')||'%' ))";
            }
            String countSql = "select count(0) from ALL_COL_COMMENTS t1 where t1.OWNER = '" +
                    schema + "' and t1.TABLE_NAME in(" + tableNameSql + ")" + keywordSql;
            log.info(countSql);

            String querySql = "select OWNER,TABLE_NAME,TABLE_TYPE,COLUMN_NAME,COMMENTS from(select t3.*,ROWNUM as RN from (select t1.TABLE_NAME,t1.OWNER,t1.COLUMN_NAME,t1.COMMENTS,t2.TABLE_TYPE from ALL_COL_COMMENTS t1 " +
                    " inner join ALL_TAB_COMMENTS t2 on t1.TABLE_NAME = t2.TABLE_NAME and t1.OWNER = t2.OWNER " +
                    " inner join ALL_TAB_COLUMNS t3 on t1.OWNER = t3.OWNER and t1.COLUMN_NAME = t3.COLUMN_NAME and t1.TABLE_NAME = t3.TABLE_NAME " +
                    "where t1.OWNER = '" + schema + "' and t1.TABLE_NAME in(" + tableNameSql + ")" +
                    keywordSql + " order by   t1.TABLE_NAME asc , t3.COLUMN_ID  asc )t3 where  ROWNUM <= ?  ) t4  where t4.RN >= ?";
            log.info(querySql);

            PreparedStatement countStatement = null;
            ResultSet countResultSet = null;
            PreparedStatement queryStatement = null;
            ResultSet queryResultSet = null;
            try {
                countStatement = connection.prepareStatement(countSql);
                countResultSet = countStatement.executeQuery();
                countResultSet.next();
                long total = countResultSet.getLong(1);
                page.setTotal(total);
                if (total == 0) {
                    return page;
                }
                // oracle 从1开始
                int start = (int) (size * (current - 1)) + 1;
                int end = (int) (start + size) - 1;
                if (start > total) {
                    return page;
                }
                queryStatement = connection.prepareStatement(querySql);
                queryStatement.setInt(1, end);
                queryStatement.setInt(2, start);
                queryResultSet = queryStatement.executeQuery();
                List<Column> columnList = new ArrayList<>();
                while (queryResultSet.next()) {
                    Column column = new Column();
                    column.setSchemaName(queryResultSet.getString("OWNER"));
                    if (queryResultSet.getString("TABLE_TYPE").contains("TABLE")) {
                        column.setType("Table");
                    } else {
                        column.setType("View");
                    }
                    column.setTableName(queryResultSet.getString("TABLE_NAME"));
                    column.setColumnName(queryResultSet.getString("COLUMN_NAME"));
                    column.setColumnCnName(StringUtil.isEmpty(queryResultSet.getString("COMMENTS")) ? queryResultSet.getString("COLUMN_NAME") : queryResultSet.getString("COMMENTS"));
                    columnList.add(column);
                }
                page.setRecords(columnList);
            } catch (SQLException e) {
                log.error("", e);
                throw new SystemException("查询异常");
            } finally {
                if (countResultSet != null) {
                    try {
                        countResultSet.close();
                    } catch (SQLException e) {
                        log.error("", e);
                    }
                }
                if (countStatement != null) {
                    try {
                        countStatement.close();
                    } catch (SQLException e) {
                        log.error("", e);
                    }
                }

                if (queryResultSet != null) {
                    try {
                        queryResultSet.close();
                    } catch (SQLException e) {
                        log.error("", e);
                    }
                }
                if (queryStatement != null) {
                    try {
                        queryStatement.close();
                    } catch (SQLException e) {
                        log.error("", e);
                    }
                }
            }
        }
        return page;
    }

    @Override
    public Page<Function> queryFunction(Page<Function> page, Connection connection, String schema, String keyword) {
        long size = page.getSize();
        long current = page.getCurrent();

        if (StringUtil.isNotBlank(schema)) {
            String keywordSql = "";
            if (StringUtil.isNotBlank(keyword)) {
                keywordSql = "  and upper(t1.OWNER||'.'||t1.OBJECT_NAME || '(' || NVL(t2.paramNames, '') || ')')  " +
                        "like '%'|| upper('" + keyword + "')||'%'";
            }

            String countSql = "select count(1) from  ALL_OBJECTS t1 " +
                    "left join (select OBJECT_ID,\n" +
                    "                           listagg(ARGUMENT_NAME, ',') within group (order by SUBPROGRAM_ID) as paramNames,\n" +
                    "                           listagg(DATA_TYPE, ',') within group (order by SUBPROGRAM_ID)     as paramType,\n" +
                    "                           listagg(IN_OUT, ',') within group ( order by SUBPROGRAM_ID)       as inputType\n" +
                    "                    from ALL_ARGUMENTS  where owner = '" + schema + "' \n" +
                    "                     and  ARGUMENT_NAME is not null group by OBJECT_ID) t2 on t1.OBJECT_ID = t2.OBJECT_ID\n" +
                    "where t1.OWNER = '" + schema + "' " +
                    "  and t1.OBJECT_TYPE in ('PROCEDURE', 'FUNCTION') " + keywordSql;
            log.info(countSql);
            String querySql = "select *  from (\n" +
                    "select t3.*,ROWNUM  as RN from (\n" +
                    "select t1.OWNER||'.'||t1.OBJECT_NAME || '(' || NVL(t2.paramNames, '') || ')'          as functionName,\n" +
                    "       t1.OBJECT_TYPE                                                  as functionType,\n" +
                    "       DBMS_METADATA.GET_DDL(t1.OBJECT_TYPE, t1.OBJECT_NAME, t1.OWNER) as ddl,\n" +
                    "       t2.paramNames,\n" +
                    "       t2.paramType,\n" +
                    "       t2.inputType\n" +
                    "from ALL_OBJECTS t1 \n" +
                    "         left join (select OBJECT_ID,\n" +
                    "                           listagg(ARGUMENT_NAME, ',') within group (order by SUBPROGRAM_ID) as paramNames,\n" +
                    "                           listagg(DATA_TYPE, ',') within group (order by SUBPROGRAM_ID)     as paramType,\n" +
                    "                           listagg(IN_OUT, ',') within group ( order by SUBPROGRAM_ID)       as inputType\n" +
                    "                    from ALL_ARGUMENTS     where owner = '" + schema + "' \n" +
                    "         and  ARGUMENT_NAME is not null   group by OBJECT_ID) t2 on t1.OBJECT_ID = t2.OBJECT_ID\n" +
                    "where t1.OWNER = '" + schema + "' " +
                    "  and t1.OBJECT_TYPE in ('PROCEDURE', 'FUNCTION') " + keywordSql + " )t3 where ROWNUM <= ?) t4 where t4.RN >= ?";
            log.info(querySql);

            PreparedStatement countStatement = null;
            ResultSet countResultSet = null;
            PreparedStatement queryStatement = null;
            ResultSet queryResultSet = null;
            try {
                countStatement = connection.prepareStatement(countSql);
                countResultSet = countStatement.executeQuery();
                countResultSet.next();
                long total = countResultSet.getLong(1);
                page.setTotal(total);
                if (total == 0) {
                    return page;
                }
                // oracle 从1开始
                int start = (int) (size * (current - 1)) + 1;
                int end = (int) (start + size) - 1;
                if (start > total) {
                    return page;
                }
                queryStatement = connection.prepareStatement(querySql);
                queryStatement.setInt(1, end);
                queryStatement.setInt(2, start);
                queryResultSet = queryStatement.executeQuery();
                List<Function> functionList = new ArrayList<>();
                while (queryResultSet.next()) {
                    Function function = new Function();
                    function.setFunctionName(queryResultSet.getString("FUNCTIONNAME"));
                    function.setDdl(queryResultSet.getString("DDL"));
                    String param = queryResultSet.getString("PARAMTYPE");
                    String proargnames = queryResultSet.getString("PARAMNAMES");
                    String functionType = queryResultSet.getString("FUNCTIONTYPE");
                    String inputType = queryResultSet.getString("INPUTTYPE");
                    List<FunctionParam> functionParamList = new ArrayList<>();
                    if (StringUtil.isNotBlank(proargnames)) {
                        String[] proargnamesSplit = proargnames.split(",");
                        String[] paramSplit = param.split(",");
                        String[] inputTypeSplit = inputType.split(",");
                        for (int i = 0; i < proargnamesSplit.length; i++) {
                            FunctionParam functionParam = new FunctionParam();
                            functionParam.setParamName(proargnamesSplit[i]);
                            functionParam.setParamType(paramSplit[i]);
                            functionParam.setInputStatus(inputTypeSplit[i].contains("IN"));
                            functionParamList.add(functionParam);
                        }

                    }
                    function.setFunctionType(MetaModel.FUNCTION.toUpperCase(Locale.ROOT).equals(functionType) ?
                            MetaModel.FUNCTION : MetaModel.PROCEDURE);
                    function.setParams(functionParamList);
                    function.setSql(getSql(function.getFunctionType()));
                    functionList.add(function);
                }
                page.setRecords(functionList);
            } catch (SQLException e) {
                log.error("", e);
                throw new SystemException("查询异常");
            } finally {
                if (countResultSet != null) {
                    try {
                        countResultSet.close();
                    } catch (SQLException e) {
                        log.error("", e);
                    }
                }
                if (countStatement != null) {
                    try {
                        countStatement.close();
                    } catch (SQLException e) {
                        log.error("", e);
                    }
                }

                if (queryResultSet != null) {
                    try {
                        queryResultSet.close();
                    } catch (SQLException e) {
                        log.error("", e);
                    }
                }
                if (queryStatement != null) {
                    try {
                        queryStatement.close();
                    } catch (SQLException e) {
                        log.error("", e);
                    }
                }
            }
        }
        return page;
    }

    private String getSql(String functionType) {
        String sql = "";
        if (MetaModel.FUNCTION.equals(functionType)) {
            sql = " select ?  from dual ";
        } else if (MetaModel.PROCEDURE.equals(functionType)) {
            sql = " call ? ";
        }

        return sql;
    }
}
