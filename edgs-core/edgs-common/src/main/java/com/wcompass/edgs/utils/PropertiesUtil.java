//package com.wcompass.edgs.utils;
//
//import com.wcompass.edgs.base.SpringContextHolder;
//
//import java.io.IOException;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.ResourceBundle;
//import java.util.stream.Stream;
//
///**
// * 资源文件properties读取工具,读取resources文件夹下的资源文件
// * 环境配置文件config-{profile}.properties会覆盖config.properties的值
// *
// * <AUTHOR>
// * @date 2019/01/18
// */
//public class PropertiesUtil {
//
//    /**
//     * 缓存打开的多个文件
//     */
//    private static HashMap<String, PropertiesUtil> configMap = new HashMap<>();
//
//    /**
//     * 打开文件时间，判断超时使用
//     */
//    private Date loadTime;
//
//    private ResourceBundle resourceBundle;
//
//    /**
//     * 环境相关的资源文件
//     */
//    private ResourceBundle[] profileResourceBundles;
//
//    /**
//     * 默认资源文件名称
//     */
//    private static final String DEFAULT_NAME = "config";
//
//    private String[] profiles;
//
//    /**
//     * 缓存时间
//     */
//    private static final int TIME_OUT = 60 * 1000;
//
//    private PropertiesUtil(String name) {
//        this.loadTime = new Date();
//        // 默认读取第一配置文件
//        this.resourceBundle = ResourceBundle.getBundle(name);
//        loadActiveProfile();
//        if (this.profiles == null || this.profiles.length == 0) {
//            // 默认是开发环境
//            this.profiles = new String[]{ "dev" };
//        }
//        this.profileResourceBundles = Stream.of(profiles).map(profile -> ResourceBundle.getBundle(StringUtil.join(name, "-", profile)))
//                .toArray(ResourceBundle[]::new);
//    }
//
//    private void loadActiveProfile() {
//        this.profiles = SpringContextHolder.getActiveProfile();
//    }
//
//
//    public static synchronized PropertiesUtil getDefaultResource() {
//        return getResource(DEFAULT_NAME);
//    }
//
//    public static synchronized PropertiesUtil getResource(String name) {
//        PropertiesUtil conf = configMap.get(name);
//        if (null == conf) {
//            conf = new PropertiesUtil(name);
//            configMap.put(name, conf);
//        }
//        // 判断是否打开的资源文件是否超时1分钟
//        if ((System.currentTimeMillis() - conf.getLoadTime().getTime()) > TIME_OUT) {
//            conf = new PropertiesUtil(name);
//            configMap.put(name, conf);
//        }
//        return conf;
//    }
//
//    /**
//     * 根据key读取value
//     *
//     * @param key
//     * @return
//     */
//    public String get(String key) {
//        return get(key, "");
//    }
//
//    public String get(String key, String defaultValue) {
//        String value = null;
//        try {
//            value = loadSource(key);
//        } catch (Exception e) {
//        }
//        if (StringUtil.isBlank(value)) {
//            try {
//                value = resourceBundle.getString(key);
//            } catch (Exception e) {
//            }
//        }
//        if (value == null && defaultValue != null) {
//            value = defaultValue;
//        }
//        return value;
//    }
//
//    private String loadSource(String key) {
//        int len = profileResourceBundles.length;
//        for (int i = len - 1; i > -1; i--) {
//            try {
//                return profileResourceBundles[i].getString(key);
//            } catch (Exception e) {
//            }
//        }
//        return null;
//    }
//
//    /**
//     * 根据key读取value(整型)
//     *
//     * @param key
//     * @return
//     */
//    public Integer getInt(String key) {
//        return getInt(key, null);
//    }
//
//    public Integer getInt(String key, Integer defaultValue) {
//        String value = get(key);
//        if (value == null && defaultValue != null) {
//            return defaultValue;
//        }
//        try {
//            return Integer.parseInt(value);
//        } catch (Exception e) {
//            return null;
//        }
//    }
//
//    /**
//     * 根据key读取value(布尔)
//     *
//     * @param key
//     * @return
//     */
//    public Boolean getBool(String key) {
//        return getBool(key, null);
//    }
//
//    public Boolean getBool(String key, Boolean defaultValue) {
//        String value = get(key);
//        if (value == null && defaultValue != null) {
//            return defaultValue;
//        }
//        try {
//            return Boolean.parseBoolean(value);
//        } catch (Exception e) {
//            return null;
//        }
//    }
//
//    public Long getLong(String key) {
//        return getLong(key, null);
//    }
//
//    public Long getLong(String key, Long defaultValue) {
//        String value = get(key);
//        if (value == null && defaultValue != null) {
//            return defaultValue;
//        }
//        try {
//            return Long.parseLong(value);
//        } catch (Exception e) {
//            return null;
//        }
//    }
//
//    public Date getLoadTime() {
//        return loadTime;
//    }
//
//
//}
