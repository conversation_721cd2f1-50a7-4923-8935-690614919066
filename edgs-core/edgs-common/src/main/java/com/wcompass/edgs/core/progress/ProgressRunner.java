package com.wcompass.edgs.core.progress;

import com.wcompass.edgs.core.task.ProgressTaskType;
import com.wcompass.edgs.core.task.ProgressType;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2022/9/27
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ProgressRunner {

    ProgressTaskType taskType();

    String progressType() default ProgressType.DOWNLOAD;

    String progressName() default "";
}
