package com.wcompass.edgs.utils;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.MD5;
import com.wcompass.edgs.core.crypto.AESUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * 加密工具类
 *
 * <AUTHOR>
 * @date Created on 2019/7/27
 */
@Slf4j
public final class EncryptUtil {

    /**
     * md5加密
     *
     * @param source
     * @return
     */
    public static String md5(String source) {
        return SecureUtil.md5(source);
    }

    public static String md5(byte[] source) {
        return new MD5().digestHex(source);
    }

    /**
     * 使用base64加密url,字符集使用的是“UTF-8”
     *
     * @param source
     * @return
     */
    public static String base64UrlEncode(String source) {
        if (source == null) {
            return null;
        }
        byte[] bytes = source.getBytes(StandardCharsets.UTF_8);
        return Base64.getUrlEncoder().encodeToString(bytes);
    }

    public static String base64UrlEncode(byte[] sourceBytes) {
        if (sourceBytes == null) {
            return null;
        }
        return Base64.getUrlEncoder().encodeToString(sourceBytes);
    }

    /**
     * 使用base64解密url,字符集使用的是“UTF-8”
     *
     * @param target
     * @return
     */
    public static String base64UrlDecodeToString(String target) {
        if (target == null) {
            return null;
        }
        byte[] bytes = target.getBytes(StandardCharsets.UTF_8);
        bytes = Base64.getUrlDecoder().decode(bytes);
        return new String(bytes);
    }

    public static byte[] base64UrlDecode(String target) {
        if (target == null) {
            return null;
        }
        byte[] bytes = target.getBytes(StandardCharsets.UTF_8);
        bytes = Base64.getUrlDecoder().decode(bytes);
        return bytes;
    }

    public static String base64DecodeToString(String target) {
        if (target == null) {
            return null;
        }
        byte[] bytes = Base64.getDecoder().decode(target);
        return new String(bytes);
    }

    public static String urlEncode(String source) throws UnsupportedEncodingException {
        String encode = URLEncoder.encode(source, StandardCharsets.UTF_8);
        //由于空格编码后转换为加号“+”，但是浏览器没有将加号“+”解码成空格
        //将编码后字符串中的“+”替换为“%20”
        return encode.replaceAll("\\+", "%20");
    }

    public static String urlDecode(String target) throws UnsupportedEncodingException {
        return URLDecoder.decode(target, StandardCharsets.UTF_8);
    }

    /**
     * service 64 encode
     *
     * @param bytes 待编码的byte[]
     * @return 编码后的base 64 code
     */
    public static String base64Encode(byte[] bytes) {
        return Base64.getEncoder().encodeToString(bytes);
    }

    /**
     * service 64 decode
     *
     * @param base64Code 待解码的base 64 code
     * @return 解码后的byte[]
     * @throws Exception
     */
    public static byte[] base64Decode(String base64Code) {
        return base64Code == null ? null : Base64.getDecoder().decode(base64Code);
    }


    /**
     * AES加密为base 64 code
     *
     * @param content    待加密的内容
     * @param encryptKey 加密密钥
     * @return 加密后的base 64 code
     * @throws Exception
     */
    public static String aesEncrypt(String content, String encryptKey) {
        return AESUtil.aesEncrypt(content, encryptKey);
    }

    public static String aesEncryptCrossPlatform(String content, String key, String iv) {
        return AESUtil.aesEncryptCrossPlatform(content, key, iv);
    }

    public static String aesEncryptCrossPlatformWithUtf8(String content, String key, String iv) {
        return AESUtil.aesEncryptCrossPlatformWithUtf8(content, key, iv);
    }

    /**
     * 将base 64 code AES解密
     *
     * @param encryptStr 待解密的base 64 code
     * @param decryptKey 解密密钥
     * @return 解密后的string
     * @throws Exception
     */
    public static String aesDecrypt(String encryptStr, String decryptKey) {
        return AESUtil.aesDecrypt(encryptStr, decryptKey);
    }

    public static String aesDecryptCrossPlatform(String encryptStr, String key, String iv) {
        return AESUtil.aesDecryptCrossPlatform(encryptStr, key, iv);
    }

    public static String aesDecryptCrossPlatformWithUtf8(String encryptStr, String key, String iv) {
        return AESUtil.aesDecryptCrossPlatformWithUtf8(encryptStr, key, iv);
    }


    public static void main(String[] args) {
        String content = "hello world";
        String key = "AjQ0YQ0MvKKC1uYH";
        String iv = "6432255672651776";
        String encryptKey = aesEncryptCrossPlatformWithUtf8(content, key, iv);
        System.out.println(encryptKey);
        System.out.println(aesDecryptCrossPlatformWithUtf8(encryptKey, key, iv));
        System.out.println(aesEncrypt("compass@2022", "123456"));
        System.out.println(aesEncrypt("CCompass@@2023", "123456"));

        System.out.println(aesDecrypt("z80clKT1MgpKG9beMJirbQ==", "123456"));

        System.out.println(aesEncrypt("nacos", "123456"));

        System.out.println(aesEncrypt("Compass@2025", "123456"));
    }


}
