package com.wcompass.edgs.core.excel;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/11/11
 */
public interface SimpleRowHandler extends RowHandler<Map<Integer, String>> {

    @Override
    void handleHead(int sheetIndex, int rowIndex, Map<Integer, String> head);

    @Override
    void handleRow(int sheetIndex, int rowIndex, Map<Integer, String> row);

    /**
     * 默认表头只有一行，行数从1开始
     * 数据开始读取的行
     * @return
     */
    default int getBeginRowNumber() {
        return 1;
    }
}
