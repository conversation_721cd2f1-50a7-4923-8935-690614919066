package com.wcompass.edgs.utils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023-10-23 17:32
 */
public class GraphUtil {
    private Map<String, Set<String>> adjacencyList = new HashMap<>();

    public GraphUtil(Map<String, Set<String>> adjacencyList) {
        this.adjacencyList = adjacencyList;
    }

    public GraphUtil() {
    }

    public void setAdjacencyList(Map<String, Set<String>> adjacencyList) {
        this.adjacencyList = adjacencyList;
    }

    public void addEdge(String source, String destination) {
        adjacencyList.computeIfAbsent(source, k -> new HashSet<>()).add(destination);
    }

    /*public List<String> findPaths(String start, String end) {
        List<String> paths = new ArrayList<>();
        Set<String> visited = new HashSet<>();
        findAllPaths(start, end, new ArrayList<>(Arrays.asList(start)), paths, visited);
        return paths;
    }

    private void findAllPaths(String current, String end, List<String> currentPath, List<String> allPaths, Set<String> visited) {
        if (current.equals(end)) {
            allPaths.add(String.join("/", new ArrayList<>(currentPath)));
            return;
        }

        visited.add(current);
        for (String neighbor : adjacencyList.getOrDefault(current, Collections.emptySet())) {
            if (!currentPath.contains(neighbor)) { // 避免重复访问当前路径中的节点
                List<String> newPath = new ArrayList<>(currentPath);
                newPath.add(neighbor);
                findAllPaths(neighbor, end, newPath, allPaths, visited);
            }
        }
        visited.remove(current); // 回溯时从visited中移除当前节点
    }*/

    public List<String> findPaths(String start, String end) {
        List<List<String>> allPaths = findAllPaths(start, end, adjacencyList);

        List<String> formattedPaths = new ArrayList<>();
        for (List<String> path : allPaths) {
            formattedPaths.add(String.join("/", path));
        }
        return formattedPaths;
    }

    public static List<List<String>> findAllPaths(String start, String end, Map<String, Set<String>> adjacencyList) {
        List<List<String>> allPaths = new ArrayList<>();
        Queue<List<String>> queue = new LinkedList<>();
        queue.add(Collections.singletonList(start));
        while (!queue.isEmpty()) {
            List<String> currentPath = queue.poll();
            String current = currentPath.get(currentPath.size() - 1);

            if (current.equals(end)) {
                allPaths.add(currentPath);
            } else {
                Set<String> neighbors = adjacencyList.get(current);
                if (neighbors != null) {
                    neighbors.stream()
                            .filter(neighbor -> !currentPath.contains(neighbor))
                            .forEach(neighbor -> {
                                List<String> newPath = new ArrayList<>(currentPath);
                                newPath.add(neighbor);
                                queue.add(newPath);
                            });
                }
            }
        }

        return allPaths;
    }


    public static void main(String[] args) {
        GraphUtil graphUtil = new GraphUtil();
        graphUtil.addEdge("1727887037928968308", "1727887037945745490");
        graphUtil.addEdge("1727887037933162807", "1727887037928968308");
        graphUtil.addEdge("1727887037937356807", "1727887037928968308");
        graphUtil.addEdge("1727887037937356816", "1727887037928968308");
        graphUtil.addEdge("1727887037928968411", "1727887037937356816");
        graphUtil.addEdge("1727887037933162506", "1727887037933162807");
        graphUtil.addEdge("1727887037937356938", "1727887037937356807");
        graphUtil.addEdge("1727887037937356978", "1727887037933162807");
        graphUtil.addEdge("1727887037924774071", "1727887037933162506");
        graphUtil.addEdge("1727887037924774071", "1727887037933162506");
        graphUtil.addEdge("1727887037928968308", "1727887037928968411");
        graphUtil.addEdge("1727887037937356807", "1727887037928968411");
        graphUtil.addEdge("1727887037937356816", "1727887037928968411");
        graphUtil.addEdge("1727887037937356978", "1727887037928968411");

       /* graphUtil.addEdge("a", "b");
        graphUtil.addEdge("b", "c");
        graphUtil.addEdge("b", "d");
        graphUtil.addEdge("c", "f");
        graphUtil.addEdge("c", "d");
        graphUtil.addEdge("d", "e");
        graphUtil.addEdge("e", "d");
        graphUtil.addEdge("e", "c");*/

        List<String> paths = graphUtil.findPaths("1727887037937356938", "1727887037945745490");
        // List<String> paths = graphUtil.findPaths("a", "f");
        System.out.println(paths.size());
        /*
        * 1.a-b-c-f
        * 2.a-b-d-e-c-f
        * 3.a-b-c-d-e-c-f的线路信息
        * */
    }
}
