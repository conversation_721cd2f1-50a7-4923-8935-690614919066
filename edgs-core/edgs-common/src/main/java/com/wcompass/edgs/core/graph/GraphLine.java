package com.wcompass.edgs.core.graph;

import com.wcompass.edgs.utils.IdUtil;
import com.wcompass.edgs.utils.StringUtil;
import lombok.Getter;

import java.util.Collection;
import java.util.LinkedList;
import java.util.stream.Collectors;

@Getter
public class GraphLine<T> {

    private final LinkedList<GraphNode<T>> nodes = new LinkedList<>();

    private final String id;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof GraphLine)) {
            return false;
        }

        GraphLine<?> graphLine = (GraphLine<?>) o;

        return getNodes().equals(graphLine.getNodes());
    }

    @Override
    public int hashCode() {
        return getNodes().hashCode();
    }

    public GraphLine() {
        this.id = IdUtil.simpleUUID();
    }

    public GraphLine(Collection<GraphNode<T>> nodes) {
        this();
        addNodes(nodes);
    }

    @Override
    public String toString() {
        return StringUtil.join(nodes.stream().map(GraphNode::getValue).collect(Collectors.toList()), "->");
    }

    public int size() {
        return nodes.size();
    }

    public GraphNode<T> firstNode() {
        return nodes.isEmpty() ? null : nodes.getFirst();
    }

    public void addFirst(GraphNode<T> node) {
        nodes.addFirst(node);
    }

    private void addNodes(Collection<GraphNode<T>> nodes) {
        this.nodes.addAll(nodes);
    }

    public GraphLine<T> copy() {
        GraphLine<T> line = new GraphLine<>();
        line.addNodes(this.nodes);
        return line;
    }
}
