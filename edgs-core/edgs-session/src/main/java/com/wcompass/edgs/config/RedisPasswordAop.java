package com.wcompass.edgs.config;

import com.wcompass.edgs.utils.EncryptUtil;
import com.wcompass.edgs.utils.StringUtil;
import com.wcompass.edgs.utils.SymmetricEncryptUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 利用aop的思想实现对redis配置密码的解密
 * <AUTHOR>
 * @date Created on 2021/3/29
 */
@Aspect
@Component
@Slf4j
public class RedisPasswordAop {

    @Value("${encrypt.key:123456}")
    private String encryptKey;

    @Pointcut("execution(* org.springframework.boot.autoconfigure.data.redis.RedisProperties.getPassword())")
    public void getPassword() {
    }

    /**
     * 集群、单机模式密码解密
     * @param point
     * @return
     * @throws Throwable
     */
    @Around("getPassword()")
    public Object aroundGetPassword(ProceedingJoinPoint point) throws Throwable {
        String password = (String) point.proceed();
        return handlePassword(password);
    }

    @Pointcut("execution(* org.springframework.boot.autoconfigure.data.redis.RedisProperties.Sentinel.getPassword())")
    public void getSentinelPassword() {
    }

    /**
     * 哨兵模式密码解密
     * @param point
     * @return
     * @throws Throwable
     */
    @Around("getSentinelPassword()")
    public Object aroundGetSentinelPassword(ProceedingJoinPoint point) throws Throwable {
        String password = (String) point.proceed();
        return handlePassword(password);
    }

    private String handlePassword(String password) {
        if (StringUtil.isNotBlank(password)) {
            password = EncryptUtil.aesDecrypt(password, encryptKey);
        } else {
            password = null;
        }
        return password;
    }
}
