package com.wcompass.edgs.core.session;

import cn.hutool.db.Db;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.wcompass.edgs.constant.GlobalConstant;
import com.wcompass.edgs.core.AccountProfile;
import com.wcompass.edgs.core.Location;
import com.wcompass.edgs.core.datasource.CustomHikariDataSource;
import com.wcompass.edgs.exception.ExceptionEnum;
import com.wcompass.edgs.exception.SystemException;
import com.wcompass.edgs.utils.DateUtil;
import com.wcompass.edgs.utils.ExceptionUtil;
import com.wcompass.edgs.utils.JsonUtil;
import com.wcompass.edgs.utils.RequestUtil;
import com.zaxxer.hikari.HikariDataSource;
import lombok.Data;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.sql.SQLException;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.LongSupplier;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2024-11-11
 */
@Slf4j
@Setter
public class SessionManager implements DisposableBean, InitializingBean {

    private LongSupplier sessionTimeoutGetter;

    private Supplier<String> enableConcurrentLoginGetter;

    private final Db sessionRepository;

    private final HikariDataSource hikariDataSource;

    private final SessionProperties sessionProperties;

    private final Cache<String, SessionSetting> sessionSettingCache;

    private final ThreadPoolTaskScheduler sessionCleanerScheduler;

    public SessionManager(SessionProperties sessionProperties) {
        this.sessionProperties = sessionProperties;
        this.hikariDataSource = buildDatasource();
        this.sessionRepository = Db.use(this.hikariDataSource);

        sessionSettingCache = Caffeine.newBuilder().expireAfterAccess(3, TimeUnit.HOURS).build();

        sessionCleanerScheduler = new ThreadPoolTaskScheduler();
        sessionCleanerScheduler.setThreadNamePrefix("session-cleaner-");
        sessionCleanerScheduler.setPoolSize(1);
        sessionCleanerScheduler.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        sessionCleanerScheduler.initialize();
    }

    private HikariDataSource buildDatasource() {
        return new CustomHikariDataSource(this.sessionProperties.getDb());
    }

    private void cleanInvalidSession() {
        long sessionDeadline = DateUtil.offSet(DateUtil.now(), -3, ChronoUnit.HOURS).getTime();
        try {
            sessionRepository.execute("""
                    delete
                    from t99_session
                    where expire_time < ?
                    """, sessionDeadline);
        } catch (SQLException e) {
            throw SystemException.wrap(e);
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        sessionCleanerScheduler.scheduleWithFixedDelay(this::cleanInvalidSession, Duration.ofHours(1));
    }

    @Data
    private static class SessionSetting {

        private long timeout;

        private boolean enableConcurrentLogin;
    }

    public void createSession(String sessionId, AccountProfile accountProfile, boolean enableForceLogin) {
        try {
            sessionRepository.tx(db -> {
                if (!isEnableConcurrentLogin(sessionId)) {
                    String userId = accountProfile.getUserId();
                    String userName = accountProfile.getUserName();

                    Location location;
                    long now = DateUtil.currentTimeMillis();
                    if (!enableForceLogin) {
                        Session session = db.query("""
                                select
                                  session_id,
                                  user_id,
                                  ip,
                                  create_time
                                from t99_session
                                where user_id = ?
                                and expire_time > ?
                                """, rs -> {
                            if (rs.next()) {
                                return Session.builder()
                                        .sessionId(rs.getString(1))
                                        .userId(rs.getString(2))
                                        .ip(rs.getString(3))
                                        .createTime(rs.getTimestamp(4))
                                        .build();
                            } else {
                                return null;
                            }
                        }, userId, now);
                        if (session != null) {
                            location = new Location();
                            location.setToken(session.getSessionId());
                            location.setIp(session.getIp());
                            location.setLoginTime(session.getCreateTime());
                            throw SystemException.wrap(ExceptionEnum.USER_HAS_LOGIN.getCode(), "账号【{}】已在其他设备登录", userName).addExtras("location", location);
                        }
                    }

                    // 下线其它会话
                    db.execute("update t99_session set force_offline = ? where user_id = ? and expire_time > ?", GlobalConstant.YES, userId, now);

                }

                int result = db.execute("""
                        insert into t99_session (session_id, user_id, ip, session_attributes, create_time, expire_time, force_offline)
                        values (?, ?, ?, ?, ?, ?, ?)
                        """,
                        sessionId, accountProfile.getUserId(), RequestUtil.getClientIp(),
                        JsonUtil.toJsonString(accountProfile), DateUtil.now(), getExpireTime(sessionId),
                        GlobalConstant.NO);
                if (result != 1) {
                    throw SystemException.wrap(ExceptionEnum.LOGIN_FAIL.getCode(), "登陆失败，无法创建会话");
                }
            });
        } catch (SQLException e) {
            Throwable originalCause = ExceptionUtil.getOriginalCause(e);
            if (originalCause instanceof SystemException systemException) {
                throw systemException;
            } else {
                throw SystemException.wrap(e);
            }
        }
    }

    public void updateSession(String sessionId, AccountProfile accountProfile) {
        Session session = Session.builder().sessionId(sessionId).sessionAttributes(JsonUtil.toJsonString(accountProfile)).expireTime(getExpireTime(sessionId)).build();
        int result = updateBySessionId(session);
        if (result != 1) {
            throw SystemException.wrap(ExceptionEnum.SESSION_TIMEOUT);
        }
    }

    public void invalidateSession(String sessionId) {
        if (sessionId != null) {
            try {
                sessionRepository.tx(db -> {
                    if (!isEnableConcurrentLogin(sessionId)) {
                        String userId = db.queryString("""
                                select user_id
                                from t99_session
                                where session_id = ?
                                """, sessionId);
                        if (userId != null) {
                            // 删除所有该用户登录的会话
                            db.execute("""
                                    delete
                                    from t99_session
                                    where user_id = ?
                                    """, userId);
                        }
                    }
                    // 删除会话
                    db.execute("""
                            delete
                            from t99_session
                            where session_id = ?
                            """, sessionId);
                });
            } catch (SQLException e) {
                throw SystemException.wrap(e);
            }
        }
    }

    /**
     * 获取当前登录用户信息
     *
     * @return
     */
    public AccountProfile getCurrentAccount(String sessionId) {
        if (sessionId == null) {
            throw new SystemException(ExceptionEnum.NO_LOGIN);
        }
        AccountProfile accountProfile = getAccountProfile(sessionId);
        if (accountProfile == null) {
            throw new SystemException(ExceptionEnum.SESSION_TIMEOUT);
        }
        try {
            sessionRepository.tx(db -> {
                Session session = db.query("""
                        select
                          session_id,
                          user_id,
                          ip,
                          create_time
                        from t99_session
                        where session_id = ?
                        """, rs -> {
                    if (rs.next()) {
                        return Session.builder().sessionId(rs.getString(1)).userId(rs.getString(2)).ip(rs.getString(3)).createTime(rs.getTimestamp(4)).build();
                    } else {
                        return null;
                    }
                }, sessionId);

                if (!isEnableConcurrentLogin(sessionId)) {
                    boolean existOtherSession = db.query("""
                            select
                              count(0)
                            from t99_session
                            where user_id = ?
                            and ip != ?
                            and expire_time > ?
                            and force_offline = 'N'
                            """, rs -> {
                        if (rs.next()) {
                            return rs.getInt(1) > 1;
                        } else {
                            return false;
                        }
                    }, session.getUserId(), session.getIp(), DateUtil.currentTimeMillis());
                    if (existOtherSession) {
                        Location location = new Location();
                        location.setIp(session.getIp());
                        location.setToken(session.getSessionId());
                        location.setLoginTime(session.getCreateTime());
                        throw SystemException.wrap(ExceptionEnum.FORCED_OFFLINE.getCode(), "账号已在其他设备登录，您被强制下线").addExtras("location", location);
                    }
                }
                // 重置token失效时间
                db.execute("""
                        update t99_session
                        set expire_time = ?
                        where session_id = ?
                        """, getExpireTime(sessionId), sessionId);
            });
        } catch (SQLException e) {
            log.error("", e);
            Throwable originalCause = ExceptionUtil.getOriginalCause(e);
            if (originalCause instanceof SystemException systemException) {
                throw systemException;
            } else {
                throw SystemException.wrap(e);
            }

        }

        return accountProfile;
    }

    private long getExpireTime(String sessionId) {
        long timeout = getSessionSetting(sessionId).getTimeout();
        return DateUtil.offSet(DateUtil.now(), timeout, ChronoUnit.SECONDS).getTime();
    }

    private boolean isEnableConcurrentLogin(String sessionId) {
        return getSessionSetting(sessionId).enableConcurrentLogin;
    }

    private SessionSetting getSessionSetting(String sessionId) {
        return sessionSettingCache.get(sessionId, k -> {
            SessionSetting sessionSetting = new SessionSetting();
            sessionSetting.setTimeout(getSessionTimeout());
            sessionSetting.setEnableConcurrentLogin(enableConcurrentLogin());
            return sessionSetting;
        });
    }

    public AccountProfile getAccountProfile(String sessionId) {
        try {
            String sessionAttributes = sessionRepository.queryString("""
                    select session_attributes
                    from t99_session
                    where session_id = ?
                    and expire_time > ?
                    and force_offline = 'N'
                    """, sessionId, DateUtil.currentTimeMillis());
            if (sessionAttributes == null) {
                return null;
            }
            return JsonUtil.parseObject(sessionAttributes, AccountProfile.class);
        } catch (SQLException e) {
            throw SystemException.wrap(e);
        }
    }

    private int updateBySessionId(Session session) {
        try {
            return sessionRepository.execute("""
                    update t99_session
                    set session_attributes = ?,
                        expire_time = ?
                    where session_id = ?
                    """, session.getSessionAttributes(), session.getExpireTime(), session.getSessionId());
        } catch (SQLException e) {
            throw SystemException.wrap(e);
        }
    }

    /**
     * 获取会话超时时间, 单位为秒（s）
     *
     * @return
     */
    public long getSessionTimeout() {
        if (sessionTimeoutGetter != null) {
            return sessionTimeoutGetter.getAsLong() * 60;
        } else {
            return GlobalConstant.DEFAULT_SESSION_TIMEOUT;
        }
    }

    public boolean enableConcurrentLogin() {
        String enableConcurrentLogin;
        if (enableConcurrentLoginGetter != null) {
            enableConcurrentLogin = enableConcurrentLoginGetter.get();
        } else {
            enableConcurrentLogin = GlobalConstant.DEFAULT_ENABLE_CONCURRENT_LOGIN;
        }
        return GlobalConstant.YES.equals(enableConcurrentLogin);
    }

    @Override
    public void destroy() throws Exception {
        if (hikariDataSource != null) {
            hikariDataSource.close();
        }
    }
}
