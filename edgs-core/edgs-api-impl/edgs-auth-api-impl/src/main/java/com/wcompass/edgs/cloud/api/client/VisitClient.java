package com.wcompass.edgs.cloud.api.client;

import com.wcompass.edgs.cloud.api.client.fallback.CollectClientFallbackFactory;
import com.wcompass.edgs.core.AjaxResponseWrapper;
import com.wcompass.edgs.core.Microservice;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2021/6/30
 */
@FeignClient(name = Microservice.ServiceName.AUTH, path = Microservice.ContextPath.AUTH,
        fallbackFactory = CollectClientFallbackFactory.class)
public interface VisitClient extends VisitCenter {

    @Override
    @PostMapping(VISIT)
    AjaxResponseWrapper<Void> visit(@RequestParam String resourceId,
                                    @RequestParam byte resourceType,
                                    @RequestParam String visitorId);

    @Override
    @GetMapping(COUNT_VISIT)
    AjaxResponseWrapper<Long> countVisit(@RequestParam String resourceId,
                                            @RequestParam byte resourceType);
}
