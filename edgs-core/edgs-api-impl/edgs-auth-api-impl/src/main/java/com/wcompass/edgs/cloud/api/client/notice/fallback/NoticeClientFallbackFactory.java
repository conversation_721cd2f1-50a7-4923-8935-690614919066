package com.wcompass.edgs.cloud.api.client.notice.fallback;

import com.wcompass.edgs.cloud.api.client.model.NoticeDTO;
import com.wcompass.edgs.cloud.api.client.notice.NoticeClient;
import com.wcompass.edgs.core.AjaxResponseWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class NoticeClientFallbackFactory implements FallbackFactory<NoticeClient> {
    @Override
    public NoticeClient create(Throwable throwable) {
        log.error("访问服务异常，服务降级", throwable);
        return new NoticeClient() {
            @Override
            public AjaxResponseWrapper<Void> addEmailDataByUser(NoticeDTO noticeDTO) {
                return null;
            }

            @Override
            public AjaxResponseWrapper<Void> addEmailDataWithToAndCc(NoticeDTO noticeDTO) {
                return null;
            }

            @Override
            public AjaxResponseWrapper<Void> addSmsDataByUser(String module, String receiverId, String noticePhone, String sendMessage, String sendTitle) {
                return null;
            }
        };
    }
}
