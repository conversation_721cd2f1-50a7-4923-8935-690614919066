package com.wcompass.edgs.cloud.api.client.fallback;

import com.wcompass.edgs.cloud.api.client.FileImportClient;
import com.wcompass.edgs.cloud.api.client.model.FileImportDTO;
import com.wcompass.edgs.core.AjaxResponseWrapper;
import com.wcompass.edgs.core.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-05-16 17:12
 */
@Component
@Slf4j
public class FileImportClientFallbackFactory implements FallbackFactory<FileImportClient> {
    @Override
    public FileImportClient create(Throwable cause) {
        return new FileImportClient() {

            @Override
            public AjaxResponseWrapper<Void> addImportData(List<FileImportDTO> dtos) {
                return AjaxResponseWrapper.fail("服务不可用");
            }

            @Override
            public AjaxResponseWrapper<Void> updateImportData(List<FileImportDTO> dtos) {
                return AjaxResponseWrapper.fail("服务不可用");
            }

            @Override
            public AjaxResponseWrapper<Void> delData() {
                return AjaxResponseWrapper.fail("服务不可用");
            }

            @Override
            public AjaxResponseWrapper<Void> delDataById(List<Integer> ids) {
                return AjaxResponseWrapper.fail("服务不可用");
            }

            @Override
            public AjaxResponseWrapper<Page<FileImportDTO>> selectDataByPage(String sessionId, String importType, String checkStatus, String sheetName, Integer current, Integer size) {
                return AjaxResponseWrapper.fail("服务不可用");
            }

            @Override
            public AjaxResponseWrapper<Integer> getDataNum(String checkStatus, String sessionId) {
                return AjaxResponseWrapper.fail("服务不可用");
            }
        };
    }
}
