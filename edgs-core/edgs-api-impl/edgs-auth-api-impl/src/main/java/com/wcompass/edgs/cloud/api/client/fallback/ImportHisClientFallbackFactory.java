package com.wcompass.edgs.cloud.api.client.fallback;

import com.wcompass.edgs.cloud.api.client.ImportHisClient;
import com.wcompass.edgs.cloud.api.client.model.FileImportHisDTO;
import com.wcompass.edgs.core.AjaxResponseWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023-05-16 17:12
 */
@Component
@Slf4j
public class ImportHisClientFallbackFactory implements FallbackFactory<ImportHisClient> {
    @Override
    public ImportHisClient create(Throwable cause) {
        return new ImportHisClient() {
            @Override
            public AjaxResponseWrapper<Void> add(FileImportHisDTO importHis) {
                return AjaxResponseWrapper.fail("服务不可用");
            }

            @Override
            public AjaxResponseWrapper<Void> updateStatus(String sessionId, String status) {
                return AjaxResponseWrapper.fail("服务不可用");
            }

            @Override
            public AjaxResponseWrapper<Void> delHis() {
                return AjaxResponseWrapper.fail("服务不可用");
            }
        };
    }
}
