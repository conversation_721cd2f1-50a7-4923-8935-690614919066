package com.wcompass.edgs.cloud.api.client.systemParam;

import com.wcompass.edgs.cloud.api.client.systemParam.fallback.SystemParamClientFallbackFactory;
import com.wcompass.edgs.core.AjaxResponseWrapper;
import com.wcompass.edgs.core.Microservice;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Properties;


@FeignClient(name = Microservice.ServiceName.AUTH, path = Microservice.ContextPath.AUTH,
        fallbackFactory = SystemParamClientFallbackFactory.class)
public interface SystemParamClient extends SystemParamCenter {

    @Override
    @GetMapping(QUERY_SYSTEM_PARAM_URL)
    AjaxResponseWrapper<String> getSystemParam(@RequestParam(name = "paramName") String paramName);

    @Override
    @GetMapping(QUERY_EMAIL_CONFIG_URL)
    AjaxResponseWrapper<Properties> getEmailConfig();

    @Override
    @GetMapping(QUERY_SIMPLE_EMAIL_CONFIG_URL)
    AjaxResponseWrapper<Properties> getSimpleEmailConfig(@RequestParam String subject, @RequestParam String msg, @RequestParam String... to);

    @Override
    @GetMapping(QUERY_ATTACHMENT_EMAIL_CONFIG_URL)
    AjaxResponseWrapper<Properties> getAttachmentEmailConfig(@RequestParam String subject, @RequestParam String msg,@RequestParam String attachmentId, @RequestParam String... to);

    @Override
    @GetMapping(QUERY_TEMPLATE_EMAIL_CONFIG_URL)
    AjaxResponseWrapper<Properties> getTemplateEmailConfig(@RequestParam String subject, @RequestParam String template , @RequestParam String... to);

    @Override
    @GetMapping(QUERY_LONG_SYSTEM_PARAM)
    AjaxResponseWrapper<Long> getLongValue(@RequestParam String paramName, @RequestParam Long defaultValue);

    @Override
    @GetMapping(QUERY_STRING_SYSTEM_PARAM)
    AjaxResponseWrapper<String> getValue(@RequestParam String paramName, @RequestParam(required = false) String defaultValue);

    @Override
    @GetMapping(QUERY_STRING_JSON_PARAM)
    AjaxResponseWrapper<String> getJsonParam(@RequestParam String paramName);

    @Override
    @PostMapping(UPDATE_VALUE)
    AjaxResponseWrapper<Void> updateValue(@RequestParam String paramName,
                                          @RequestParam String paramValue);
}
