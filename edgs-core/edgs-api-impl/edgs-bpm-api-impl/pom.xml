<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>edgs-api-impl</artifactId>
        <groupId>com.wcompass.edgs.cloud.api.impl</groupId>
        <version>4.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>edgs-bpm-api-impl</artifactId>
    <version>4.0.0</version>
    <packaging>jar</packaging>
    <name>edgs-bpm-api-impl</name>

    <dependencies>

        <dependency>
            <groupId>com.wcompass.edgs.cloud.api.impl</groupId>
            <artifactId>edgs-base-api-impl</artifactId>
            <version>4.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.wcompass.edgs.cloud.api</groupId>
            <artifactId>edgs-bpm-api</artifactId>
            <version>4.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>edgs-bpm-api-impl</finalName>

    </build>
</project>