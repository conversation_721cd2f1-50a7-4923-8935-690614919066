//package com.wcompass.edgs.cloud.sync;
//
//import com.wcompass.edgs.exception.SystemException;
//import com.wcompass.edgs.utils.CollectionUtil;
//import com.wcompass.edgs.utils.EncryptUtil;
//import com.wcompass.edgs.utils.ExceptionUtil;
//import com.wcompass.edgs.utils.StringUtil;
//import lombok.extern.slf4j.Slf4j;
//import okhttp3.*;
//import org.springframework.http.HttpStatus;
//
//import javax.net.ssl.*;
//import java.nio.charset.StandardCharsets;
//import java.security.KeyManagementException;
//import java.security.NoSuchAlgorithmException;
//import java.security.SecureRandom;
//import java.security.cert.CertificateException;
//import java.security.cert.X509Certificate;
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 对Qlik Replicate API的封装
// *
// * <AUTHOR>
// * @date Created on 2022/2/17
// */
//@Slf4j
//public class QlikReplicateServer {
//
//    public static final String DEFAULT_IP = "127.0.0.1";
//
//    public static final int DEFAULT_PORT = 3552;
//
//    private final String ip;
//
//    private final Integer port;
//
//    private final String user;
//
//    private final String password;
//
//    private String authorization;
//
//    private final OkHttpClient httpClient;
//
//    private final List<Cookie> cookieStore = new ArrayList<>();
//
//    public QlikReplicateServer(String ip, int port, String user, String password) {
//        this.ip = ip;
//        this.port = port;
//        this.user = user;
//        this.password = password;
//        checkServerStatus();
//        try {
//            this.httpClient = createSSLHttpClient();
//            this.login();
//        } catch (Exception e) {
//            throw SystemException.wrap(ExceptionUtil.printOriginalStackTrace(e));
//        }
//    }
//
//    public QlikReplicateServer(String user, String password) {
//        this(DEFAULT_IP, DEFAULT_PORT, user, password);
//    }
//
//    public void listTask(String searchPattern) {
//        try {
//            String listTask = QlikReplicateServerApi.LIST_TASK.format(ip, port);
//            HttpUrl.Builder urlBuilder = HttpUrl.get(listTask).newBuilder();
//            urlBuilder.addQueryParameter("search_pattern", searchPattern);
//            Request request = new Request.Builder()
//                    .url(urlBuilder.build())
//                    .header("Authorization", this.authorization)
//                    .get()
//                    .build();
//            Call call = this.httpClient.newCall(request);
//            Response response = call.execute();
//            String message = response.message();
//            log.info(message);
//        } catch (Exception e) {
//            throw SystemException.wrap(e);
//        }
//    }
//
//    public void login() {
//        String api = QlikReplicateServerApi.LOGIN.format(ip, port);
//        try {
//            Request request = new Request.Builder()
//                    .url(api)
//                    .header("Authorization", this.authorization)
//                    .get()
//                    .build();
//            Call call = this.httpClient.newCall(request);
//            Response response = call.execute();
//            log.info("请求成功了");
//            Headers headers = response.headers();
//            int code = response.code();
//            if(HttpStatus.OK.value() == code) {
//                log.info("", headers);
//                // 记录cookies
//            } else {
//                String message = response.message();
//                throw SystemException.wrap("Qlik Repicate服务器认证失败，{}", message);
//            }
//        } catch (Exception e) {
//            throw SystemException.wrap(e);
//        }
//    }
//
//    private void checkServerStatus() {
//        if (StringUtil.isBlank(ip)) {
//            throw SystemException.wrap("qlik replicate服务器的ip不能为空");
//        }
//        if (port == null) {
//            throw SystemException.wrap("qlik replicate服务器的port不能为空");
//        }
//        if (StringUtil.isBlank(user)) {
//            throw SystemException.wrap("qlik replicate服务器的登录账号缺失");
//        }
//        if (StringUtil.isBlank(password)) {
//            throw SystemException.wrap("qlik replicate服务器的登录密码缺失");
//        }
//        String key = EncryptUtil.base64Encode(StringUtil.join(this.user, ":", this.password).getBytes(StandardCharsets.UTF_8));
//        this.authorization = StringUtil.join("Basic ", key);
//    }
//
//    private OkHttpClient createSSLHttpClient() throws NoSuchAlgorithmException, KeyManagementException {
//        TrustAllCerts trustAllCerts = new TrustAllCerts();
//        SSLContext sslContext = SSLContext.getInstance("TLS");
//        sslContext.init(null, new TrustManager[]{trustAllCerts}, new SecureRandom());
//        SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();
//        return new OkHttpClient.Builder()
//                .cookieJar(new CookieJar() {
//                    @Override
//                    public void saveFromResponse(HttpUrl url, List<Cookie> cookies) {
//                        setCookie(cookies);
//                    }
//
//                    @Override
//                    public List<Cookie> loadForRequest(HttpUrl url) {
//                        return cookieStore;
//                    }
//                })
//                .sslSocketFactory(sslSocketFactory, trustAllCerts)
//                .hostnameVerifier(new TrustAllHostnameVerifier())
//                .build();
//    }
//
//    private void setCookie(List<Cookie> cookies) {
//        if(CollectionUtil.isNotEmpty(cookies)) {
//            cookieStore.addAll(cookies);
//        }
//    }
//
//    private static class TrustAllCerts implements X509TrustManager {
//        @Override
//        public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
//            // do nothing
//        }
//
//        @Override
//        public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
//            // do nothing
//        }
//
//        @Override
//        public X509Certificate[] getAcceptedIssuers() {
//            return new X509Certificate[0];
//        }
//    }
//
//    private static class TrustAllHostnameVerifier implements HostnameVerifier {
//        @Override
//        public boolean verify(String hostname, SSLSession session) {
//            return true;
//        }
//    }
//}
