package com.wcompass.edgs.cloud.api.client.metadata.model;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class TableAnalyseResultDTO {

    private Integer id;

    private String instanceId;

    private String instanceCode;

    private String parentId;

    private Integer columnCount;

    private Integer rowCount;

    private Integer repeatRowCount;

    private String status;

    private Date startTime;

    private Date endTime;

    private String failureMessage;

    private List<ColumnAnalyseResultDTO> columnList;

}
