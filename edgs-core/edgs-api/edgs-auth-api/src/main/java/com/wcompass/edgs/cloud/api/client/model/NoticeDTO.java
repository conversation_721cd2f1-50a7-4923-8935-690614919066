package com.wcompass.edgs.cloud.api.client.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NoticeDTO implements Serializable {

    private static final long serialVersionUID = -1710295178494429549L;
    /**
     * 主键
     *
     */
    private Integer id;

    /**
     * 接收者类型: 1-用户, 2-部门
     *
     */
    private String receiverType;

    /**
     * 接受者的ID, 用户id或部门id
     *
     */
    private String receiverId;

    /**
     * 通知邮箱
     *
     */
    private String noticeEmail;

    /**
     * 抄送邮箱
     *
     */
    private String noticeCcEmail;

    /**
     * 通知电话
     *
     */
    private String noticePhone;

    /**
     * 发送时间
     *
     */
    private Date sendTime;

    /**
     * 发送状态, 1: 成功, 0: 失败
     *
     */
    private String sendStatus;

    /**
     * 发送结果
     *
     */
    private String sendResult;

    /**
     * 发送类型：1、email
     *
     */
    private String sendType;

    /**
     * 归属模块: 1-元数据, 2-数据质量, 3-数据标准
     *
     */
    private String module;

    /**
     * 通知微信
     */
    private String noticeWechat;
    /**
     * 附件id/邮件附件，多个附件用,分隔
     *
     */
    private String attachId;
    /**
     * 发送标题/邮件主题
     *
     */
    private String sendTitle;
    /**
     * 发送内容/邮件内容
     *
     */
    private String sendMessage;
}
