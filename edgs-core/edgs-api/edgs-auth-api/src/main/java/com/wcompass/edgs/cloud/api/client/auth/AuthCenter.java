package com.wcompass.edgs.cloud.api.client.auth;

import com.wcompass.edgs.cloud.api.client.model.AttachmentDTO;
import com.wcompass.edgs.cloud.api.client.model.DicDTO;
import com.wcompass.edgs.cloud.api.client.model.UserDTO;
import com.wcompass.edgs.cloud.api.client.model.auth.LoginDTO;
import com.wcompass.edgs.core.AjaxResponseWrapper;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 认证中心
 *
 * <AUTHOR>
 * @date Created on 2021/3/8
 */
public interface AuthCenter {

    String LOGIN = "/v1/login";

    /**
     * 统一登录
     *
     * @param loginDTO   登录信息传输对象
     * @return
     */
    AjaxResponseWrapper<Void> login(LoginDTO loginDTO);


    String IS_SYSADMIN = "/v1/sys/user/isSysadmin";

    /**
     * 判断用户是否为管理员
     *
     * @param userId
     * @return
     */
    AjaxResponseWrapper<Boolean> isSysadmin(String userId);

    String IS_ADMIN_ROLE = "/v1/sys/role/isAdminRole";

    /**
     * 是否为管理员角色
     *
     * @param userId 用户ID
     */
    AjaxResponseWrapper<Boolean> isAdminRole(String userId);

    String SYSTEM_PARAM = "/v1/auth/sys/param/getValue";

    /**
     * 根据参数名称获取系统参数值
     *
     * @param paramName
     * @return
     */
    AjaxResponseWrapper<String> getSystemValue(String paramName);

    String EDIT_SYSTEM_PARAM = "/v1/sys/param/update";

    /**
     * 修改系统参数
     *
     * @param paramName
     * @param paramValue
     * @return
     */
    AjaxResponseWrapper<Void> editSystemParam(String paramName, String paramValue);

    String REMOVE_ATTACHMENT = "/v1/attachment/remove";

    /**
     * 删除附件信息
     *
     * @param attachmentId
     * @return
     */
    AjaxResponseWrapper<Void> removeAttachment(String attachmentId);

    String DIC_LIST_BY_CODE = "/v1/auth/sys/dict/listByCode";

    /**
     * 根据code获取字典信息
     *
     * @param keyword
     * @return
     */
    AjaxResponseWrapper<List<DicDTO>> listDictByDictCode(String keyword);


    String USER_DETAIL = "/v1/sys/user/getUserDetail";

    /**
     * 获取用户信息
     *
     * @param userId
     * @return
     */
    AjaxResponseWrapper<UserDTO> getUserDetail(String userId);


    String SAVE_ATTACHMENT = "/v1/attachment/save";

    /**
     * 保存附件
     *
     * @param attachmentDTO
     * @return
     */
    AjaxResponseWrapper<String> saveAttachment(AttachmentDTO attachmentDTO);

    String DELETE_EMAIL_USER = "/v1/emailUser/delete";

    /**
     * 删除邮件相关用户
     */
    AjaxResponseWrapper<Void> deleteEmailUser(String module, String objectId, String type);

    String SAVE_EMAIL_USER = "/v1/emailUser/save";

    /**
     * 保存邮件相关用户
     *
     * @param module GlobalConstant.EmailUserModule
     * @param objectId 对象id
     * @param type GlobalConstant.EmailUserType
     * @param userId 用户id
     */
    AjaxResponseWrapper<Void> saveEmailUser(String module, String objectId, String type, String userId);

    String LIST_EMAIL_USER = "/v1/emailUser/list";

    /**
     * 保存邮件相关用户
     */
    AjaxResponseWrapper<List<UserDTO>> listEmailUser(String module, String objectId, String type);


    String IS_MENU_CODE = "/v1/sys/auth/isMenuCode";
    /**
     * 用户是否对菜单具有权限，并且该菜单可见
     */
    AjaxResponseWrapper<Boolean> isMenuCode(String userId, String menuCode);

}
