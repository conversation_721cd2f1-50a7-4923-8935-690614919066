package com.wcompass.edgs.cloud.api.client.model.datasource;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@Schema(description = "数据源基本信息模型")
public class DatasourceInfoDTO implements Serializable {

    private static final long serialVersionUID = 6595878988766377364L;
    /**
     * （主键）数据源ID
     */
    @Schema(description = "数据源ID")
    private String datasourceId;

    /**
     * 数据源名称
     */
    @Schema(description = "数据源名称")
    @NotBlank
    private String datasourceName;

    @Schema(description = "数据源类型")
    private String datasourceType;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 适配器工作模式
     */
    @Schema(description = "适配器工作模式")
    private String modeId;


    @Schema(description = "数据库驱动")
    private String driverClassName;

    @Schema(description = "数据库名称")
    private String databaseName;

    @Schema(description = "数据库url")
    private String url;

    @Schema(description = "关联用户数量")
    private Long userNumber;

    @Schema(description = "认证方式")
    public String authMode;

}
