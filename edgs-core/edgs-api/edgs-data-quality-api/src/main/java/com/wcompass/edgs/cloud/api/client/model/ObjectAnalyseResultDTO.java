package com.wcompass.edgs.cloud.api.client.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/4/29 17:38
 */
@Data
public class ObjectAnalyseResultDTO implements Serializable {
    private static final long serialVersionUID = -1085771242315384151L;

    private int analyseId;
    private int auditObjectId;
    private int columnCount;
    private int rowCount;
    private int repeatRowCount;
    private Date startTime;
    private Date endTime;
    private boolean isSuccess;
    /**
     * 失败信息
     */
    private String failureMessage;

    private List<ObjectColumnAnalyseResultDTO> columnAnalyseResults;


    public ObjectAnalyseResultDTO() {
    }


    public ObjectAnalyseResultDTO(int auditObjectId) {
        this.auditObjectId = auditObjectId;
    }
}
