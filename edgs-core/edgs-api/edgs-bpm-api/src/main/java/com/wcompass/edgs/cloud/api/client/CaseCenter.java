package com.wcompass.edgs.cloud.api.client;

import com.wcompass.edgs.cloud.api.client.model.CaseDTO;
import com.wcompass.edgs.core.AjaxResponseWrapper;

import java.util.ArrayList;

public interface CaseCenter {

    String START_METADATA_CHANGE_URL = "/v1/case/startMetadataChange";
    AjaxResponseWrapper<CaseDTO> startMetadataChange(String alterationHisKey, String subscribeName, String subscribeDesc, String businessId, String nextUserId);

    String START_DATA_QUALITY_URL = "/v1/case/startDataquality";
    AjaxResponseWrapper<CaseDTO> startDataquality(String caseName,String caseDesc,String nextUserId,String ruleTaskIds,Integer projectId);

    String IS_DATAQUALITY_RELATED_BY_PROJECT_IDS = "/v1/case/isDataqualityRelatedByProjectIds";
    AjaxResponseWrapper<Boolean> isDataqualityRelatedByProjectIds(ArrayList<Integer> projectIds);

    String IS_DATAQUALITY_RELATED_BY_RULE_IDS = "/v1/case/isDataqualityRelatedByRuleIds";
    AjaxResponseWrapper<Boolean> isDataqualityRelatedByRuleIds(ArrayList<Long> ruleIds);
}
