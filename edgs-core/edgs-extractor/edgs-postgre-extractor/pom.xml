<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.wcompass.edgs.cloud</groupId>
    <artifactId>edgs-extractor</artifactId>
    <version>4.0.0</version>
  </parent>

  <groupId>com.wcompass.edgs.cloud.extractor</groupId>
  <artifactId>edgs-postgre-extractor</artifactId>
  <version>4.0.0</version>
  <packaging>jar</packaging>
  <name>edgs-postgre-extractor</name>


  <dependencies>
    <dependency>
      <groupId>com.wcompass.edgs.cloud.extractor</groupId>
      <artifactId>edgs-base-extractor</artifactId>
      <version>4.0.0</version>
    </dependency>

  </dependencies>

  <build>
    <finalName>edgs-greenplum-extractor</finalName>
  </build>
</project>
