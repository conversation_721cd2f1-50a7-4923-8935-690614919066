package com.wcompass.edgs.extractor;

import com.google.common.collect.Lists;
import com.wcompass.edgs.core.datasource.AdapterParam;
import com.wcompass.edgs.core.datasource.DatabaseType;
import com.wcompass.edgs.core.extract.ExtractRule;
import com.wcompass.edgs.core.extract.FilterClassifier;
import com.wcompass.edgs.core.extract.MetaModel;
import com.wcompass.edgs.core.extractor.ExtractRoot;
import com.wcompass.edgs.extractor.model.ExtractorProperties;
import com.wcompass.edgs.utils.DataSourceUtil;
import com.wcompass.edgs.utils.IdUtil;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.ServerSocket;
import java.net.Socket;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date Created on 2021/4/1
 */
public class PostgreExtractorTest {

    @Test
    public void telnet() throws IOException {
        try(Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress("************", 9092));
            boolean connected = socket.isConnected();
            System.out.println(connected);
        }
    }

    @Test
    public void testConnect() throws SQLException, ClassNotFoundException {
        String url = "**********************************************";
        String username = "postgres";
        String password = "postgres";
        String driverClass = DatabaseType.PostgreSQL.getDriver();
        try(Connection connection = DataSourceUtil.getJdbcConnection(driverClass, url, username, password)) {
            connection.isValid(4);
        }
    }

    public List<ExtractRule> buildExtractClassifiers() {
        List<ExtractRule> extractRootList = new ArrayList<>();

        ExtractRule extractRule = new ExtractRule();
        extractRule.setSchemaName("guandata");

        List<FilterClassifier> filterClassifierList = new ArrayList<>();

        FilterClassifier filterClassifier = new FilterClassifier();
        filterClassifier.setClassifierId(MetaModel.TABLE);
        filterClassifier.setFilterRules(Collections.emptyList());
        filterClassifierList.add(filterClassifier);

        filterClassifier = new FilterClassifier();
        filterClassifier.setClassifierId(MetaModel.VIEW);
        filterClassifier.setFilterRules(Collections.emptyList());
        filterClassifierList.add(filterClassifier);

        filterClassifier = new FilterClassifier();
        filterClassifier.setClassifierId(MetaModel.FUNCTION);
        filterClassifier.setFilterRules(Collections.emptyList());
        filterClassifierList.add(filterClassifier);

        filterClassifier = new FilterClassifier();
        filterClassifier.setClassifierId(MetaModel.TRIGGER);
        filterClassifier.setFilterRules(Collections.emptyList());
        filterClassifierList.add(filterClassifier);

        filterClassifier = new FilterClassifier();
        filterClassifier.setClassifierId(MetaModel.PROCEDURE);
        filterClassifier.setFilterRules(Collections.emptyList());
        filterClassifierList.add(filterClassifier);

        filterClassifier = new FilterClassifier();
        filterClassifier.setClassifierId(MetaModel.PARTITION);
        filterClassifier.setFilterRules(Collections.emptyList());
        filterClassifierList.add(filterClassifier);

        filterClassifier = new FilterClassifier();
        filterClassifier.setClassifierId(MetaModel.PARTITION_TABLE);
        filterClassifier.setFilterRules(Collections.emptyList());
        filterClassifierList.add(filterClassifier);

        extractRule.setExtractClassifiers(filterClassifierList);

        extractRootList.add(extractRule);

        return extractRootList;
    }

    @Test
    public void test05() throws IOException, SQLException, ClassNotFoundException {
        long start = System.currentTimeMillis();
        PostgreExtractor postgreExtractor = new PostgreExtractor();

        Map<String, String> params = new HashMap<>();
        params.put(AdapterParam.DRIVER_CLASS_NAME, DatabaseType.PostgreSQL.getDriver());
        params.put(AdapterParam.URL, "**********************************************");

        ExtractorProperties extractorProperties = ExtractorProperties.builder()
                .execMode("offline")
                .batchNo(IdUtil.getSnowflakeId())
                .extractMode("jdbc")
                .extractClassifiers(buildExtractClassifiers())
                .source()
                .datasourceId("a6b2d5087bf743439715c37b37f5228b")
                .datasourceType(DatabaseType.PostgreSQL.name())
                .database("postgres")
                .extractRoots(Lists.newArrayList("guandata")
                        .stream()
                        .map(ExtractRoot::new)
                        .collect(Collectors.toList())
                )
                .params(params)
//                .schemas(Lists.newArrayList("DG"))
                .username("postgres")
                .password("postgres")
                .end()
                .build();


        postgreExtractor.setExtractorProperties(extractorProperties);
        postgreExtractor.extract();
        postgreExtractor.flush();
        long end = System.currentTimeMillis();
        System.out.println(TimeUnit.MILLISECONDS.toSeconds(end - start));
    }
}
