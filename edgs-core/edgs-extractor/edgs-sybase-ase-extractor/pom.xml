<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.wcompass.edgs.cloud</groupId>
        <artifactId>edgs-extractor</artifactId>
        <version>4.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.wcompass.edgs.cloud.extractor</groupId>
    <artifactId>edgs-sybase-ase-extractor</artifactId>
    <version>4.0.0</version>
    <packaging>jar</packaging>
    <name>edgs-sybase-ase-extractor</name>

    <dependencies>
        <dependency>
            <groupId>com.wcompass.edgs.cloud.extractor</groupId>
            <artifactId>edgs-base-extractor</artifactId>
            <version>4.0.0</version>
        </dependency>


        <dependency>
            <groupId>jdbc.sybase</groupId>
            <artifactId>jconn4</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>edgs-sybase-ase-extractor</finalName>
    </build>

</project>
