package com.wcompass.edgs.core.databricks;

import cn.hutool.core.io.FileUtil;
import com.wcompass.edgs.core.databricks.model.*;
import com.wcompass.edgs.exception.SystemException;
import com.wcompass.edgs.utils.CollectionUtil;
import com.wcompass.edgs.utils.DateUtil;
import com.wcompass.edgs.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.lang.NonNull;
import org.springframework.util.StreamUtils;
import org.springframework.web.client.ResponseErrorHandler;
import org.springframework.web.client.RestClient;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * databricks restful client 封装
 */
@Slf4j
public class DatabricksRestClient {

    private final RestClient restClient;

    private final String LIMIT = "limit";

    private final String path;
    private final List<String> filterType;

    private final Set<String> directoryPaths = new HashSet<>();

    public String getPath() {
        return path;
    }

    DatabricksRestClient(String hostName, String token, String path, String filterType) {
        this.path = path;
        this.filterType = Arrays.stream(filterType.toLowerCase().split("[,，\\s]+"))
                .map(ext -> StringUtil.startsWith(ext, ".") ? ext : StringUtil.join(".", ext))
                .collect(Collectors.toList());
        this.restClient = RestClient.builder()
                .baseUrl(hostName)
                .defaultStatusHandler(new ResponseErrorHandler() {

                    @Override
                    public boolean hasError(@NonNull ClientHttpResponse response) throws IOException {
                        return response.getStatusCode().isError();
                    }

                    @Override
                    public void handleError(@NonNull ClientHttpResponse response) throws IOException {
                        if (response.getStatusCode().isError()) {
                            if (response.getStatusCode().value() == 401) {
                                throw SystemException.wrap("Databricks authentication failed");
                            }
                            String error = StreamUtils.copyToString(response.getBody(), StandardCharsets.UTF_8);
                            String message = String.format("%s - %s", response.getStatusCode().value(), error);
                            log.error("采集失败{}", message);
                            throw SystemException.wrap(message);
                        }
                    }
                })
                .defaultHeaders(httpHeaders -> {
                    httpHeaders.setBearerAuth(token);
                    httpHeaders.setAcceptCharset(List.of(StandardCharsets.UTF_8));
                    httpHeaders.setAccept(List.of(MediaType.APPLICATION_JSON));
                })
                .build();
    }

    public static DatabricksRestClientBuilder builder() {
        return new DatabricksRestClientBuilder();
    }


    public List<DatabricksConnection> listConnections(String connectionName) {
        String uri = DatabricksRestApiEnum.UNITY_CATALOG_CONNECTIONS.getApiUriBuilder()
                .queryParam("name", connectionName)
                .build()
                .toUriString();

        DatabricksResponse response = restClient.get()
                .uri(uri)
                .retrieve()
                .body(DatabricksResponse.class);

        List<DatabricksConnection> connections = new ArrayList<>();
        if (response != null && response.connections() != null && !response.connections().isEmpty()) {
            connections.addAll(response.connections());
        }
        return connections;
    }


    public List<DatabricksJob> listJobs() {
        String uri = DatabricksRestApiEnum.JOBS_LIST.getApiUriBuilder()
                .queryParam(LIMIT, 100)
                .build()
                .toUriString();

        DatabricksResponse response = restClient.get()
                .uri(uri)
                .retrieve()
                .body(DatabricksResponse.class);

        List<DatabricksJob> databricksJobs = new ArrayList<>();
        if (response != null && response.jobs() != null && !response.jobs().isEmpty()) {
            databricksJobs.addAll(response.jobs());
        }
        return databricksJobs;
    }

    public List<DatabricksJobRun> listJobRuns(String jobId) {
        String uri = DatabricksRestApiEnum.JOBS_RUNS_LIST.getApiUriBuilder()
                .queryParam("job_id", jobId)
                .queryParam(LIMIT, 26)
                .build()
                .toUriString();

        DatabricksResponse response = restClient.get()
                .uri(uri)
                .retrieve()
                .body(DatabricksResponse.class);

        List<DatabricksJobRun> databricksJobRuns = new ArrayList<>();
        if (response != null && response.runs() != null && !response.runs().isEmpty()) {
            databricksJobRuns.addAll(response.runs());
        }
        return databricksJobRuns;
    }

    public List<DatabricksRunView> listRunView(String runId, String viewsToExport) {
        String uri = DatabricksRestApiEnum.JOBS_RUNS_EXPORT.getApiUriBuilder()
                .queryParam("run_id", runId)
                .queryParam("views_to_export", viewsToExport)
                .build()
                .toUriString();

        DatabricksResponse response = restClient.get()
                .uri(uri)
                .retrieve()
                .body(DatabricksResponse.class);

        List<DatabricksRunView> databricksScripts = new ArrayList<>();
        if (response != null && response.views() != null && !response.views().isEmpty()) {
            databricksScripts.addAll(response.views());
        }
        return databricksScripts;
    }

    public void authenticate() {
        String uri = DatabricksRestApiEnum.WORKSPACE_LIST.get20ApiUriBuilder()
                .queryParam("path", this.path)
                .build().toUriString();
        restClient.get()
                .uri(uri)
                .retrieve()
                .body(new ParameterizedTypeReference<>() {
                });

    }

    public List<DatabricksScript> listDirectoryAndScripts() {
        List<WorkspaceObject.WorkspaceObjectDTO> workspaceObjectList = new ArrayList<>();
        this.workspaceFileList(workspaceObjectList, null, directoryPaths);
        return workspaceObjectList.stream().map((WorkspaceObject.WorkspaceObjectDTO workspaceObjectDTO) -> {
            String dtoPath = workspaceObjectDTO.getPath();
            String detail = this.workspaceFileDetail(dtoPath);
            DatabricksScript databricksScript = new DatabricksScript();
            String suffix = FileUtil.getSuffix(dtoPath);
            Path p = Paths.get(dtoPath);
            databricksScript.setName(p.toFile().getName().replace("." + suffix, ""));
            databricksScript.setCommand(detail);
            databricksScript.setLanguage(suffix);
            databricksScript.setPath(p.getParent().toString());
            databricksScript.setCreatedAt(DateUtil.formatDatetime(DateUtil.ofTime(Long.parseLong(workspaceObjectDTO.getCreatedAt()))));
            databricksScript.setModifiedAt(DateUtil.formatDatetime(DateUtil.ofTime(Long.parseLong(workspaceObjectDTO.getModifiedAt()))));
            return databricksScript;
        }).toList();
    }

    private void workspaceFileList(List<WorkspaceObject.WorkspaceObjectDTO> result, String path, Set<String> directoryPaths) {
        String uri = DatabricksRestApiEnum.WORKSPACE_LIST.get20ApiUriBuilder()
                .queryParam("path", path == null ? this.path : path)
                .build().toUriString();
        WorkspaceObject workspaceObject = restClient.get()
                .uri(uri)
                .retrieve()
                .body(new ParameterizedTypeReference<>() {
                });
        if (workspaceObject != null && CollectionUtil.isNotEmpty(workspaceObject.getObjects())) {
            for (WorkspaceObject.WorkspaceObjectDTO workspaceObjectDTO : workspaceObject.getObjects()) {
                String dtoPath = workspaceObjectDTO.getPath();
                if ("FILE".equalsIgnoreCase(workspaceObjectDTO.getObjectType())) {
                    String suffix = FileUtil.getSuffix(dtoPath.toLowerCase());
                    if (filterType.contains("." + suffix)) {
                        directoryPaths.add(Paths.get(dtoPath).getParent().toString().replace(File.separator, "/"));
                        result.add(workspaceObjectDTO);
                        log.debug("获取到原始脚本：{}", workspaceObjectDTO.getPath());
                    }
                } else {
                    this.workspaceFileList(result, dtoPath, directoryPaths);
                }
            }
        }
    }

    private String workspaceFileDetail(String path) {
        String uri = DatabricksRestApiEnum.WORKSPACE_EXPORT.get20ApiUriBuilder()
                .queryParam("path", path)
                .queryParam("format", "SOURCE")
                .queryParam("direct_download", "true")
                .build().toUriString();
        return restClient.get()
                .uri(uri)
                .retrieve()
                .body(new ParameterizedTypeReference<>() {
                });
    }

    public Set<String> getDirectoryPaths() {
        return directoryPaths;
    }
}
