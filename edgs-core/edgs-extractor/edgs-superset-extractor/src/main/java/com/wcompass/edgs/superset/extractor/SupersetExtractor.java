package com.wcompass.edgs.superset.extractor;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.wcompass.edgs.core.datasource.AdapterParam;
import com.wcompass.edgs.core.extract.MetaModel;
import com.wcompass.edgs.core.extract.Metadata;
import com.wcompass.edgs.core.extractor.ExtractRoot;
import com.wcompass.edgs.core.log.LogLevelEnum;
import com.wcompass.edgs.exception.SystemException;
import com.wcompass.edgs.extractor.AbstractExtractor;
import com.wcompass.edgs.superset.core.SupersetClient;
import com.wcompass.edgs.superset.core.model.*;
import com.wcompass.edgs.utils.CollectionUtil;
import com.wcompass.edgs.utils.DateUtil;
import com.wcompass.edgs.utils.ExceptionUtil;
import lombok.extern.slf4j.Slf4j;

import java.time.ZoneId;
import java.util.*;

/**
 * Superset 元数据采集器
 */
@Slf4j
public class SupersetExtractor extends AbstractExtractor {

    private SupersetClient client;

    private final Map<String, Map<String, String>> databaseCache = new HashMap<>();

    @Override
    public String getExtractRootClassifierId() {
        return MetaModel.SUPERSET;
    }

    @Override
    public void apiExtract() {
        ExtractRoot extractRoot = getExtractRoots().get(0);
        String extractRootName = extractRoot.getName();
        try {
            Metadata superset = buidlRootMetadata(extractRoot);

            log.info("开始加载仪表盘的元数据");
            log(LogLevelEnum.INFO, extractRootName, "开始加载仪表盘的元数据");
            List<Metadata> dashboards = loadDashboard();

            log.info("开始加载图表的元数据");
            log(LogLevelEnum.INFO, extractRootName, "开始加载图表的元数据");
            List<Metadata> charts = loadChart(dashboards);

            log.info("开始加载数据集的元数据");
            log(LogLevelEnum.INFO, extractRootName, "开始加载数据集的元数据");
            List<Metadata> datasets = loadDataset(charts);

            superset.addChildren(dashboards);
            write(extractRootName, superset);
        } catch (Exception e) {
            log.error("采集元数据失败", e);

            log(LogLevelEnum.ERROR, extractRootName, "采集元数据失败, {}", ExceptionUtil.getCauseMessage(e));

            failureCallback(getExtractTaskId(), getDatasourceId(), extractRootName);
        }
    }

    private List<Metadata> loadDataset(List<Metadata> charts) {
        List<Metadata> metadataList = new ArrayList<>();
        List<SupersetDataset> datasets = getClient().listDataset();
        for (Metadata chart : charts) {
            String datasetId = chart.getAttr("datasource_id");
            for (SupersetDataset dataset : datasets) {
                if (dataset.getId().toString().equals(datasetId)) {
                    Metadata datasetMd = new Metadata();
                    datasetMd.setCode(MetaModel.SUPERSET_DATASET + "-" + datasetId);
                    datasetMd.setName(dataset.getTableName());
                    datasetMd.setClassifierId(MetaModel.SUPERSET_DATASET);

                    datasetMd.addAttr("kind", dataset.getKind());
                    datasetMd.addAttr("datasource_type", dataset.getDatasourceType());
                    datasetMd.addAttr("schema", dataset.getSchema());
                    datasetMd.addAttr("table_name", dataset.getTableName());
                    datasetMd.addAttr("sql", dataset.getSql());
                    StringJoiner stringJoiner = new StringJoiner("，");
                    dataset.getOwners().forEach(userDTO -> stringJoiner.add(userDTO.getFirstName() + " " + userDTO.getLastName()));
                    datasetMd.addAttr("owners", stringJoiner.toString());
                    datasetMd.addAttr("changed_user", dataset.getChangedByName());
                    datasetMd.addAttr("changed_time", DateUtil.formatUTCWithZone(dataset.getChangedOnUtc(), ZoneId.of("Asia/Shanghai")));

                    //处理数据库
                    buildDatabase(datasetMd, dataset);
                    databaseCache.clear();

                    chart.addChild(datasetMd);

                    metadataList.add(datasetMd);
                    break;
                }
            }
        }
        return metadataList;
    }

    private void buildDatabase(Metadata datasetMd, SupersetDataset dataset) {
        SupersetDatabase database = dataset.getDatabase();
        Integer databaseId = database.getId();
        SupersetDatabase supersetDatabase = this.getClient().getDatabase(databaseId);

        Metadata databaseMetadata = new Metadata();
        databaseMetadata.setClassifierId(MetaModel.SUPERSET_DATABASE);
        databaseMetadata.setCode(MetaModel.SUPERSET_DATABASE + "-" + databaseId);
        databaseMetadata.setName(supersetDatabase.getDatabaseName());

        if (databaseCache.containsKey(databaseId.toString())) {
            databaseMetadata.addAttr(databaseCache.get(databaseId.toString()));
        } else {
            Map<String, String> attrs = new HashMap<>();
            attrs.put("type", supersetDatabase.getBackend());
            SupersetDatabase.ParametersDTO parameters = supersetDatabase.getParameters();
            attrs.put("host", parameters.getHost());
            attrs.put("database", parameters.getDatabase());
            attrs.put("port", parameters.getPort());
            attrs.put("username", parameters.getUsername());
            databaseMetadata.addAttr(attrs);
            databaseCache.put(databaseId.toString(), attrs);
        }

        datasetMd.addChild(databaseMetadata);
    }

    private List<Metadata> loadChart(List<Metadata> dashboards) {
        List<Metadata> metadataList = new ArrayList<>();
        List<SupersetChart> charts = getClient().listChart();
        for (SupersetChart chart : charts) {
            Metadata chartMetadata = new Metadata();
            chartMetadata.setUid(chart.getId().toString());
            chartMetadata.setCode(MetaModel.SUPERSET_CHART + "-" + chart.getId());
            chartMetadata.setName(chart.getSliceName());
            chartMetadata.setClassifierId(MetaModel.SUPERSET_CHART);
            chartMetadata.addAttr("viz_type", chart.getVizType());
            chartMetadata.addAttr("datasource_name", chart.getDatasourceNameText());
            chartMetadata.addAttr("datasource_type", chart.getDatasourceType());
            chartMetadata.addAttr("created_user", chart.getCreatedBy().getFirstName() + " " + chart.getCreatedBy().getLastName());
            chartMetadata.addAttr("changed_user", chart.getChangedByName());
            chartMetadata.addAttr("changed_time", DateUtil.formatUTCWithZone(chart.getChangedOnUtc(), ZoneId.of("Asia/Shanghai")));
            //数据集id
            chartMetadata.addAttr("datasource_id", chart.getDatasourceId().toString());

            List<SupersetDashboard> chartDashboards = chart.getDashboards();
            if (chartDashboards != null && !chartDashboards.isEmpty()) {
                dashboards.forEach((Metadata dashboard) -> {
                    for (SupersetDashboard chartDashboard : chartDashboards) {
                        if (chartDashboard.getId().toString().equals(dashboard.getUid())) {
                            dashboard.addChild(chartMetadata);
                        }
                    }
                });
            }

            SupersetChartFormData chartFormData = chart.getFormData();
            if (chartFormData != null) {
                //处理维度字段
                buildDimensions(chartMetadata, chartFormData);
                //处理指标字段
                buildMetric(chartMetadata, chartFormData);
                //处理指标字段
                buildMetrics(chartMetadata, chartFormData);
                //处理过滤器
                buildFilters(chartMetadata, chartFormData);
            }
            metadataList.add(chartMetadata);
        }

        return metadataList;
    }

    private static void buildFilters(Metadata chartMetadata, SupersetChartFormData chartFormData) {
        List<SupersetChartFormData.AdhocFiltersDTO> adhocFilters = chartFormData.getAdhocFilters();
        if (CollectionUtil.isNotEmpty(adhocFilters)) {
            for (SupersetChartFormData.AdhocFiltersDTO adhocFilter : adhocFilters) {
                Metadata filter = new Metadata();
                filter.setClassifierId(MetaModel.SUPERSET_FILTER);

                String expressionType = adhocFilter.getExpressionType();
                if ("SIMPLE".equals(expressionType)) {
                    if("No filter".equals(adhocFilter.getComparator())){
                        continue;
                    }
                    filter.setCode(adhocFilter.getFilterOptionName());
                    filter.setName(adhocFilter.getSubject() + "(" + adhocFilter.getOperator() + ")");
                } else if ("SQL".equals(expressionType)) {
                    filter.setCode(adhocFilter.getFilterOptionName());
                    filter.setName(adhocFilter.getSqlExpression());
                } else {
                    throw SystemException.wrap("暂不支持的表达式类型{【】}", expressionType);
                }
                filter.addAttr("clause", adhocFilter.getClause());
                filter.addAttr("comparator", adhocFilter.getComparator());
                filter.addAttr("expression_type", expressionType);
                filter.addAttr("operator", adhocFilter.getOperator());
                filter.addAttr("sql_expression", adhocFilter.getSqlExpression());
                filter.addAttr("subject", adhocFilter.getSubject());

                chartMetadata.addChild(filter);
            }
        }
    }

    private static void buildMetric(Metadata chartMetadata, SupersetChartFormData chartFormData) {
        SupersetChartFormData.MetricsDTO metric = chartFormData.getMetric();
        if (metric != null) {
            Metadata metricField = new Metadata();
            metricField.setClassifierId(MetaModel.SUPERSET_FIELD);

            metricField.setCode(metric.getOptionName());
            metricField.setName(metric.getLabel());
            metricField.addAttr("field_type", "METRIC");
            if ("SIMPLE".equals(metric.getExpressionType())) {
                metricField.addAttr("expression_type", "SIMPLE");
                metricField.addAttr("column_name", metric.getColumn().getColumnName());
                metricField.addAttr("column_type", metric.getColumn().getType());
                metricField.addAttr("aggregate", metric.getAggregate());
            } else if ("SQL".equals(metric.getExpressionType())) {
                metricField.addAttr("expression_type", "SQL");
                metricField.addAttr("sql_expression", metric.getSqlExpression());
            } else {
                throw SystemException.wrap("暂不支持的表达式类型{【】}", metric.getExpressionType());
            }
            chartMetadata.addChild(metricField);
        }
    }

    private static void buildMetrics(Metadata chartMetadata, SupersetChartFormData chartFormData) {
        List<SupersetChartFormData.MetricsDTO> metrics = chartFormData.getMetrics();
        if (CollectionUtil.isNotEmpty(metrics)) {
            for (SupersetChartFormData.MetricsDTO metric : metrics) {
                Metadata metricField = new Metadata();
                metricField.setClassifierId(MetaModel.SUPERSET_FIELD);

                metricField.setCode(metric.getOptionName());
                metricField.setName(metric.getLabel());
                metricField.addAttr("field_type", "METRIC");
                if ("SIMPLE".equals(metric.getExpressionType())) {
                    metricField.addAttr("expression_type", "SIMPLE");
                    metricField.addAttr("column_name", metric.getColumn().getColumnName());
                    metricField.addAttr("column_type", metric.getColumn().getType());
                    metricField.addAttr("aggregate", metric.getAggregate());
                } else if ("SQL".equals(metric.getExpressionType())) {
                    metricField.addAttr("expression_type", "SQL");
                    metricField.addAttr("sql_expression", metric.getSqlExpression());
                } else {
                    throw SystemException.wrap("暂不支持的表达式类型{【】}", metric.getExpressionType());
                }
                chartMetadata.addChild(metricField);
            }
        }
    }

    private static void buildDimensions(Metadata chartMetadata, SupersetChartFormData chartFormData) {
        JSONArray groupby = chartFormData.getGroupby();
        if (groupby != null && !groupby.isEmpty()) {
            for (Object groupColumn : groupby) {
                Metadata dimensionsField = new Metadata();
                dimensionsField.setClassifierId(MetaModel.SUPERSET_FIELD);

                String gColumn = groupColumn.toString();
                //维度-自定义sql类型
                if (JSONUtil.isTypeJSONObject(gColumn)) {
                    JSONObject jsonObject = JSONUtil.parseObj(groupColumn);
                    dimensionsField.setCode(jsonObject.getStr("label"));
                    dimensionsField.setName(jsonObject.getStr("label"));
                    dimensionsField.addAttr("field_type", "DIMENSIONS");
                    dimensionsField.addAttr("expression_type", jsonObject.getStr("expressionType"));
                    dimensionsField.addAttr("sql_expression", jsonObject.getStr("sqlExpression"));
                } else {
                    //普通类型
                    dimensionsField.setCode(gColumn);
                    dimensionsField.setName(gColumn);
                    dimensionsField.addAttr("column_name", gColumn);
                    dimensionsField.addAttr("expression_type", "SIMPLE");
                    dimensionsField.addAttr("field_type", "DIMENSION");
                }
                chartMetadata.addChild(dimensionsField);
            }
        }
    }

    private List<Metadata> loadDashboard() {
        List<Metadata> metadataList = new ArrayList<>();
        List<SupersetDashboard> dashboards = getClient().listDashboard();
        for (SupersetDashboard dashboard : dashboards) {
            Metadata metadata = new Metadata();
            metadata.setCode(MetaModel.SUPERSET_DASHBOARD + "-" + dashboard.getId());
            metadata.setName(dashboard.getDashboardTitle());
            metadata.setClassifierId(MetaModel.SUPERSET_DASHBOARD);
            metadata.setUid(dashboard.getId().toString());
            metadata.addAttr("status", dashboard.getStatus());
            metadata.addAttr("created_user", dashboard.getCreatedBy().getFirstName() + " " + dashboard.getCreatedBy().getLastName());
            metadata.addAttr("changed_user", dashboard.getChangedByName());
            metadata.addAttr("changed_time", DateUtil.formatUTCWithZone(dashboard.getChangedOnUtc(), ZoneId.of("Asia/Shanghai")));

            metadataList.add(metadata);
        }

        return metadataList;
    }


    private Metadata buidlRootMetadata(ExtractRoot extractRoot) {
        Metadata metadata = new Metadata();
        metadata.setCode(extractRoot.getName());
        metadata.setName(extractRoot.getName());
        metadata.setClassifierId(extractRoot.getClassifierId());
        return metadata;
    }

    private SupersetClient getClient() {
        if (client == null) {
            client = SupersetClient.builder()
                    .server(getParamValue(AdapterParam.SERVER))
                    .username(getSource().getUsername())
                    .password(getSource().getPassword())
                    .provider(getParamValue(AdapterParam.PROVIDER))
                    .build();
        }
        return client;
    }
}
