package com.wcompass.edgs.extractor;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wcompass.edgs.core.datasource.AdapterParam;
import com.wcompass.edgs.core.extract.MetaModel;
import com.wcompass.edgs.core.extract.Metadata;
import com.wcompass.edgs.core.extractor.ExtractRoot;
import com.wcompass.edgs.core.log.LogLevelEnum;
import com.wcompass.edgs.extractor.core.Entity;
import com.wcompass.edgs.extractor.core.PbDataSet;
import com.wcompass.edgs.extractor.core.PbReport;
import com.wcompass.edgs.extractor.core.formula.Field;
import com.wcompass.edgs.extractor.core.formula.FormulaInfo;
import com.wcompass.edgs.utils.*;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024-06-13 15:31
 */
@Slf4j
public class PowerBiFileExtractor extends AbstractExtractor{
    @Override
    public String getExtractRootClassifierId() {
        return MetaModel.Power_BI;
    }

    @Override
    public void serverFileExtract() {
        ExtractRoot extractRoot = getExtractRoots().get(0);
        String extractRootName = extractRoot.getName();
        String filePath = getParamValue(AdapterParam.FILE_PATH);

        try {
            Metadata root = buidlRootMetadata(extractRoot);
            List<PbReport> pbReports = new ArrayList<>();
            PowerBiFileLoaderService service = new PowerBiFileLoaderService(filePath);
            log(LogLevelEnum.INFO, extractRootName, "开始加载文件数据");
            service.load(pbReports);
            log(LogLevelEnum.INFO, extractRootName, "加载文件数据结束");

            log(LogLevelEnum.INFO, extractRootName, "开始组合报表项和数据集之间的关系");
            loadReport(pbReports, root);
            write(extractRootName, root);
        }catch (Exception e) {
            log.error("采集元数据失败", e);

            log(LogLevelEnum.ERROR, extractRootName, "采集元数据失败, {}", ExceptionUtil.getCauseMessage(e));

            failureCallback(getExtractTaskId(), getDatasourceId(), extractRootName);
        }


    }

    private void loadReport(List<PbReport> pbReports, Metadata root) throws JsonProcessingException {

        if (CollectionUtil.isNotEmpty(pbReports)) {
            for (PbReport report:pbReports) {
                Set<String> items = new HashSet<>();
                Map<String,Set<String>> queryMap = new HashMap<>();
                Map<String, Entity> exprMap = new HashMap<>();

                //数据集对应数据集字段缓存
                Map<String,Metadata> fieldMap = new HashMap<>();
                Map<String,Metadata> datasetMap = new HashMap<>();

                List<PbDataSet> dataSets = report.getDataSets();
                loadDataSet(dataSets, root, fieldMap, datasetMap, report);

                List<String> dataTransformsJsons = report.getDataTransformsJsons();
                if (CollectionUtil.isNotEmpty(dataTransformsJsons)) {
                    for (String jsonString:dataTransformsJsons) {
                        Map<String, Object> dataTransformsMap = new HashMap<>();
                        if (StringUtil.isNotBlank(jsonString)) {
                            dataTransformsMap = JsonUtil.parseMap(jsonString, String.class, Object.class);
                        }
                        if (CollectionUtil.isNotEmpty(dataTransformsMap)) {
                            List<LinkedHashMap<String,Object>> selects = (List<LinkedHashMap<String,Object>> )dataTransformsMap.get("selects");
                            if (CollectionUtil.isNotEmpty(selects)) {

                                for (LinkedHashMap<String,Object> map:selects) {
                                    String displayName = (String) map.get("displayName");
                                    String queryName = (String) map.get("queryName");
                                    items.add(displayName);

                                    Map<String,Object> expr = (Map<String, Object>) map.get("expr");
                                    if (CollectionUtil.isNotEmpty(expr)) {
                                        Entity vo = new Entity();
                                        getEntity(vo,expr);
                                        exprMap.put(queryName, vo);
                                    }

                                    Set<String> set = queryMap.get(displayName);
                                    if (CollectionUtil.isNotEmpty(set)) {
                                        set.add(queryName);
                                    }else {
                                        set = new HashSet<>();
                                        set.add(queryName);
                                        queryMap.put(displayName, set);
                                    }

                                }
                            }

                        }

                    }
                }
                Metadata reportMetadata = getMetadata(report, items, queryMap, exprMap, fieldMap, datasetMap);
                root.addChild(reportMetadata);


            }

        }


    }

    private static void loadDataSet(List<PbDataSet> dataSets, Metadata root,
                                    Map<String,Metadata> fieldMap,
                                    Map<String,Metadata> datasetMap,
                                    PbReport report) {

        if (CollectionUtil.isNotEmpty(dataSets)) {
            for (PbDataSet dataSet:dataSets) {
                String datasetName = dataSet.getDatasetName();
                List<Field> colDatas = dataSet.getColDatas();
                Metadata datasetMetadata = new Metadata();
                String datasetCode = StringUtil.join(datasetName);
                datasetMetadata.setCode(datasetCode);
                datasetMetadata.setName(datasetName);
                datasetMetadata.setClassifierId(MetaModel.PB_Dataset);
                // 设置SQL、database等信息
                FormulaInfo info = dataSet.getInfo();
                if (info != null) {
                    datasetMetadata.addAttr("datasource", info.getDatasource());
                    datasetMetadata.addAttr("database", info.getDatabase());
                    datasetMetadata.addAttr("schema", info.getSchema());
                    datasetMetadata.addAttr("table", info.getTable());
                    datasetMetadata.addAttr("formula", info.getFormula());
                    datasetMetadata.addAttr("querySql", String.join(";" ,info.getTranSql()));
                }

                for (Field colData:colDatas) {
                    String formula = null;
                    Object object;
                    if ("measure".equals(datasetName)) {
                        object = colData.getName();
                        formula = colData.getFormula();
                    } else{
                        Map<String, Object> colMap = JsonUtil.parseMap(colData.getJsonData(), String.class, Object.class);
                        object = colMap.get("name");
                    }
                    if (object != null) {
                        String fieldName = object.toString();
                        Metadata fieldMetadata = new Metadata();
                        fieldMetadata.setCode(StringUtil.join(datasetCode, ".", fieldName));
                        fieldMetadata.setName(fieldName);
                        fieldMetadata.setClassifierId(MetaModel.PB_FIELD);
                        fieldMetadata.addAttr("dax_formula", formula);
                        datasetMetadata.addChild(fieldMetadata);
                        fieldMap.put(StringUtil.join(datasetName, "_", fieldName), fieldMetadata);
                    }
                }
                datasetMap.put(datasetName, datasetMetadata);
                report.addMetadataDataSet(datasetMetadata);
            }
        }
    }

    private static Metadata getMetadata(PbReport report, Set<String> items,
                                        Map<String,Set<String>> queryMap,
                                        Map<String, Entity> exprMap,
                                        Map<String,Metadata> fieldMap,
                                        Map<String,Metadata> datasetMap) {
        Metadata reportMetadata = new Metadata();
        reportMetadata.setCode(report.getReportName());
        reportMetadata.setName(report.getReportName());
        reportMetadata.setClassifierId(MetaModel.PB_PowerBIReport);
        for (String item: items) {
            Metadata itemMetadata = new Metadata();
            itemMetadata.setCode(item);
            itemMetadata.setName(item);
            itemMetadata.setClassifierId(MetaModel.PB_ReportItem);
            //获取查询字段
            Set<String> queryFields = queryMap.get(item);
            StringBuilder sb = new StringBuilder();
            String datasetId = null;
            if (CollectionUtil.isNotEmpty(queryFields)) {
                Set<String> distinctDatasetIds = new HashSet<>();
                for (String queryField:queryFields) {

                    Entity entity = exprMap.get(queryField);
                    if (entity != null) {
                        Metadata fieldMetadata = fieldMap.get(StringUtil.join(entity.getEntity(), "_", entity.getProperty()));
                        Metadata datasetMetadata = datasetMap.get(entity.getEntity());
                        if (datasetMetadata != null) {
                            String code = datasetMetadata.getCode();
                            if (StringUtil.isNotBlank(datasetId) && !distinctDatasetIds.contains(code)) {
                                Metadata newMetadata = new Metadata();
                                newMetadata.setCode(item);
                                newMetadata.setName(item);
                                newMetadata.setClassifierId(MetaModel.PB_ReportItem);
                                Map<String, String> attrs = new HashMap<>();
                                if (fieldMetadata != null) {
                                    attrs.put("fieldId", fieldMetadata.getCode());
                                }
                                newMetadata.setCode(StringUtil.join(item, "--", code));
                                attrs.put("datasetId", code);
                                newMetadata.setAttrs(attrs);
                                reportMetadata.addChild(newMetadata);
                                distinctDatasetIds.add(code);
                                continue;
                            } else {
                                datasetId = code;
                                distinctDatasetIds.add(code);
                            }
                        }
                        if (fieldMetadata != null) {
                            if (StringUtil.isNotBlank(sb)) {
                                sb.append(",");
                            }
                            sb.append(fieldMetadata.getCode());
                        }
                    }
                }
            }
            Map<String, String> attrs = new HashMap<>();
            attrs.put("fieldId", sb.toString());
            attrs.put("datasetId", datasetId);
            itemMetadata.setCode(StringUtil.join(item, "--", datasetId));
            itemMetadata.setAttrs(attrs);
            reportMetadata.addChild(itemMetadata);
        }
        reportMetadata.addChildren(report.getMetadataDataSets());
        return reportMetadata;
    }


    private Metadata buidlRootMetadata(ExtractRoot extractRoot) {
        Metadata metadata = new Metadata();
        //获取powerBiServer
        metadata.setCode(extractRoot.getName());
        metadata.setName(extractRoot.getName());
        metadata.setClassifierId(extractRoot.getClassifierId());
        metadata.addAttr("filePath", getParamValue("filePath"));
        return metadata;
    }


    private static void getEntity(Entity vo, Map<String,Object> expr) throws JsonProcessingException {
        extractData(vo, JsonUtil.toJsonString(expr));
    }

    public static void extractData(Entity vo, String jsonStr) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(jsonStr);
        extractNodeValues(vo, rootNode);
    }

    private static void extractNodeValues(Entity vo, JsonNode node) {
        if (node != null) {
            if (node.has("Entity")) {
                vo.setEntity(node.get("Entity").asText());
            }

            if (node.has("Property")) {
                vo.setProperty(node.get("Property").asText());
            }
            // 遍历所有字段
            Iterator<Map.Entry<String, JsonNode>> it = node.fields();
            while (it.hasNext()) {
                Map.Entry<String, JsonNode> entry = it.next();
                extractNodeValues(vo, entry.getValue());
            }
        }
    }






















}
