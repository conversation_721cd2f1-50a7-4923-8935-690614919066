package com.wcompass.edgs.core;

import com.wcompass.edgs.core.validation.ValidatorUtil;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class SshServerClientBuilder {

    @NotBlank(message = "服务器地址不能为空")
    private String host;

    @NotBlank(message = "连接用户名不能为空")
    private String username;

    private Integer port;

    @NotBlank(message = "连接用户名对应密码不能为空")
    private String password;

    private Integer connectTimeout;

    public SshServerClientBuilder host(String host) {
        this.host = host;
        return this;
    }

    public SshServerClientBuilder username(String username) {
        this.username = username;
        return this;
    }

    public SshServerClientBuilder port(Integer port) {
        this.port = port;
        return this;
    }

    public SshServerClientBuilder password(String password) {
        this.password = password;
        return this;
    }

    public SshServerClientBuilder connectTimeout(Integer connectTimeout) {
        this.connectTimeout = connectTimeout;
        return this;
    }



    public SshServerClient build() {
        ValidatorUtil.validateWithThrowError(this);
        return new SshServerClient(host, username, port, password, connectTimeout);
    }
}
