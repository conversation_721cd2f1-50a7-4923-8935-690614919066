<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wcompass.edgs.modules.parser.dao.read.DataFlowTaskViewReadMapper">
    <select id="getParseTaskView" resultType="com.wcompass.edgs.modules.parser.model.taskView.ParseTaskViewVO" databaseId="mysql">
        SELECT
        COALESCE(SUM(CASE WHEN subquery.parse_status = 0 THEN count ELSE 0 END), 0) AS parse,
        COALESCE(SUM(CASE WHEN subquery.parse_status = 1 THEN count ELSE 0 END), 0) AS success,
        COALESCE(SUM(CASE WHEN subquery.parse_status = 2 THEN count ELSE 0 END), 0) AS warn,
        COALESCE(SUM(CASE WHEN subquery.parse_status = 3 THEN count ELSE 0 END), 0) AS fail,
        COALESCE(SUM(CASE WHEN subquery.parse_status = 9 THEN count ELSE 0 END), 0) AS parsing,
        COALESCE(SUM(count), 0) AS total
        FROM (
        SELECT
        t1.parse_status,
        count(*) AS count
        FROM
        t01_dataflow_parse_log t1
        JOIN t01_instance t2
        ON t1.namespace LIKE concat(t2.namespace, '%')
        AND t2.classifier_id = 'Root'
        <where>
            <if test="nameList != null and nameList.size > 0">
                AND t2.instance_code IN
                <foreach collection="nameList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="recentBatch != null and recentBatch != ''">
                AND t1.recent_batch = #{recentBatch}
            </if>
        </where>
        GROUP BY
        t1.parse_status
        ) subquery
    </select>
    <select id="getParseTaskView" resultType="com.wcompass.edgs.modules.parser.model.taskView.ParseTaskViewVO" databaseId="kingbase">
        SELECT
        COALESCE(SUM(CASE WHEN subquery.parse_status = 0 THEN count ELSE 0 END), 0) AS parse,
        COALESCE(SUM(CASE WHEN subquery.parse_status = 1 THEN count ELSE 0 END), 0) AS success,
        COALESCE(SUM(CASE WHEN subquery.parse_status = 2 THEN count ELSE 0 END), 0) AS warn,
        COALESCE(SUM(CASE WHEN subquery.parse_status = 3 THEN count ELSE 0 END), 0) AS fail,
        COALESCE(SUM(CASE WHEN subquery.parse_status = 9 THEN count ELSE 0 END), 0) AS parsing,
        COALESCE(SUM(count), 0) AS total
        FROM (
        SELECT
        t1.parse_status,
        count(*) AS count
        FROM
        t01_dataflow_parse_log t1
        JOIN t01_instance t2
        ON t1.namespace LIKE t2.namespace || '%'
        AND t2.classifier_id = 'Root'
        <where>
            <if test="nameList != null and nameList.size > 0">
                AND t2.instance_code IN
                <foreach collection="nameList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="recentBatch != null and recentBatch != ''">
                AND t1.recent_batch = #{recentBatch}
            </if>
        </where>
        GROUP BY
        t1.parse_status
        ) subquery
    </select>
    <select id="getAutomaticParseView" resultType="com.wcompass.edgs.modules.parser.model.taskView.AutomaticParseViewVO">
        SELECT
        COALESCE(SUM(CASE WHEN subquery.parse_status = '0' OR subquery.parse_status = '1' THEN count ELSE 0 END), 0) AS wait,
        COALESCE(SUM(CASE WHEN subquery.parse_status = '4' THEN count ELSE 0 END), 0) AS progress,
        COALESCE(SUM(CASE WHEN subquery.parse_status = '2' THEN count ELSE 0 END), 0) AS success,
        COALESCE(SUM(CASE WHEN subquery.parse_status = '3' THEN count ELSE 0 END), 0) AS fail,
        COALESCE(SUM(count), 0) AS total
        FROM
        (
        SELECT
        t1.parse_status,
        count(*) AS count
        FROM
        t01_dataflow_automatic_parse t1
        JOIN t99_datasource t2
        ON
        t1.datasource_id = t2.datasource_id
        <where>
            <if test="nameList != null and nameList.size > 0">
                AND t2.datasource_id IN
                <foreach collection="nameList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
        t1.parse_status) subquery
    </select>
    <select id="listAutoParseTask" resultType="com.wcompass.edgs.modules.parser.model.taskView.AutoParseTaskVO">
        SELECT
        t1.id,
        t1.job_name AS systemTaskName,
        t3.code_item_name AS extractTargetType,
        t1.description,
        t4.datasource_name
        FROM
        t01_extract_job t1
        JOIN t01_extract_datasource t2 ON
        t1.id = t2.extract_job_id
        JOIN t99_dic t3 ON
        t1.source_type = t3.code_item
        AND t3.dict_code = 'METADATA_EXTRACT_SOURCE_TYPE'
        JOIN t99_datasource t4 ON t2.datasource_id = t4.datasource_id
        WHERE
            t1.enable_parse_scan = 'Y'
            <if test="datasourceIdList != null and datasourceIdList.size > 0">
                AND t2.datasource_id IN
                <foreach collection="datasourceIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
    </select>
    <select id="getDatasourceSelect" parameterType="java.lang.String" resultType="com.wcompass.edgs.core.Option">
        SELECT
            t3.datasource_id AS value,
            t3.datasource_name AS label
        FROM
            t99_datasource_adapter t1
                JOIN t99_datasource_adapter_mode t2
                     ON
                         t1.adapter_id = t2.adapter_id
                JOIN t99_datasource t3
                     ON
                         t3.mode_id = t2.mode_id
        WHERE
            t1.adapter_id = #{smallCategoryCode}
    </select>
    <select id="getDatasourceIdByTypeList" parameterType="java.util.List" resultType="java.lang.String">
        SELECT
            t3.datasource_id
        FROM
            t99_datasource_adapter t1
                JOIN t99_datasource_adapter_mode t2
                     ON
                         t1.adapter_id = t2.adapter_id
                JOIN t99_datasource t3
                     ON
                         t3.mode_id = t2.mode_id
        WHERE
            t1.adapter_id IN
        <foreach collection="typeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="listDeleteLogId" resultType="java.lang.Integer" databaseId="mysql">
        SELECT
        t1.id
        FROM
        t01_dataflow_parse_log t1
        JOIN t01_instance t2
        ON
        t1.namespace LIKE concat(t2.namespace, '%')
        AND t2.classifier_id = 'Root'
        <where>
            <if test="nameList != null and nameList.size > 0">
                AND t2.instance_code IN
                <foreach collection="nameList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="recentBatch != null and recentBatch != ''">
                AND t1.recent_batch = #{recentBatch}
            </if>
        </where>
    </select>
    <select id="listDeleteLogId" resultType="java.lang.Integer" databaseId="kingbase">
        SELECT
        t1.id
        FROM
        t01_dataflow_parse_log t1
        JOIN t01_instance t2
        ON
        t1.namespace LIKE t2.namespace || '%'
        AND t2.classifier_id = 'Root'
        <where>
            <if test="nameList != null and nameList.size > 0">
                AND t2.instance_code IN
                <foreach collection="nameList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="recentBatch != null and recentBatch != ''">
                AND t1.recent_batch = #{recentBatch}
            </if>
        </where>
    </select>
</mapper>