spring:
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
    banner-mode: log
  cloud:
    nacos:
      config:
        shared-configs:
          - data-id: edgs-common.yml
          - data-id: edgs-sql-parser.yml
        server-addr: **************:8848
        namespace: maoninglang
        prefix: bootstrap-88
        file-extension: yml
        username: nacos
        password: 9bH8ljpYxdgMphr9+UzkYA==
    discovery:
      client:
        composite-indicator:
          enabled: false


---


spring:
  jackson:
    time-zone: UTC
    date-format: "yyyy-MM-dd HH:mm:ss"
  flyway:
    baseline-on-migrate: true
    baseline-version: 1.0.0
    validate-migration-naming: true


registry:
  type: zookeeper
  zookeeper:
    namespace: dolphinscheduler
    connect-string: **************:2181
    retry-policy:
      base-sleep-time: 60ms
      max-sleep: 300ms
      max-retries: 5
    session-timeout: 60s
    connection-timeout: 30s
    block-until-connected: 60s
    digest: ~


worker:
  # worker listener port
  listen-port: 8233
  # worker execute thread number to limit task instances in parallel
  exec-threads: 100
  # worker heartbeat interval
  max-heartbeat-interval: 10s
  # worker host weight to dispatch tasks, default value 100
  host-weight: 100
  server-load-protection:
    # If set true, will open worker overload protection
    enabled: false
    # Worker max system cpu usage, when the worker's system cpu usage is smaller then this value, worker server can be dispatched tasks.
    max-system-cpu-usage-percentage-thresholds: 0.7
    # Worker max jvm cpu usage, when the worker's jvm cpu usage is smaller then this value, worker server can be dispatched tasks.
    max-jvm-cpu-usage-percentage-thresholds: 0.7
    # Worker max System memory usage , when the master's system memory usage is smaller then this value, master server can execute workflow.
    max-system-memory-usage-percentage-thresholds: 0.98
    # Worker max disk usage , when the worker's disk usage is smaller then this value, worker server can be dispatched tasks.
    max-disk-usage-percentage-thresholds: 0.7
  registry-disconnect-strategy:
    # The disconnect strategy: stop, waiting
    strategy: waiting
    # The max waiting time to reconnect to registry if you set the strategy to waiting
    max-waiting-time: 100s
  task-execute-threads-full-policy: REJECT
  tenant-config:
    # tenant corresponds to the user of the system, which is used by the worker to submit the job. If system does not have this user, it will be automatically created after the parameter worker.tenant.auto.create is true.
    auto-create-tenant-enabled: true
    # Scenes to be used for distributed users. For example, users created by FreeIpa are stored in LDAP. This parameter only applies to Linux, When this parameter is true, auto-create-tenant-enabled has no effect and will not automatically create tenants.
    distributed-tenant-enabled: false
    # If set true, will use worker bootstrap user as the tenant to execute task when the tenant is `default`.
    default-tenant-enabled: false
    default-tenant-code: pengpei
  project: edgs
  default-group: ${spring.application.name}
  worker-address: **************

management:
  endpoints:
    web:
      exposure:
        include:
          - health
          - metrics
          - prometheus
          - logfile
  endpoint:
    health:
      enabled: true
      show-details: always
  health:
    db:
      enabled: true
    defaults:
      enabled: false
  metrics:
    tags:
      application: ${spring.application.name}

metrics:
  enabled: true

---

edgs:
  dolphinscheduler:
    server-addr: **************:12345
    username: admin
    password: Compass@2025


