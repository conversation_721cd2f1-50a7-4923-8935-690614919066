package com.wcompass.edgs.modules.core.handler.mapping;

import com.wcompass.edgs.exception.SystemException;
import com.wcompass.edgs.modules.parser.dao.read.mapping.BISyncConnectionReadMapper;
import com.wcompass.edgs.modules.parser.model.mapping.DataflowSyncConnectionVO;
import jakarta.annotation.Resource;

import java.util.List;

/**
 * @Description:
 * @Author: wenshijin
 * @Date: 2025年06月02日14:02
 */

public abstract class B<PERSON>atasourceHandler extends AbstractDatasourceHandler  {

    @Resource
    private BISyncConnectionReadMapper biSyncConnectionReadMapper;

    protected List<DataflowSyncConnectionVO> queryFineReportConnection(String datasourceId) {
        return queryConnectionsWithValidation(datasourceId, (namespace) -> biSyncConnectionReadMapper.queryFineDBConnection(namespace));
    }

    protected List<DataflowSyncConnectionVO> queryGunaBIConnection(String datasourceId) {
        return queryConnectionsWithValidation(datasourceId, (namespace) -> biSyncConnectionReadMapper.queryGunaBIConnection(namespace));
    }

    protected List<DataflowSyncConnectionVO> queryYHBIConnection(String datasourceId) {
        return queryConnectionsWithValidation(datasourceId, (namespace) -> biSyncConnectionReadMapper.queryYHBIConnection(namespace));
    }

    protected List<DataflowSyncConnectionVO> queryFineBIConnection(String datasourceId) {
        return queryConnectionsWithValidation(datasourceId, (namespace) -> biSyncConnectionReadMapper.queryFineBIConnection(namespace));
    }



    protected List<String> queryPowerBIConnect(String datasourceId) {
        String datasourceNamespace = getDatasourceNamespace(datasourceId);
        if (datasourceNamespace == null) {
            throw SystemException.warn("datasourceName: {}  未采集 ", datasourceId);
        }
        return biSyncConnectionReadMapper.queryPowerBIConnect(datasourceNamespace);
    }
}
