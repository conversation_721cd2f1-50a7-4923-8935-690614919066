package com.wcompass.edgs.modules.parser.service.impl;

import com.wcompass.edgs.core.Page;
import com.wcompass.edgs.modules.parser.dao.read.BlackListReadMapper;
import com.wcompass.edgs.modules.parser.dao.write.BlackListWriteMapper;
import com.wcompass.edgs.modules.parser.model.base.BlackListObjectVO;
import com.wcompass.edgs.modules.parser.model.base.SystemDatasourceVO;
import com.wcompass.edgs.modules.parser.service.BaseParseMetadataService;
import com.wcompass.edgs.modules.parser.service.BlackListService;
import com.wcompass.edgs.utils.DateUtil;
import com.wcompass.edgs.utils.StringUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: wenshijin
 * @Date: 2025年06月04日14:01
 */
@Service
@Slf4j
public class BlackListServiceImpl implements BlackListService {

    @Resource
    private BlackListReadMapper blackListReadMapper;

    @Resource
    private BlackListWriteMapper blackListWriteMapper;

    @Resource
    private BaseParseMetadataService baseParseMetadataService;

    @Override
    public void addBlackList(List<String> metadataIds, String userId) {
        blackListWriteMapper.insertDataflowBlackList(metadataIds, userId, DateUtil.now());
    }

    @Override
    public void removeBlackList(List<String> metadataIds) {
        blackListWriteMapper.removeDataflowBlackList(metadataIds);
    }

    @Override
    public Boolean checkMetadata(String metadataId) {
        return blackListReadMapper.countBlackList(metadataId);
    }

    @Override
    public Page<BlackListObjectVO> queryBlackListVOByPage(Page<BlackListObjectVO> page, String systemId
            , String datasourceId, String schemaId, Set<String> classifierIds, String keyword) {
        List<String> namespaceList = new ArrayList<>();
        Map<String, SystemDatasourceVO> namespaceMap = new HashMap<>();
        List<SystemDatasourceVO> systemDatasourceVOS = systemDatasourceVOS = baseParseMetadataService
                .queryNamespaceSchema(systemId, datasourceId, schemaId);
        if (StringUtil.isNotBlank(systemId)) {
            if (StringUtil.isNotBlank(schemaId)) {
                namespaceList = systemDatasourceVOS.stream().map(SystemDatasourceVO::getSchemaNamespacePath).distinct()
                        .map(s -> StringUtil.join(s, "%")).collect(Collectors.toList());
            } else {
                namespaceList = systemDatasourceVOS.stream().map(SystemDatasourceVO::getDatasourceCodePath).distinct()
                        .map(s -> StringUtil.join(s, "%")).collect(Collectors.toList());
            }
        }
        namespaceMap = systemDatasourceVOS.stream().collect(Collectors.toMap(SystemDatasourceVO::getSchemaNamespacePath,
                Function.identity(), (k1, k2) -> k1));

        blackListReadMapper.queryBlackListVOByPage(page, namespaceList, classifierIds, keyword);
        for (BlackListObjectVO blackListObjectVO : page.getRecords()) {
            for (String k : namespaceMap.keySet()) {
                if (blackListObjectVO.getNamespace().startsWith(k)) {
                    SystemDatasourceVO systemDatasourceVO = namespaceMap.get(k);
                    blackListObjectVO.setSystemId(systemDatasourceVO.getSystemId());
                    blackListObjectVO.setSystemName(systemDatasourceVO.getSystemName());
                    blackListObjectVO.setDatasourceId(systemDatasourceVO.getDatasourceId());
                    blackListObjectVO.setDatasourceName(systemDatasourceVO.getDatasourceName());
                    blackListObjectVO.setSchemaId(systemDatasourceVO.getSchemaId());
                    blackListObjectVO.setSchemaName(systemDatasourceVO.getSchemaCode());
                    break;
                }
            }

        }
        return page;
    }
}
