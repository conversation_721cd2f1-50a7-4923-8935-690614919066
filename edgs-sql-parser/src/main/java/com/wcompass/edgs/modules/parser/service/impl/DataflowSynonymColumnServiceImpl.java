package com.wcompass.edgs.modules.parser.service.impl;

import com.wcompass.api.OracleApplication;
import com.wcompass.edgs.modules.core.cache.MetaDataCacheServer;
import com.wcompass.edgs.modules.core.constant.enums.DatasourceConnectionType;
import com.wcompass.edgs.modules.core.constant.enums.DatasourceTypeEnum;
import com.wcompass.edgs.modules.parser.dao.read.DataflowSynonymColumnReadMapper;
import com.wcompass.edgs.modules.parser.dao.write.DataflowSynonymColumnWriteMapper;
import com.wcompass.edgs.modules.parser.model.base.ParserColumnInfo;
import com.wcompass.edgs.modules.parser.model.db.oracle.DataflowSynonymColumn;
import com.wcompass.edgs.modules.parser.model.db.oracle.SynonymVO;
import com.wcompass.edgs.modules.parser.model.mapping.DataflowDatasourceVO;
import com.wcompass.edgs.modules.parser.model.mapping.DatasourceMappingVO;
import com.wcompass.edgs.modules.parser.service.BaseParseMetadataService;
import com.wcompass.edgs.modules.parser.service.DataflowDatasourceMappingService;
import com.wcompass.edgs.modules.parser.service.DataflowSynonymColumnService;
import com.wcompass.edgs.utils.CollectionUtil;
import com.wcompass.edgs.utils.IdUtil;
import com.wcompass.edgs.utils.JsonUtil;
import com.wcompass.edgs.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: wenshijin
 * @Date: 2025年06月02日19:03
 */
@Service
@Slf4j
public class DataflowSynonymColumnServiceImpl implements DataflowSynonymColumnService {


    @Resource
    private DataflowSynonymColumnReadMapper dataflowSynonymColumnReadMapper;

    @Resource
    private DataflowSynonymColumnWriteMapper dataflowSynonymColumnWriteMapper;

    @Resource
    private BaseParseMetadataService BaseParseMetadataService;

    @Resource
    private DataflowDatasourceMappingService dataflowDatasourceMappingService;

    @Resource
    private MetaDataCacheServer metaDataCacheServer;

    @Override
    public void syncDataflowSynonymColumn() {
        try {
            // 1. 获取Oracle数据源
            log.info("开始获取Oracle类型的数据源");
            List<DataflowDatasourceVO> oracleDatasources = dataflowSynonymColumnReadMapper.queryAllOracleDatasourceVOList();
            log.info("获取Oracle类型的数据源成功，共获取到{}个数据源", oracleDatasources.size());

            // 2. 遍历处理每个数据源
            for (DataflowDatasourceVO datasource : oracleDatasources) {
                processOracleDatasource(datasource);
            }
        } catch (Exception e) {
            log.error("同步数据流同义词列时发生异常", e);
        }
    }

    private void processOracleDatasource(DataflowDatasourceVO datasource) {
        // 获取数据库命名空间
        String databaseNamespace = BaseParseMetadataService.queryNamespaceByDBId(datasource.getDatasourceId());
        if (StringUtil.isBlank(databaseNamespace)) {
            log.warn("数据源{}的命名空间为空，跳过处理", datasource.getDatasourceName());
            return;
        }

        // 获取同义词列表
        log.info("开始获取数据源{}下的同义词", datasource.getDatasourceName());
        List<SynonymVO> synonymList = dataflowSynonymColumnReadMapper.queryAllSynonymVOListByNamespace(databaseNamespace);

        if (CollectionUtil.isEmpty(synonymList)) {
            log.info("数据源{}下的同义词为空", datasource.getDatasourceName());
            return;
        }

        log.info("数据源{}下的同义词获取成功，共获取到{}个同义词",
                datasource.getDatasourceName(), synonymList.size());

        // 获取数据源映射关系
        List<DatasourceMappingVO> datasourceMappings = dataflowDatasourceMappingService
                .queryDatasourceMappingListByDatasourceIdAndAdepterName(
                        datasource.getDatasourceId(), DatasourceTypeEnum.ORACLE.getDisplayName());

        // 收集需要处理的数据源ID
        Set<String> datasourceIds = collectDatasourceIds(datasource, datasourceMappings);

        // 获取列信息映射
        Map<String, ParserColumnInfo> columnMap = metaDataCacheServer.get(datasourceIds);

        // 处理每个同义词
        for (SynonymVO synonym : synonymList) {
            synonym.setDatasourceId(datasource.getDatasourceId());
            try {
                processSynonym(datasource, synonym, datasourceMappings, columnMap);
            } catch (Exception e) {
                log.error("处理同义词时发生异常", e);
            }
        }
    }

    private Set<String> collectDatasourceIds(DataflowDatasourceVO datasource,
                                             List<DatasourceMappingVO> datasourceMappings) {
        Set<String> datasourceIds = new HashSet<>();

        if (CollectionUtil.isNotEmpty(datasourceMappings)) {
            datasourceIds.addAll(datasourceMappings.stream()
                    .filter(s -> DatasourceConnectionType.JDBC.getDatasourceType().equals(s.getConnectionType()))
                    .map(DatasourceMappingVO::getLocalDatasourceId)
                    .collect(Collectors.toSet()));
        }

        datasourceIds.add(datasource.getDatasourceId());
        return datasourceIds;
    }

    private void processSynonym(DataflowDatasourceVO datasource, SynonymVO synonym,
                                List<DatasourceMappingVO> datasourceMappings,
                                Map<String, ParserColumnInfo> columnMap) {
        log.info("开始同步数据源{}下的同义词{}", datasource.getDatasourceName(), synonym.getSynonymCode());

        String synonymDDL = synonym.getSynonymDDL();
        String createSynonymStmt = OracleApplication.getCreateSynonymSourceTable(synonymDDL);

        if (StringUtil.isBlank(createSynonymStmt)) {
            log.warn("数据源{}下的同义词{}的源表为空", datasource.getDatasourceName(), synonym.getSynonymCode());
            return;
        }

        if (createSynonymStmt.contains("@")) {
            processDbLinkSynonym(datasource, synonym, createSynonymStmt, datasourceMappings);
        } else {
            processRegularSynonym(datasource, synonym, createSynonymStmt, columnMap);
        }
    }

    private void processDbLinkSynonym(DataflowDatasourceVO datasource, SynonymVO synonym,
                                      String createSynonymStmt, List<DatasourceMappingVO> datasourceMappings) {
        log.info("数据源{}下的同义词{}的源表包含dblink {}",
                datasource.getDatasourceName(), synonym.getSynonymCode(), createSynonymStmt);

        List<DatasourceMappingVO> jdbcDbLinks = datasourceMappings.stream()
                .filter(s -> DatasourceConnectionType.DBLINK.getDatasourceType().equals(s.getConnectionType()))
                .collect(Collectors.toList());

        Map<String, DatasourceMappingVO> dblinkMapping = new HashMap<>();
        //只考虑一对一的link
        for (DatasourceMappingVO jdbcDbLink : jdbcDbLinks) {
            dblinkMapping.put(jdbcDbLink.getExternalConnectionName().toUpperCase(), jdbcDbLink);
        }

        if (CollectionUtil.isEmpty(jdbcDbLinks)) {
            log.warn("数据源{}下没有对应的dblink映射", datasource.getDatasourceName());
            return;
        }

        String[] split = createSynonymStmt.split("@");
        if (split.length != 2) {
            log.warn("同义词{}的创建语句格式不正确: {}", synonym.getSynonymCode(), createSynonymStmt);
            return;
        }

        String dblinkName = split[1];
        String tablePath = split[0];

        DatasourceMappingVO datasourceMappingVO = dblinkMapping.get(dblinkName.toUpperCase());
        String schemaCode = "";
        String tableCode = "";
        if (tablePath.contains(".")) {
            String[] pathParts = tablePath.split("\\.");
            if (pathParts.length == 2) {
                schemaCode = pathParts[0];
                tableCode = pathParts[1];
            } else {
                log.warn("同义词{}的表路径格式不正确: {}", synonym.getSynonymCode(), tablePath);
                return;
            }
        } else {
            schemaCode = datasourceMappingVO.getDefaultSchema();
            tableCode = tablePath;
        }
        if (StringUtil.isBlank(schemaCode) || StringUtil.isBlank(tableCode)) {
            log.warn("同义词{}的表路径格式不正确: {}", synonym.getSynonymCode(), tablePath);
            return;
        }

        List<ParserColumnInfo> columns = BaseParseMetadataService.getParserColumnInfoByTablePathAndDatasourceId(
                datasourceMappingVO.getLocalDatasourceId(), schemaCode, tableCode);
        addSynonymColumn(synonym, columns);
    }


    private void processRegularSynonym(DataflowDatasourceVO datasource, SynonymVO synonym,
                                       String createSynonymStmt, Map<String, ParserColumnInfo> columnMap) {
        List<String> columnKeys = columnMap.keySet().stream()
                .filter(s -> s.contains(StringUtil.join(createSynonymStmt, ".").toUpperCase()))
                .collect(Collectors.toList());


        if (CollectionUtil.isEmpty(columnKeys)) {
            log.warn("数据源{}下的同义词{}没有找到匹配的列信息",
                    datasource.getDatasourceName(), synonym.getSynonymCode());
            return;
        }
        List<ParserColumnInfo> columns = new ArrayList<>();

        for (String columnKey : columnKeys) {
            ParserColumnInfo parserColumnInfo = columnMap.get(columnKey);
            if (parserColumnInfo != null) {
                columns.add(parserColumnInfo);
            }
        }
        addSynonymColumn(synonym, columns);
    }

    private void addSynonymColumn(SynonymVO synonym, List<ParserColumnInfo> columnInfos) {
        if (CollectionUtil.isEmpty(columnInfos)) {
            log.warn("同义词{}没有找到匹配的列信息", synonym.getSynonymCode());
            return;
        }
        DataflowSynonymColumn dataflowSynonymColumn = dataflowSynonymColumnReadMapper.queryDataflowSynonymColumn(synonym);
        List<String> columnList = columnInfos.stream().map(ParserColumnInfo::getColumnCode).collect(Collectors.toList());
        if (dataflowSynonymColumn != null) {
            dataflowSynonymColumn.setColumnList(JsonUtil.toJsonString(columnList));
            dataflowSynonymColumnWriteMapper.updateDataflowSynonymColumn(dataflowSynonymColumn);
        } else {
            dataflowSynonymColumn = new DataflowSynonymColumn();
            dataflowSynonymColumn.setId(IdUtil.getSnowflakeId());
            dataflowSynonymColumn.setDatasourceId(synonym.getDatasourceId());
            dataflowSynonymColumn.setSchemaCode(synonym.getSchemaCode());
            dataflowSynonymColumn.setSynonymCode(synonym.getSynonymCode());
            dataflowSynonymColumn.setColumnList(JsonUtil.toJsonString(columnList));
            dataflowSynonymColumnWriteMapper.addDataflowSynonymColumn(dataflowSynonymColumn);
        }

    }

}
