package com.wcompass.edgs.modules.parser.service.impl;

import cn.hutool.core.date.DateUtil;
import com.wcompass.edgs.exception.SystemException;
import com.wcompass.edgs.modules.parser.dao.read.DataflowParseLogReadMapper;
import com.wcompass.edgs.modules.parser.model.base.SystemDatasourceVO;
import com.wcompass.edgs.modules.parser.service.BaseParseMetadataService;
import com.wcompass.edgs.modules.parser.service.ForeignService;
import com.wcompass.edgs.utils.RequestUtil;
import com.wcompass.edgs.utils.StringUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: wenshijin
 * @Date: 2025年06月04日15:52
 */
@Service
@Slf4j
public class ForeignServiceImpl implements ForeignService {

    @Resource
    private DataflowParseLogReadMapper dataflowParseLogReadMapper;

    @Resource
    private BaseParseMetadataService baseParseMetadataService;


    @Override
    public void exportDataflowLogs(String sessionId) {
        throw SystemException.wrap("暂时不支持当前导出");

//        List<SystemDatasourceVO> systemDatasourceVOS = systemDatasourceVOS = baseParseMetadataService
//                .queryNamespaceSchema(null, null, null);
//        Map<String, SystemDatasourceVO> namespaceMap = systemDatasourceVOS.stream().collect(Collectors.toMap(SystemDatasourceVO::getSchemaNamespacePath,
//                Function.identity(), (k1, k2) -> k1));
//        List<DataflowParseLogExportVO> dataflowParseLogExportVOS = dataflowParseLogReadMapper.queryDataFlowParseLogExportVO(sessionId);
//        for (DataflowParseLogExportVO dataflowParseLogExportVO : dataflowParseLogExportVOS) {
//            for (String k : namespaceMap.keySet()) {
//                if (dataflowParseLogExportVO.getNamespace().startsWith(k)) {
//                    SystemDatasourceVO systemDatasourceVO = namespaceMap.get(k);
//                    dataflowParseLogExportVO.setSystemName(systemDatasourceVO.getSystemName());
//                    dataflowParseLogExportVO.setDatasourceName(systemDatasourceVO.getDatasourceName());
//
//                    //生成对应的code namespace
//                    String namespace = dataflowParseLogExportVO.getNamespace();
//                    StringBuilder path = new StringBuilder();
//                    if (StringUtil.isNotBlank(namespace)) {
//                        String[] instanceIds = StringUtil.split(namespace, "/");
//                        for (int i = instanceIds.length - 1; i >= 0; i--) {
//                            MetadataVO instance = metadataCreateReadMapper.getMetadataByInstanceId(instanceIds[i]);
//                            if (instance != null && StringUtil.isNotBlank(instance.getClassifierId())) {
//                                if (instance.getClassifierId().equals("Root")) {
//                                    break;
//                                }
//                                path.insert(0, "/").insert(1, instance.getMetadataCode());
//                            }
//                        }
//                    }
//                    dataflowParseLogExportVO.setCodeNameSpace(path.toString());
//                    break;
//                }
//            }
//
//
//        }
//
//        HttpServletResponse response = RequestUtil.getResponse();
//        try {
//            String date = DateUtil.formatDateTime(new Date());
//            String fileName = URLEncoder.encode("血缘解析日志导出" + "-" + date + ".xlsx", StandardCharsets.UTF_8.toString());
//            response.addHeader("Content-Disposition", "attachment;filename=".concat(fileName));
//            response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
//            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
//            response.setCharacterEncoding(StandardCharsets.UTF_8.toString());
//
//            new DataFlowLogTemplateGenerator(dataflowParseLogExportVOS).generateWorkbook(response.getOutputStream());
//
//        } catch (Exception e) {
//
//        }


    }
}
