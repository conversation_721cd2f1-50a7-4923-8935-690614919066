package com.wcompass.edgs.modules.parser.dao.read;

import com.wcompass.edgs.core.Page;
import com.wcompass.edgs.modules.parser.model.mapping.DataflowDatasourceMapping;
import com.wcompass.edgs.modules.parser.model.mapping.DatasourceMappingVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @Description:
 * @Author: wenshijin
 * @Date: 2025年05月30日17:52
 */

public interface DataflowDatasourceMappingReadMapper {

    List<String> queryDatasourceMappingLocalDatasourceId();

    List<DatasourceMappingVO> queryDatasourceMappingListByDatasourceIdAndAdepterName(
            @Param("datasourceId") String datasourceId, @Param("adapterName") String adapterName);


    List<DataflowDatasourceMapping> queryDataflowDatasourceMappingVO(Long id);

    List<DatasourceMappingVO> queryDataflowDatasourceMappingVOByPage(
            @Param("page") Page<DatasourceMappingVO> page, @Param("adapterNames") List<String> adapterNames,
            @Param("keyword") String keyword, @Param("match") String match, @Param("sourceType") String sourceType, @Param("datasourceId") String datasourceId);

}
