package com.wcompass.edgs.modules.core.util;

import com.wcompass.api.model.CustomizeRelation;
import com.wcompass.api.model.ParseResult;
import com.wcompass.api.model.ParserColumn;
import com.wcompass.edgs.core.datasource.DatabaseType;
import com.wcompass.edgs.modules.core.annotations.DatasourceType;
import com.wcompass.edgs.modules.core.constant.SqlParserConstants;
import com.wcompass.edgs.modules.parser.model.base.ParserColumnInfo;
import com.wcompass.edgs.modules.parser.model.parser.MatchRelationContext;
import com.wcompass.edgs.modules.parser.model.parser.MetadataRelation;
import com.wcompass.edgs.modules.parser.model.parser.ParserTaskResult;
import com.wcompass.edgs.utils.CollectionUtil;
import com.wcompass.edgs.utils.StringUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: wenshijin
 * @Date: 2025年06月03日16:30
 */

public class CustomizeRelationUtil {

    public static List<CustomizeRelation> getTableCustomizeRelations(List<CustomizeRelation> customizeRelations) {
        List<CustomizeRelation> customizeRelationList = new ArrayList<>();
        for (CustomizeRelation customizeRelation : customizeRelations) {
            if (StringUtil.isBlank(customizeRelation.getSource().getColumnCode())) {
                customizeRelationList.add(customizeRelation);
            }
        }

        return customizeRelationList;
    }

    public static List<CustomizeRelation> getColumnCustomizeRelations(List<CustomizeRelation> customizeRelations) {
        List<CustomizeRelation> customizeRelationList = new ArrayList<>();
        for (CustomizeRelation customizeRelation : customizeRelations) {
            if (StringUtil.isNotBlank(customizeRelation.getSource().getColumnCode())) {
                customizeRelationList.add(customizeRelation);
            }
        }

        return customizeRelationList;
    }


    public static ParserTaskResult matchMetadataRelation(
            ParseResult parseResult, Map<String, ParserColumnInfo> columnMap, String metadataId, String classifierId
            , Integer parserLogId, String namespace, StringBuffer logDetails) {
        List<MetadataRelation> metadataRelationList = new ArrayList<>();
        Set<String> messageSet = new LinkedHashSet<>();
        Set<String> sourceTableTargetTableSet = new HashSet<>();

        if (!parseResult.isSuccess()) {
            LogUtil.error(logDetails, "SQL解析失败 {} ", parseResult.getErrorMessage());
            return new ParserTaskResult().setParserStatus(SqlParserConstants.ParseStatus.FAIL)
                    .setParserMessage(parseResult.getErrorMessage())
                    .setParserLogId(parserLogId)
                    .setMetadataId(metadataId)
                    .setParseLogDetails(logDetails);
        }

        List<CustomizeRelation> relations = parseResult.getCustomizeRelations();



        relations.forEach(relation -> {
            MetadataRelation metadataRelation = new MetadataRelation();
            //匹配表级血缘
            metadataRelation.setTransformId(metadataId);
            metadataRelation.setTransformClassifierId(classifierId);
            metadataRelation.setTransformNamespace(namespace);
            String sourceTablePath = relation.getSource().getTablePath();
            String targetTablePath = relation.getTarget().getTablePath();

            boolean logStatus = false;
            //防止出现多条无意义的打印日志: 表级血缘匹配日志
            if (!sourceTableTargetTableSet.contains(StringUtil.join(sourceTablePath, "_", targetTablePath))) {
                LogUtil.log(logDetails, "开始匹配表级血缘 源端：{} 目标端 {} ", sourceTablePath, targetTablePath);
                logStatus = true;
            }
            if (columnMap.containsKey(sourceTablePath)) {
                ParserColumnInfo parserColumnInfo = columnMap.get(sourceTablePath);
                metadataRelation.setSourceId(parserColumnInfo.getTableId());
                metadataRelation.setSourceClassifierId(parserColumnInfo.getTableClassifierId());
            } else {
                if (logStatus) {
                    messageSet.add(sourceTablePath);
                    LogUtil.warn(logDetails, "未找到源端对应的表或视图 {} ", sourceTablePath);
                }
            }

            if (columnMap.containsKey(targetTablePath)) {
                ParserColumnInfo parserColumnInfo = columnMap.get(targetTablePath);
                metadataRelation.setTargetId(parserColumnInfo.getTableId());
                metadataRelation.setTargetClassifierId(parserColumnInfo.getTableClassifierId());
            } else {
                if (logStatus) {
                    messageSet.add(targetTablePath);
                    LogUtil.warn(logDetails, "未找到目标端对应的表或视图 {} ", targetTablePath);
                }

            }
            sourceTableTargetTableSet.add(StringUtil.join(sourceTablePath, "_", targetTablePath));
            if (StringUtil.isNotBlank(metadataRelation.getSourceId()) && StringUtil.isNotBlank(
                    metadataRelation.getTargetId())) {
                metadataRelationList.add(metadataRelation);
                if (StringUtil.isBlank(relation.getTarget().getColumnCode()) ||
                        StringUtil.isBlank(relation.getSource().getColumnCode())) {
                    return;
                }

                MetadataRelation columnRelation = new MetadataRelation();
                columnRelation.setTransformId(metadataId);
                columnRelation.setTransformClassifierId(classifierId);
                columnRelation.setTransformNamespace(namespace);

                String sourceColumnPath = relation.getSource().getColumnPath();
                String targetColumnPath = relation.getTarget().getColumnPath();
                LogUtil.log(logDetails, "开始匹配字段级血缘 源端：{} 目标端 {} ", sourceColumnPath, targetColumnPath);
                if (columnMap.containsKey(sourceColumnPath)) {
                    ParserColumnInfo parserColumnInfo = columnMap.get(sourceColumnPath);
                    columnRelation.setSourceId(parserColumnInfo.getColumnId());
                    columnRelation.setSourceClassifierId(parserColumnInfo.getColumnClassifierId());
                } else {
                    LogUtil.warn(logDetails, "未找到源端对应的字段 {} ", sourceColumnPath);
                    messageSet.add(sourceColumnPath);
                }

                if (columnMap.containsKey(targetColumnPath)) {
                    ParserColumnInfo parserColumnInfo = columnMap.get(targetColumnPath);
                    columnRelation.setTargetId(parserColumnInfo.getColumnId());
                    columnRelation.setTargetClassifierId(parserColumnInfo.getColumnClassifierId());
                } else {
                    LogUtil.warn(logDetails, "未找到目标端对应的字段 {} ", targetColumnPath);
                    messageSet.add(targetColumnPath);
                }

                if (columnRelation.getSourceId() != null && columnRelation.getTargetId() != null) {
                    metadataRelationList.add(columnRelation);
                }

            }

        });
        List<MetadataRelation> metadataRelations = metadataRelationList.stream().filter(d -> !d.getSourceId()
                .equals(d.getTargetId())).distinct().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(
                        o -> StringUtil.join(o.getTransformId(), ".", o.getSourceId(), ".", o.getTargetId())
                ))), ArrayList::new));
        if (CollectionUtil.isNotEmpty(messageSet)) {
            StringJoiner join = new StringJoiner(",");
            messageSet.forEach(join::add);
            return new ParserTaskResult().setMetadataId(metadataId).setParserLogId(parserLogId)
                    .setParserStatus(SqlParserConstants.ParseStatus.WARN).setParserMessage(StringUtil.join(
                            "以下数据未匹配到元数据", join.toString()))
                    .setMetadataRelationList(metadataRelations)
                    .setParseLogDetails(logDetails);

        }
        return new ParserTaskResult().setMetadataId(metadataId).setParserLogId(parserLogId)
                .setParserStatus(SqlParserConstants.ParseStatus.SUCCESS)
                .setMetadataRelationList(metadataRelations)
                .setParseLogDetails(logDetails);

    }

    public static ParserTaskResult matchDB2DBMetadataRelation(
            MatchRelationContext matchRelationContext
    ) {
        ParseResult parseResult = matchRelationContext.getParseResult();
        String metadataId = matchRelationContext.getTransformId();
        String classifierId = matchRelationContext.getTransformClassifierId();
        Integer parserLogId = matchRelationContext.getParserLogId();
        String namespace = matchRelationContext.getNamespace();
        StringBuffer logDetails = matchRelationContext.getParserLogDetails();
        Map<String, ParserColumnInfo> sourceColumnMap = matchRelationContext.getSourceColumnMap();
        Map<String, ParserColumnInfo> targetColumnMap = matchRelationContext.getTargetColumnMap();
        String sourceDefaultSchema = matchRelationContext.getSourceDefaultSchema();
        String targetDefaultSchema = matchRelationContext.getTargetDefaultSchema();
        List<MetadataRelation> metadataRelationList = new ArrayList<>();
        Set<String> messageSet = new LinkedHashSet<>();
        Set<String> sourceTableTargetTableSet = new HashSet<>();

        if (!parseResult.isSuccess()) {
            LogUtil.error(logDetails, "SQL解析失败 {} ", parseResult.getErrorMessage());
            return new ParserTaskResult().setParserStatus(SqlParserConstants.ParseStatus.FAIL)
                    .setParserMessage(parseResult.getErrorMessage())
                    .setParserLogId(parserLogId)
                    .setMetadataId(metadataId)
                    .setParseLogDetails(logDetails);
        }

        List<CustomizeRelation> customizeRelations = parseResult.getCustomizeRelations();
        customizeRelations.forEach(relation -> {
            MetadataRelation metadataRelation = new MetadataRelation();

            //匹配表级血缘
            metadataRelation.setTransformId(metadataId);
            metadataRelation.setTransformClassifierId(classifierId);
            metadataRelation.setTransformNamespace(namespace);
            String sourceTablePath = relation.getSource().getTablePath(sourceDefaultSchema);
            String targetTablePath = "";
            if (relation.getTarget().getSchemaCode().toUpperCase().equals(sourceDefaultSchema.toUpperCase())) {
                relation.getTarget().setSchemaCode(targetDefaultSchema);
            }
            targetTablePath = relation.getTarget().getTablePath(targetDefaultSchema);

            //防止出现多条无意义的打印日志: 表级血缘匹配日志
            boolean logStatus = sourceTableTargetTableSet.contains(StringUtil.join(sourceTablePath, "_", targetTablePath));
            if (!logStatus) {
                LogUtil.log(logDetails, "开始匹配表级血缘 源端：{} 目标端 {} ", sourceTablePath, targetTablePath);
            }

            if (sourceColumnMap.containsKey(sourceTablePath)) {
                ParserColumnInfo parserColumnInfo = sourceColumnMap.get(sourceTablePath);
                metadataRelation.setSourceId(parserColumnInfo.getTableId());
                metadataRelation.setSourceClassifierId(parserColumnInfo.getTableClassifierId());
            } else {
                if (!logStatus) {
                    messageSet.add(sourceTablePath);
                    LogUtil.warn(logDetails, "未找到源端对应的表或视图 {} ", sourceTablePath);
                }
            }

            if (targetColumnMap.containsKey(targetTablePath)) {
                ParserColumnInfo parserColumnInfo = targetColumnMap.get(targetTablePath);
                metadataRelation.setTargetId(parserColumnInfo.getTableId());
                metadataRelation.setTargetClassifierId(parserColumnInfo.getTableClassifierId());
            } else {
                if (!logStatus) {
                    messageSet.add(targetTablePath);
                    LogUtil.warn(logDetails, "未找到目标端对应的表或视图 {} ", targetTablePath);
                }
            }

            sourceTableTargetTableSet.add(StringUtil.join(sourceTablePath, "_", targetTablePath));
            if (StringUtil.isNotBlank(metadataRelation.getSourceId()) && StringUtil.isNotBlank(
                    metadataRelation.getTargetId())) {
                metadataRelationList.add(metadataRelation);
                if (StringUtil.isBlank(relation.getTarget().getColumnCode()) ||
                        StringUtil.isBlank(relation.getSource().getColumnCode())) {
                    return;
                }
                MetadataRelation columnRelation = new MetadataRelation();
                columnRelation.setTransformId(metadataId);
                columnRelation.setTransformClassifierId(classifierId);
                columnRelation.setTransformNamespace(namespace);

                String sourceColumnPath = relation.getSource().getColumnPath(sourceDefaultSchema);
                String targetColumnPath = relation.getTarget().getColumnPath(targetDefaultSchema);
                LogUtil.log(logDetails, "开始匹配字段级血缘 源端：{} 目标端 {} ", sourceColumnPath, targetColumnPath);

                if (sourceColumnMap.containsKey(sourceColumnPath)) {
                    ParserColumnInfo parserColumnInfo = sourceColumnMap.get(sourceColumnPath);
                    columnRelation.setSourceId(parserColumnInfo.getColumnId());
                    columnRelation.setSourceClassifierId(parserColumnInfo.getColumnClassifierId());
                } else {
                    messageSet.add(sourceColumnPath);
                    LogUtil.warn(logDetails, "未找到源端对应的字段 {} ", sourceColumnPath);
                }

                if (targetColumnMap.containsKey(targetColumnPath)) {
                    ParserColumnInfo parserColumnInfo = targetColumnMap.get(targetColumnPath);
                    columnRelation.setTargetId(parserColumnInfo.getColumnId());
                    columnRelation.setTargetClassifierId(parserColumnInfo.getColumnClassifierId());
                } else {
                    messageSet.add(targetColumnPath);
                    LogUtil.warn(logDetails, "未找到目标端对应的字段 {} ", targetColumnPath);
                }
                if (columnRelation.getSourceId() != null && columnRelation.getTargetId() != null) {
                    metadataRelationList.add(columnRelation);
                }
            }

        });

        List<MetadataRelation> metadataRelations = metadataRelationList.stream().filter(d -> !d.getSourceId()
                .equals(d.getTargetId())).distinct().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(
                        o -> StringUtil.join(o.getTransformId(), ".", o.getSourceId(), ".", o.getTargetId())
                ))), ArrayList::new));
        if (CollectionUtil.isNotEmpty(messageSet)) {
            StringJoiner join = new StringJoiner(",");
            messageSet.forEach(join::add);
            return new ParserTaskResult().setMetadataId(metadataId).setParserLogId(parserLogId)
                    .setParserStatus(SqlParserConstants.ParseStatus.WARN).setParserMessage(StringUtil.join(
                            "以下数据未匹配到元数据", join.toString()))
                    .setMetadataRelationList(metadataRelations)
                    .setParseLogDetails(logDetails);
        }
        return new ParserTaskResult().setMetadataId(metadataId).setParserLogId(parserLogId)
                .setParserStatus(SqlParserConstants.ParseStatus.SUCCESS)
                .setMetadataRelationList(metadataRelations)
                .setParseLogDetails(logDetails);
    }




}
