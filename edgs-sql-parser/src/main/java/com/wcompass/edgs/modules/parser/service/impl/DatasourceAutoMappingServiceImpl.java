package com.wcompass.edgs.modules.parser.service.impl;

import com.wcompass.edgs.modules.parser.dao.read.DataflowDatasourceMappingReadMapper;
import com.wcompass.edgs.modules.parser.dao.read.mapping.DatasourceSyncConnectionReadMapper;
import com.wcompass.edgs.modules.parser.dao.write.DataflowDatasourceMappingWriteMapper;
import com.wcompass.edgs.modules.parser.model.mapping.DataflowDatasourceMapping;
import com.wcompass.edgs.modules.parser.model.mapping.DataflowSyncConnectionVO;
import com.wcompass.edgs.modules.parser.service.DatasourceAutoMappingService;
import com.wcompass.edgs.utils.CollectionUtil;
import com.wcompass.edgs.utils.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * @Description:
 * @Author: wenshijin
 * @Date: 2025年06月02日23:38
 */
@Slf4j
@Service
public class DatasourceAutoMappingServiceImpl implements DatasourceAutoMappingService {

    @Resource
    private DatasourceSyncConnectionReadMapper datasourceSyncConnectionReadMapper;

    @Resource
    private DataflowDatasourceMappingReadMapper dataflowDatasourceMappingReadMapper;

    @Resource
    private DataflowDatasourceMappingWriteMapper dataflowDatasourceMappingWriteMapper;

    @Override
    public void autoMapping() {
        log.info("开始执行自动映射");
        List<DataflowSyncConnectionVO> connections = datasourceSyncConnectionReadMapper.queryAllDataflowSyncConnectionVO();
        log.info("获取到 {} 个需要映射的连接", connections.size());

        int processedCount = 0;
        int skippedCount = 0;
        int mappedCount = 0;

        for (DataflowSyncConnectionVO connection : connections) {
            processedCount++;
            log.debug("处理连接 [{}/{}]: {}", processedCount, connections.size(), connection.getId());

            // 检查是否已存在映射
            List<DataflowDatasourceMapping> existingMappings =
                    dataflowDatasourceMappingReadMapper.queryDataflowDatasourceMappingVO(connection.getId());

            if (CollectionUtil.isNotEmpty(existingMappings)) {
                log.debug("连接 [{}] 已存在映射, 跳过", connection.getId());
                skippedCount++;
                continue;
            }

            try {
                DataflowDatasourceMapping mapping = new DataflowDatasourceMapping();
                mapping.setSyncConnectionId(connection.getId());
                mapping.setId(IdUtil.getSnowflakeId());

                //TODO 自动匹配下个版本在考虑
                dataflowDatasourceMappingWriteMapper.insertDataflowDatasourceMapping(mapping);
                log.info("成功为连接 [{}] 创建映射 [{}]", connection.getId(), mapping.getId());
                mappedCount++;
            } catch (Exception e) {
                log.error("为连接 [{}] 创建映射失败: {}", connection.getId(), e.getMessage(), e);
            }
        }

        log.info("自动映射完成. 总连接: {}, 已映射: {}, 已跳过: {}",
                connections.size(), mappedCount, skippedCount);
    }

}
