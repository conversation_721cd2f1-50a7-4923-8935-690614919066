package com.wcompass.edgs.modules.parser.controller;

import com.wcompass.edgs.cloud.api.client.model.ProgressDTO;
import com.wcompass.edgs.core.AjaxResponseWrapper;
import com.wcompass.edgs.core.BaseController;
import com.wcompass.edgs.core.Page;
import com.wcompass.edgs.modules.core.constant.SqlParserConstants;
import com.wcompass.edgs.modules.parser.model.base.*;
import com.wcompass.edgs.modules.parser.model.dataflow.CtzDataflowMapVO;
import com.wcompass.edgs.modules.parser.model.log.DataflowParseLogVO;
import com.wcompass.edgs.modules.parser.model.search.MetadataParserTreeNodeVO;
import com.wcompass.edgs.modules.parser.service.*;
import com.wcompass.edgs.utils.ObjectUtil;
import com.wcompass.edgs.utils.StringUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.hibernate.validator.constraints.Range;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: wenshijin
 * @Date: 2025年06月04日14:31
 */
@RestController
@RequestMapping("/db")
@Validated
@Tag(name = "数据库解析")
public class DatabaseParserController extends BaseController {

    @Resource
    private DatabaseParserService databaseParserService;

    @Resource
    private BaseParserService baseParserService;

    @Resource
    private SearchMetadataService searchMetadataService;

    @Resource
    private DataflowParseLogService dataflowParseLogService;

    @Resource
    private DataflowParseTmpService dataflowParseTmpService;

    @Resource
    private BlackListService blackListService;


    @GetMapping("/listSystem")
    @Operation(summary = "系统列表")
    public AjaxResponseWrapper<List<MetadataParserVO>> listSystem() {
        return AjaxResponseWrapper.data(baseParserService.queryAllSystem(databaseParserService.getAdapterId(), databaseParserService.getParserClassifierId()));
    }

    @GetMapping("/listDatasourceOfSystem")
    @Operation(summary = "数据源列表")
    public AjaxResponseWrapper<List<TemporaryObject>> listDatasourceOfSystem(
            @NotBlank(message = "请选择系统！") @Parameter(description = "系统的id") String systemId
    ) {
        return AjaxResponseWrapper.data(baseParserService.listDatasourceOfSystem(systemId, databaseParserService.getAdapterId()));
    }

    @GetMapping("/tree")
    @Operation(summary = "元数据仓库-左侧树")
    public AjaxResponseWrapper<List<MetadataParserTreeNodeVO>> tree(
            @Parameter(description = "叶子节点显示的页数") @RequestParam(required = false, defaultValue = "1") Integer current,
            @Parameter(description = "元数据类型") @RequestParam(required = false) String classifierId,
            @Parameter(description = "节点类型") @RequestParam(required = false, defaultValue = "") String nodeType,
            @NotNull(message = "无效的元数据") @Range(min = 1, message = "无效的元数据")
            @RequestParam(name = "parentId") String parentId,
            @NotNull(message = "无效的元数据") @Range(min = 1, message = "无效的元数据")
            @RequestParam(name = "systemId") String systemId,
            @Parameter(description = "数据源id") @RequestParam(required = false) String datasourceId,
            @Parameter(description = "关键字") @RequestParam(required = false) String keyword) {
        AjaxResponseWrapper<List<MetadataParserTreeNodeVO>> result = AjaxResponseWrapper.data(
                searchMetadataService.tree(current, parentId, systemId,
                        datasourceId,
                        classifierId,
                        nodeType,
                        keyword,
                        databaseParserService.getAdapterId(),
                        databaseParserService.getParserClassifierId())
        );
        return result;
    }

    @PostMapping("/parserMetadata")
    @Operation(summary = "解析元数据")
    public AjaxResponseWrapper<Void> parserMetadata(@NotEmpty(message = "至少要选中一个节点解析血缘")
                                                    @Validated @RequestBody List<ParseMetadataVO> parseMetadataVOList
    ) {
        String sessionId = baseParserService.addParserMetadata(parseMetadataVOList, getCurrentUserId(), databaseParserService.getParserClassifierId(), false);
        if (ObjectUtil.isNotEmpty(sessionId)) {
            return AjaxResponseWrapper.tip("解析任务启动成功，请稍后查看血缘结果！");
        } else {
            return AjaxResponseWrapper.fail("解析执行成功: 未获取到需要解析的对象");
        }
    }


    @GetMapping("/getParseSessionIds")
    @Operation(summary = "获取解析日志的批次id")
    public AjaxResponseWrapper<List<String>> getParseSessionIds() {
        return AjaxResponseWrapper.data(dataflowParseLogService.getSessionIdList(databaseParserService.getParserClassifierId()));
    }


    @GetMapping("/getParseSystems")
    @Operation(summary = "获取解析系统")
    public AjaxResponseWrapper<List<TemporaryObject>> getParseSystem() {
        List<MetadataParserVO> metadataParserVOS = baseParserService.queryAllSystem(databaseParserService.getAdapterId(), databaseParserService.getParserClassifierId());
        return AjaxResponseWrapper.data(metadataParserVOS.stream().map(m -> new TemporaryObject(m.getInstanceName(), m.getInstanceId()))
                .collect(Collectors.toList()));
    }

    @GetMapping("/getParseDatasource")
    @Operation(summary = "获取解析数据库")
    public AjaxResponseWrapper<List<TemporaryObject>> getParseDatasource(
            @NotBlank(message = "请选择系统！") @Parameter(description = "系统的id") String systemId) {
        return AjaxResponseWrapper.data(baseParserService.listDatasourceOfSystem(systemId, databaseParserService.getAdapterId()));
    }

    @GetMapping("/getParseSchema")
    @Operation(summary = "获取解析schema")
    public AjaxResponseWrapper<List<TemporaryObject>> getParseSchema(
            @NotBlank(message = "请选择系统！") @Parameter(description = "系统的id") String systemId,
            @NotBlank(message = "请选择数据源！") @Parameter(description = "数据源的id") String datasourceId
    ) {
        return AjaxResponseWrapper.data(baseParserService.listSchemaOfDatasource(systemId, datasourceId, databaseParserService.getAdapterId()));
    }


    @GetMapping("getParseClassifier")
    @Operation(summary = "获取解析类型")
    public AjaxResponseWrapper<List<TemporaryObject>> getParseClassifier() {
        return AjaxResponseWrapper.data(baseParserService.listClassifier(databaseParserService.getParserClassifierId()));
    }

    @GetMapping("/queryParseLogByPage")
    public AjaxResponseWrapper<Page<DataflowParseLogVO>> queryParseLogByPage(
            @Parameter(name = "systemId", description = "系统Id")
            @RequestParam(name = "systemId", required = false) String systemId,
            @Parameter(name = "datasourceId", description = "数据源Id")
            @RequestParam(name = "datasourceId", required = false) String datasourceId,
            @Parameter(name = "schemaId", description = "schemaId")
            @RequestParam(name = "schemaId", required = false) String schemaId,
            @Parameter(name = "classifierId", description = "元数据类型")
            @RequestParam(name = "classifierId", required = false) String classifierId,
            @Parameter(name = "keyword", description = "搜索关键字,元数据名称/代码")
            @RequestParam(name = "keyword", required = false) String keyword,
            @Parameter(name = "sessionId", description = "批次id")
            @RequestParam(name = "sessionId", required = false) String sessionId,
            @Parameter(name = "recentBatch", description = "是否最新批次 Y 是 N不是")
            @RequestParam(name = "recentBatch", required = false) String recentBatch,
            @Parameter(name = "parseStatus", description = "解析状态： 0 正在解析，1解析成功，2警告，3 失败")
            @RequestParam(name = "parseStatus", required = false) String parseStatus,
            @Parameter(name = "metadataId", description = "元数据id")
            @RequestParam(name = "metadataId", required = false) String metadataId,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer current,
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {

        if (StringUtil.isNotBlank(recentBatch) && SqlParserConstants.RecentBatch.NON.equals(recentBatch)) {
            recentBatch = "";
        }

        Set<String> classifierIds = new HashSet<>();
        if (StringUtil.isNotBlank(classifierId)) {
            classifierIds.add(classifierId);
        } else {
            classifierIds = databaseParserService.getParserClassifierId();
        }

        return AjaxResponseWrapper.data(dataflowParseLogService.queryDataflowParseLogVOByPage(Page.of(size, current),
                sessionId, systemId, datasourceId, schemaId
                , classifierIds, keyword, recentBatch, parseStatus, metadataId));
    }


    @GetMapping("/queryParseTmpByPage")
    public AjaxResponseWrapper<Page<DataflowParseTmpVO>> queryDataflowParseTmpByPage(
            @NotNull(message = "无效的元数据") @Parameter(name = "metadataId", description = "元数据id")
            @RequestParam(name = "metadataId", required = false) String metadataId,
            @NotNull(message = "无效的解析类型") @Parameter(name = "parseLevel", description = "解析类型 0 table,1 column")
            @RequestParam(name = "parseLevel", required = false) String parseLevel,
            @Parameter(name = "changeStatus", description = "变化状态： 0 没变化，1新增，2删除")
            @RequestParam(name = "changeStatus", required = false) String changeStatus,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer current,
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size
    ) {
        return AjaxResponseWrapper.data(dataflowParseTmpService.queryDataflowParseTmpByPage(Page.of(size, current), parseLevel, changeStatus, metadataId,
                databaseParserService.getParserClassifierId()));
    }

    @PostMapping("/dataflowExport")
    @Operation(summary = "解析的血缘导出")
    public AjaxResponseWrapper<Void> dataflowExport(
            @NotNull(message = "无效的元数据") @Parameter(name = "metadataId", description = "元数据id")
            @RequestParam(name = "metadataId", required = false) String metadataId
    ) {
        ProgressDTO progressDTO = dataflowParseTmpService.dataflowExport(metadataId, this.getCurrentUserId(), databaseParserService.getParserClassifierId());
        return AjaxResponseWrapper.tip("开始导出").addExtras("progressId", progressDTO.getProgressId());
    }

    @PostMapping("/addStorage")
    @Operation(summary = "一键导入")
    public AjaxResponseWrapper<Void> addStorage(
            @NotNull(message = "无效的元数据") @Parameter(name = "metadataId", description = "元数据id")
            @RequestParam(name = "metadataId", required = false) String metadataId) {
        dataflowParseTmpService.addStorage(metadataId, databaseParserService.getParserClassifierId());
        return AjaxResponseWrapper.tip("操作成功");
    }

    @GetMapping("/getParseTmpChangeStatus")
    @Operation(summary = "是否有变化")
    public AjaxResponseWrapper<Boolean> getParseTmpChangeStatus(
            @NotNull(message = "无效的元数据") @Parameter(name = "metadataId", description = "元数据id")
            @RequestParam(name = "metadataId", required = false) String metadataId
    ) {
        return AjaxResponseWrapper.data(dataflowParseTmpService.getParseTmpChangeStatus(metadataId, databaseParserService.getParserClassifierId()));
    }

    @PostMapping("/addBlackList")
    @Operation(summary = "添加解析对象到黑名单")
    public AjaxResponseWrapper<Void> addBlackList(
            @NotEmpty(message = "至少要选中一个节点解析血缘") @RequestParam List<String> metadataIds
    ) {
        blackListService.addBlackList(metadataIds, getCurrentUserId());
        return AjaxResponseWrapper.tip("添加成功");
    }

    @PostMapping("/removeBlackList")
    @Operation(summary = "从黑名单移除解析对象")
    public AjaxResponseWrapper<Void> removeBlackList(
            @NotEmpty(message = "至少要选中一个节点解析血缘") @RequestParam List<String> metadataIds
    ) {
        blackListService.removeBlackList(metadataIds);
        return AjaxResponseWrapper.tip("移除成功");
    }

    @GetMapping("/queryBlackListVOByPage")
    @Operation(summary = "黑名单列表")
    public AjaxResponseWrapper<Page<BlackListObjectVO>> queryBlackListVOByPage(
            @Parameter(name = "systemId", description = "系统Id")
            @RequestParam(name = "systemId", required = false) String systemId,
            @Parameter(name = "datasourceId", description = "数据源Id")
            @RequestParam(name = "datasourceId", required = false) String datasourceId,
            @Parameter(name = "schemaId", description = "schemaId")
            @RequestParam(name = "schemaId", required = false) String schemaId,
            @Parameter(name = "classifierId", description = "元数据类型")
            @RequestParam(name = "classifierId", required = false) String classifierId,
            @Parameter(name = "keyword", description = "搜索关键字,元数据名称/代码")
            @RequestParam(name = "keyword", required = false) String keyword,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer current,
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        Set<String> classifierIds = new HashSet<>();
        if (StringUtil.isNotBlank(classifierId)) {
            classifierIds.add(classifierId);
        } else {
            classifierIds = databaseParserService.getParserClassifierId();
        }
        return AjaxResponseWrapper.data(blackListService.queryBlackListVOByPage(Page.of(size, current)
                , systemId, datasourceId, schemaId, classifierIds, keyword));
    }

    @GetMapping("/viewDataflowParseTmp")
    @Operation(summary = "查看解析血缘图展示")
    public AjaxResponseWrapper<CtzDataflowMapVO> viewDataflowParseTmp(
            @NotNull(message = "无效的元数据") @Parameter(description = "元数据id")
            @RequestParam(name = "metadataId", required = false) String metadataId,
            @NotNull(message = "无效的解析类型") @Parameter(name = "parseLevel", description = "解析类型 0 table,1 column")
            @RequestParam(name = "parseLevel", required = false) String parseLevel
    ) {
        return AjaxResponseWrapper.data(dataflowParseTmpService.getCtzDataflowMap(metadataId, parseLevel, databaseParserService.getParserClassifierId()));
    }


    @GetMapping("/hasParserTmpMap")
    @Operation(summary = "判断是否展示血缘解析图")
    public AjaxResponseWrapper<Boolean> hasParserTmpMap(
            @NotNull(message = "无效的元数据") @RequestParam(name = "metadataId") String metadataId) {
        return AjaxResponseWrapper.data(dataflowParseTmpService.hasParserTmpMap(metadataId, databaseParserService.getParserClassifierId()));
    }

    @PostMapping("/executeParserMetadata")
    @Operation(summary = "批量解析元数据")
    public AjaxResponseWrapper<Void> executeParserMetadata(@NotEmpty(message = "至少要选中一个节点解析血缘")
                                                           @Validated @RequestBody List<String> parserIds
    ) {
        String sessionId = baseParserService.executeParserMetadata(parserIds, getCurrentUserId());
        if (ObjectUtil.isNotEmpty(sessionId)) {
            return AjaxResponseWrapper.tip("解析任务启动成功，请稍后查看血缘结果！");
        } else {
            return AjaxResponseWrapper.fail("解析执行成功: 未获取到需要解析的对象");
        }

    }

    @GetMapping("/queryParserType")
    @Operation(summary ="获取解析类型，数据源映射类型")
    public AjaxResponseWrapper<List<String>> queryParserType() {
        return AjaxResponseWrapper.data(baseParserService.queryAdapterNames(databaseParserService.getAdapterId()));
    }
}
