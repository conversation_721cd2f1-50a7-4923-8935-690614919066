package com.wcompass.edgs.modules.core.handler.parser.bi;

import com.wcompass.api.SqlApplication;
import com.wcompass.edgs.core.extract.MetaModel;
import com.wcompass.edgs.modules.core.annotations.ParserTaskType;
import com.wcompass.edgs.modules.core.constant.SqlParserConstants;
import com.wcompass.edgs.modules.core.handler.parser.AbstractParserTask;
import com.wcompass.edgs.modules.core.util.AssertUtil;
import com.wcompass.edgs.modules.core.util.LogUtil;
import com.wcompass.edgs.modules.core.util.SqlAnalyzeUtil;
import com.wcompass.edgs.modules.core.util.SqlParserUtil;
import com.wcompass.edgs.modules.parser.dao.read.parser.FineBIParserMetadataReadMapper;
import com.wcompass.edgs.modules.parser.model.base.ParserColumnInfo;
import com.wcompass.edgs.modules.parser.model.base.ParserMetadata;
import com.wcompass.edgs.modules.parser.model.bi.finebi.*;
import com.wcompass.edgs.modules.parser.model.bi.fr.FRFunctionParser;
import com.wcompass.edgs.modules.parser.model.bi.fr.FRInputVO;
import com.wcompass.edgs.modules.parser.model.mapping.DatasourceMappingVO;
import com.wcompass.edgs.modules.parser.model.parser.MetadataRelation;
import com.wcompass.edgs.modules.parser.model.parser.ParseContext;
import com.wcompass.edgs.modules.parser.model.parser.ParserTaskResult;
import com.wcompass.edgs.modules.parser.model.parser.SelectParserResult;
import com.wcompass.edgs.utils.CollectionUtil;
import com.wcompass.edgs.utils.ObjectUtil;
import com.wcompass.edgs.utils.StringUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.wcompass.edgs.modules.core.constant.SqlParserConstants.VIEW_TMP;

/**
 * @Description:
 * @Author: wenshijin
 * @Date: 2025年09月18日17:26
 */
@ParserTaskType(MetaModel.FINE_BI_DATASET)
@Slf4j
public class FineBIDataSetParserTask extends AbstractParserTask {

    @Resource
    private FineBIParserMetadataReadMapper fineBIParserMetadataReadMapper;

    @Override
    public ParserMetadata getParseMetadata(String metadataId) {
        return buildParserMetadata(fineBIParserMetadataReadMapper.getMetadataParserFineBIDataSet(metadataId));
    }

    @Override
    public ParserTaskResult parser(ParserMetadata parserMetadata, ParseContext parseContext) {
        StringBuffer parseLogDetails = LogUtil.createParseLogDetails(parserMetadata.getClassifierId(), parserMetadata.getInstanceCode());
        try {
            // 判断数据集是否 引用了其他的数据集
            FineDataSet fineDataSet = fineBIParserMetadataReadMapper.getFineDataSet(parserMetadata.getInstanceId());

            List<FineField> fineFieldList = fineBIParserMetadataReadMapper.getFineFieldVOList(fineDataSet.getInstanceId());
            Map<String, FineField> fineFieldMap = fineFieldList.stream()
                    .collect(Collectors.toMap(f -> f.getFineFieldCode().toUpperCase(), Function.identity()));

            List<MetadataRelation> metadataRelationList = new ArrayList<>();
            String parserStatus = SqlParserConstants.ParseStatus.SUCCESS;
            String parserMessage = "";
            if (ObjectUtil.isNull(fineDataSet.getBaseDatasetId())) {
                // 构建源表 =》 公共数据集
                LogUtil.log(parseLogDetails, "当前数据集归属公共数据集");
                List<DatasourceMappingVO> dmVOS = SqlParserUtil.getDatasourceMappingVO(parseContext.getDatasourceMappingVOs(), Arrays.asList(fineDataSet.getConnectionName()));


                AssertUtil.collectionIsNull(dmVOS, "未配置FineBI类型数据源{}->的映射关系"
                        , parserMetadata.getDatasourceName());

                DatasourceMappingVO mapping = dmVOS.get(0);
                AssertUtil.strIsNull(mapping.getDefaultSchema(),
                        "未配置FineBI类型数据源{}->的默认模式",
                        parserMetadata.getDatasourceName()
                );

                LogUtil.log(parseLogDetails, "映射数据源类型 {},默认schema {}",
                        mapping.getLocalDatasourceType(),
                        mapping.getDefaultSchema()
                );
                Set<String> datasourceIds = dmVOS.stream().map(DatasourceMappingVO::getLocalDatasourceId)
                        .collect(Collectors.toSet());
                Map<String, ParserColumnInfo> parserColumnInfoMap = getParserColumnInfoMap(datasourceIds);

                String parserSql = replaceParseSql(fineDataSet.getParserSql(), parserMetadata.getNamespace(), parserMetadata.getDatasourceType(), parseLogDetails);
                parserSql = replaceFRSql(parserSql, new ArrayList<>());
                LogUtil.logSql(parseLogDetails, parserSql);


                SelectParserResult selectParserResult = SqlAnalyzeUtil.parserSelectSql(parserSql, mapping.getLocalDatasourceType(),
                        mapping.getDefaultSchema(), parserColumnInfoMap, fineFieldMap.keySet(), parseLogDetails);

                Map<String, Set<ParserColumnInfo>> relationMapping = selectParserResult.getRelationMapping();
                if (CollectionUtil.isNotEmpty(relationMapping)) {
                    Set<ParserColumnInfo> sourceTables = relationMapping.getOrDefault(VIEW_TMP, new HashSet<>());
                    //表级血缘生成
                    for (ParserColumnInfo sourceTable : sourceTables) {
                        metadataRelationList.add(new MetadataRelation(sourceTable.getTableId(), sourceTable.getTableClassifierId(),
                                fineDataSet.getInstanceId(), MetaModel.FINE_BI_DATASET, parserMetadata.getInstanceId(),
                                parserMetadata.getClassifierId(),
                                parserMetadata.getNamespace()));
                    }
                    relationMapping.remove(VIEW_TMP);

                    for (String key : relationMapping.keySet()) {
                        Set<ParserColumnInfo> parserColumnInfos = relationMapping.getOrDefault(key, new HashSet<>());
                        for (ParserColumnInfo sourceColumn : parserColumnInfos) {
                            MetadataRelation metadataRelation = new MetadataRelation(sourceColumn.getColumnId(), sourceColumn.getColumnClassifierId(),
                                    fineFieldMap.get(key).getFineFieldId(), MetaModel.FINE_BI_FIELD, parserMetadata.getInstanceId(),
                                    parserMetadata.getClassifierId(),
                                    parserMetadata.getNamespace());
                            metadataRelationList.add(metadataRelation);
                        }
                    }

                }

                parserStatus = selectParserResult.getParseStatus();
                parserMessage = selectParserResult.getParseMessage();

            } else {
                LogUtil.log(parseLogDetails, "当前数据集归属应用数据集");
                //先构建非解析的血缘  应用数据集 =》 报表的 关系
                FineSubject fineSubject = fineBIParserMetadataReadMapper.getFineSubjectId(parserMetadata.getInstanceId());

                LogUtil.log(parseLogDetails,"当前分析主题: {}",fineSubject.getFineSubjectCode());

                List<FineReport> fineReports = fineBIParserMetadataReadMapper.getFineReport(fineSubject.getNamespace());

                for (FineReport fineReport : fineReports) {
                    List<FineReportItem> fineReportItemVOList = fineBIParserMetadataReadMapper.getFineReportItemVOList(fineReport.getNamespace(), fineDataSet.getId());
                    LogUtil.log(parseLogDetails, "开始构建应用数据集 和 报表 {}之间的关系",fineReport.getFineReportCode());
                    metadataRelationList.add(new MetadataRelation(fineDataSet.getInstanceId(), MetaModel.FINE_BI_DATASET,
                            fineReport.getFineReportId(), MetaModel.FINE_BI_DASHBOARD, parserMetadata.getInstanceId(),
                            parserMetadata.getClassifierId(),
                            parserMetadata.getNamespace()));

                    for (FineReportItem fineReportItem : fineReportItemVOList) {
                        LogUtil.log(parseLogDetails, "开始构建报表项: {}", fineReportItem.getReportItemCode());
                        String sourceField = fineReportItem.getSourceField();
                        FineField fineField = fineFieldMap.get(sourceField.toUpperCase());

                        if (fineField == null) {
                            LogUtil.warn(parseLogDetails, "报表项对应的字段未识别:", sourceField);
                            parserStatus = SqlParserConstants.ParseStatus.WARN;
                        } else {
                            MetadataRelation metadataRelation = new MetadataRelation(fineField.getFineFieldId(), MetaModel.FINE_BI_FIELD,
                                    fineReportItem.getReportItemId(), MetaModel.FINE_BI_DASHBOARD_ITEM, parserMetadata.getInstanceId(),
                                    parserMetadata.getClassifierId(),
                                    parserMetadata.getNamespace());
                            metadataRelationList.add(metadataRelation);
                        }

                    }

                }


                LogUtil.log(parseLogDetails, "开始构建公共数据集 和 应用数据集之间的关系");
                String datasourceNamespace = getDatasourceNamespace(parserMetadata.getDatasourceId());
                FineDataSet sourceDataSet = fineBIParserMetadataReadMapper.getFineDataSetByFineDataSetId(fineDataSet.getBaseDatasetId(), datasourceNamespace);

                if (ObjectUtil.isNull(sourceDataSet)) {
                    LogUtil.warn(parseLogDetails, "未找到应用数据集引用的公共数据集: {}", fineDataSet.getBaseDatasetId());
                } else {
                    //构建公共数据集和应用数据集的血缘
                    metadataRelationList.add(new MetadataRelation(sourceDataSet.getInstanceId(), MetaModel.FINE_BI_DATASET,
                            fineDataSet.getInstanceId(), MetaModel.FINE_BI_DATASET, parserMetadata.getInstanceId(),
                            parserMetadata.getClassifierId(),
                            parserMetadata.getNamespace()));
                    List<FineField> sourceFineFieldList = fineBIParserMetadataReadMapper.getFineFieldVOList(sourceDataSet.getInstanceId());

                    Map<String, FineField> sourceFineFieldMap = sourceFineFieldList.stream().collect(Collectors.toMap(f -> f.getFineFieldCode().toUpperCase(), Function.identity()));
                    Map<String,List<FineField>> mappingMap = new HashMap<>();
                    for (FineField fineField : fineFieldList) {
                        LogUtil.log(parseLogDetails, "开始构建应用数据集字段: {}", fineField.getFineFieldCode());
                        String sourceField = fineField.getSourceField();
                        List<FineField> mappingFineFieldList = new ArrayList<>();
                        if (StringUtil.isNotBlank(sourceField)) {
                            for (String string : fineField.getSourceField().split(",")) {
                                if (StringUtil.isBlank(string)) {
                                    continue;
                                }
                                String upperCase = string.toUpperCase();
                                FineField sourceFieldSplit = sourceFineFieldMap.get(upperCase);
                                if (sourceFieldSplit == null) {
                                    if (mappingMap.containsKey(upperCase)) {
                                        for (FineField field : mappingFineFieldList) {
                                            MetadataRelation metadataRelation = new MetadataRelation(field.getFineFieldId(), MetaModel.FINE_BI_FIELD,
                                                    fineField.getFineFieldId(), MetaModel.FINE_BI_FIELD, parserMetadata.getInstanceId(),
                                                    parserMetadata.getClassifierId(),
                                                    parserMetadata.getNamespace());
                                            metadataRelationList.add(metadataRelation);
                                        }
                                    } else {
                                        LogUtil.warn(parseLogDetails, "应用数据集字段未识别到: {}", upperCase);
                                        parserStatus = SqlParserConstants.ParseStatus.WARN;
                                    }
                                 } else {
                                    mappingFineFieldList.add(sourceFieldSplit);
                                    MetadataRelation metadataRelation = new MetadataRelation(sourceFieldSplit.getFineFieldId(), MetaModel.FINE_BI_FIELD,
                                            fineField.getFineFieldId(), MetaModel.FINE_BI_FIELD, parserMetadata.getInstanceId(),
                                            parserMetadata.getClassifierId(),
                                            parserMetadata.getNamespace());
                                    metadataRelationList.add(metadataRelation);
                                }
                            }
                            if (CollectionUtil.isNotEmpty(mappingFineFieldList)) {
                                mappingMap.put(fineField.getFineFieldCode().toUpperCase(), mappingFineFieldList);
                            }

                        }
                    }
                }


            }
            return new ParserTaskResult().setParserStatus(parserStatus)
                    .setParserMessage(parserMessage)
                    .setParserLogId(parserMetadata.getParseLogId())
                    .setMetadataId(parserMetadata.getInstanceId())
                    .setMetadataRelationList(metadataRelationList.stream().distinct().collect(Collectors.toList()))
                    .setParseLogDetails(parseLogDetails);
        } catch (Exception e) {
            log.error("解析失败: ", e);
            //构建失败的解析结果对象
            LogUtil.error(parseLogDetails, "解析失败: {}", e.getMessage());
            return new ParserTaskResult().setParserStatus(SqlParserConstants.ParseStatus.FAIL)
                    .setParserMessage("解析失败")
                    .setParserLogId(parserMetadata.getParseLogId())
                    .setMetadataId(parserMetadata.getInstanceId())
                    .setParseLogDetails(parseLogDetails);
        }

    }


    public String replaceFRSql(String sql, List<FRInputVO> frInputVOS) {

        Map<String, String> paramNameMap = new HashMap<>();
        for (FRInputVO frInputVO : frInputVOS) {
            if (StringUtil.isEmpty(frInputVO.getDefaultValue())) {
                paramNameMap.put(frInputVO.getFrInputCode().toLowerCase(), "");
            } else if ("/".equals(frInputVO.getDefaultValue().trim())) {
                paramNameMap.put(frInputVO.getFrInputCode().toLowerCase(), "0");
            } else {
                paramNameMap.put(frInputVO.getFrInputCode().toLowerCase(), frInputVO.getDefaultValue().trim());
            }
        }

        sql = new FRFunctionParser(sql, paramNameMap).replaceFRFunction();
        sql = SqlApplication.removeSqlComment(sql);
        sql = SqlApplication.removeOrderBy(sql);
        return sql;
    }
}
