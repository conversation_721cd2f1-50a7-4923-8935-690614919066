package com.wcompass.edgs.modules.parser.service.impl;

import com.wcompass.edgs.modules.core.constant.enums.DatasourceCategoryEnum;
import com.wcompass.edgs.modules.core.constant.enums.DatasourceTypeEnum;
import com.wcompass.edgs.modules.parser.model.base.MetadataParserVO;
import com.wcompass.edgs.modules.parser.model.base.TemporaryObject;
import com.wcompass.edgs.modules.parser.service.BIParserService;
import com.wcompass.edgs.modules.parser.service.BaseParserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: wenshijin
 * @Date: 2025年06月03日17:22
 */
@Service
@Slf4j
public class BIParserServiceImpl implements BIParserService {
    @Override
    public Set<String> getAdapterId() {
        List<DatasourceTypeEnum> datasourceTypeEnums = DatasourceTypeEnum.getByCategory(DatasourceCategoryEnum.BI);
        return datasourceTypeEnums.stream().map(DatasourceTypeEnum::getAdapterId).collect(Collectors.toSet());
    }


    @Override
    public Set<String> getParserClassifierId() {
        List<DatasourceTypeEnum> datasourceTypeEnums = DatasourceTypeEnum.getByCategory(DatasourceCategoryEnum.BI);
        return datasourceTypeEnums.stream().flatMap(t -> t.getParserClasses().stream()).collect(Collectors.toSet());
    }
}
