package com.wcompass.edgs.modules.parser.controller;

import com.wcompass.edgs.modules.parser.service.DataflowGlobalConnectionService;
import com.wcompass.edgs.modules.parser.service.DatasourceAutoMappingService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description:
 * @Author: wenshijin
 * @Date: 2025年06月04日15:51
 */
@RestController
@RequestMapping("/demo")
@Validated
@Tag(name ="demo")
public class DemoController{

    @Resource
    private DataflowGlobalConnectionService dataflowGlobalConnectionService;

    @Resource
    private DatasourceAutoMappingService datasourceAutoMappingService;

    @GetMapping(value = "/syncDataflowConnection")
    public void syncDataflowConnection() {
        dataflowGlobalConnectionService.syncDataflowConnection();
    }

    @GetMapping(value = "/autoMapping")
    public void autoMapping() {
        datasourceAutoMappingService.autoMapping();
    }


}
