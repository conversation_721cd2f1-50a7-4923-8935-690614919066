package com.wcompass.edgs.modules.core.util.parser.dts;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONObject;
import com.wcompass.api.SqlApplication;
import com.wcompass.api.enums.parse.type.DatabaseType;
import com.wcompass.api.model.CustomizeRelation;
import com.wcompass.api.model.ParseRequest;
import com.wcompass.api.model.ParseResult;
import com.wcompass.api.model.ParserColumn;
import com.wcompass.edgs.modules.core.util.SelectSqlUtil;
import com.wcompass.edgs.modules.core.util.SqlAnalyzeUtil;
import com.wcompass.edgs.modules.core.util.SqlParserUtil;
import com.wcompass.edgs.modules.parser.model.base.ParserColumnInfo;
import com.wcompass.edgs.modules.parser.model.dts.datax.DataXModel;
import com.wcompass.edgs.modules.parser.model.dts.datax.DataXParserVO;
import com.wcompass.edgs.modules.parser.model.dts.datax.DataXReadVO;
import com.wcompass.edgs.modules.parser.model.dts.datax.DataXWriteVO;
import com.wcompass.edgs.utils.NumberUtil;
import com.wcompass.edgs.utils.StringUtil;

import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cn.hutool.core.util.ReUtil.replaceAll;
import static com.wcompass.edgs.modules.core.constant.SqlParserConstants.VIEW_TMP;

/**
 * @Description:
 * @Author: wenshijin
 * @Date: 2025年06月02日17:39
 */

public class DataXUtil {


    private static final String PROP_PARAMS = "params";
    private static final String PROP_FOLDER = "folder";
    private static final String PROP_FILE = "file";

    private static final String HDFS_WAREHOUSE_PREFIX = "/user/hive/warehouse/";
    private static final String DB_SUFFIX = ".db";

    private static final Map<String, String> JDBC_PATTERNS = new HashMap<>();

    static {
        JDBC_PATTERNS.put("Mysql", "jdbc:mysql://{host}[:{port}]/[{database}][\\?{params}]");
        JDBC_PATTERNS.put("SQLServer", "jdbc:sqlserver://{host}[:{port}][;databaseName={database}][;{params}]");
        JDBC_PATTERNS.put("PostgreSQL", "jdbc:postgresql://{host}[:{port}]/[{database}][\\?{params}]");
        JDBC_PATTERNS.put("Hive", "jdbc:hive2://{host}[:{port}]/[{database}][\\?{params}]");
    }


    public static ParseResult parseDataXJobRelation(DataXParserVO dataXParserVO, StringBuffer parseLogDetails
            , Map<String, ParserColumn> readColumnMap
            , String readDatasourceType) {
        List<CustomizeRelation> customizeRelationList = new ArrayList<>();

        Boolean status = true;

        String message = "";

        // 判断DataX 的read组件是否是query 语句
        DataXReadVO read = dataXParserVO.getRead();
        DataXWriteVO write = dataXParserVO.getWrite();
        if (StringUtil.isNotBlank(read.getQuerySQL())) {
            ParseRequest parseRequest = new ParseRequest().setDbType(DatabaseType.fromCode(readDatasourceType))
                    .setSql(read.getQuerySQL()).setSelected(true).setDefaultSchema(read.getDefaultSchema()).setColumnMap(readColumnMap);
            SqlAnalyzeUtil.isMultiDatabase(readDatasourceType, parseRequest);
            ParseResult parseResult = SqlApplication.parseSql(parseRequest);
            List<String> viewColumns = parseResult.getViewTmpColumns();

            Map<String, Set<ParserColumn>> parserSelectResultMapping = SelectSqlUtil.getParserSelectResultMapping(parseResult.getCustomizeRelations());

            Set<ParserColumn> sourceTables = SelectSqlUtil.getSourceTables(parserSelectResultMapping);
            for (String table : write.getTables()) {
                for (ParserColumn sourceTable : sourceTables) {
                    CustomizeRelation customizeRelation = new CustomizeRelation();
                    customizeRelation.setSource(sourceTable);
                    customizeRelation.setTarget(ParserColumn.ofParseTable(table, write.getDefaultSchema()));
                    customizeRelationList.add(customizeRelation);
                }
            }
            parserSelectResultMapping.remove(VIEW_TMP);
            if (com.wcompass.edgs.utils.CollectionUtil.isNotEmpty(viewColumns)) {
                //如果有字段的情况，则按顺序进行匹配

                for (int i = 0; i < viewColumns.size(); i++) {
                    Set<ParserColumn> sourceColumns = parserSelectResultMapping.getOrDefault(viewColumns.get(i), new HashSet<>());
                    String targetColumnName = write.getColumns().get(i);
                    for (ParserColumn sourceColumn : sourceColumns) {
                        for (String table : write.getTables()) {
                            CustomizeRelation customizeRelation = new CustomizeRelation();
                            customizeRelation.setSource(sourceColumn);
                            customizeRelation.setTarget(ParserColumn.ofParseTable(table, write.getDefaultSchema()).setColumnCode(targetColumnName));
                            customizeRelationList.add(customizeRelation);
                        }
                    }

                }
            } else {
                for (String key : parserSelectResultMapping.keySet()) {
                    Set<ParserColumn> sourceColumns = parserSelectResultMapping.getOrDefault(key, new HashSet<>());
                    for (ParserColumn sourceColumn : sourceColumns) {
                        for (String table : write.getTables()) {
                            CustomizeRelation customizeRelation = new CustomizeRelation();
                            customizeRelation.setSource(sourceColumn);
                            customizeRelation.setTarget(ParserColumn.ofParseTable(table, write.getDefaultSchema()).setColumnCode(key));
                            customizeRelationList.add(customizeRelation);
                        }
                    }
                }
            }
            status = parseResult.isSuccess();
            message = parseResult.getErrorMessage();
        } else {
            String readTable = read.getTables().get(0);
            for (String table : write.getTables()) {
                CustomizeRelation customizeRelation = new CustomizeRelation();
                customizeRelation.setSource(ParserColumn.ofParseTableByCode(readTable, read.getDefaultSchema()));
                customizeRelation.setTarget(ParserColumn.ofParseTableByCode(table, write.getDefaultSchema()));
                customizeRelationList.add(customizeRelation);
            }

            for (int i = 0; i < read.getColumns().size(); i++) {
                String sourceColumnName = read.getColumns().get(i);
                String targetColumnName = write.getColumns().get(i);
                if (NumberUtil.isNumber(sourceColumnName)) {
                    sourceColumnName = targetColumnName;
                }

                for (String table : write.getTables()) {
                    CustomizeRelation customizeRelation = new CustomizeRelation();
                    customizeRelation.setSource(ParserColumn.ofParseTableByCode(readTable, read.getDefaultSchema()).setColumnCode(sourceColumnName));
                    customizeRelation.setTarget(ParserColumn.ofParseTableByCode(table, write.getDefaultSchema()).setColumnCode(targetColumnName));
                    customizeRelationList.add(customizeRelation);
                }

            }
        }
        return new ParseResult(status, message, customizeRelationList);
    }

    public static DataXParserVO parserDataXJob(String json) {
        JSONObject jsonData = JSONObject.parseObject(json);
        Map<String, Object> job = (Map<String, Object>) jsonData.get("job");
        Map<String, Object> content = ((List<Map<String, Object>>) job.get("content")).get(0);

        DataXReadVO read = parseReader(content);
        DataXWriteVO write = parseWriter(content);

        return new DataXParserVO(read, write);
    }

    private static DataXReadVO parseReader(Map<String, Object> content) {
        Map<String, Object> reader = (Map<String, Object>) content.get("reader");
        String readName = (String) reader.get("name");

        DataXReadVO read = parserDataXRead((Map<String, Object>) reader.get("parameter"), DataXModel.match(readName));
        read.setName(readName);

        return read;
    }

    private static DataXWriteVO parseWriter(Map<String, Object> content) {
        Map<String, Object> writer = (Map<String, Object>) content.get("writer");
        String writeName = (String) writer.get("name");

        DataXWriteVO write = parserDataXWrite((Map<String, Object>) writer.get("parameter"), DataXModel.match(writeName));
        write.setName(writeName);

        return write;
    }

    public static DataXReadVO parserDataXRead(Map<String, Object> readerParameter, DataXModel dataXModel) {
        DataXReadVO read = new DataXReadVO();

        List<Map<String, Object>> connections = (List<Map<String, Object>>) readerParameter.get("connection");

        if (CollectionUtil.isEmpty(connections)) {
            return parseHdfsOrHiveRead(readerParameter, dataXModel, read);
        }

        return parseJdbcRead(readerParameter, dataXModel, read, connections);
    }

    private static DataXReadVO parseHdfsOrHiveRead(Map<String, Object> readerParameter, DataXModel dataXModel, DataXReadVO read) {
        if (!readerParameter.containsKey("defaultFS")) {
            return read;
        }

        read.setJdbcUrl((String) readerParameter.get("defaultFS"));
        String modelName = dataXModel.getDataXModelName();

        if ("hdfsreader".equalsIgnoreCase(modelName)) {
            parseHdfsRead(readerParameter, read);
        } else if ("hivereader".equalsIgnoreCase(modelName)) {
            parseHiveRead(readerParameter, dataXModel, read);
        }

        return read;
    }

    private static void parseHdfsRead(Map<String, Object> readerParameter, DataXReadVO read) {
        String path = (String) readerParameter.get("path");

        List<Map<String, Object>> columns = (List<Map<String, Object>>) readerParameter.get("column");
        for (Map<String, Object> column : columns) {
            String name = (String) column.get("name");
            if (StringUtil.isBlank(name)) {
                read.getColumns().add(((Integer) column.get("index")).toString());
            } else {
                read.getColumns().add(name);
            }
        }

        Map<String, String> pathInfo = parserHdfsPath(path);
        read.setTables(Arrays.asList(pathInfo.getOrDefault("table", "")));
        read.setDefaultSchema(pathInfo.getOrDefault("defaultSchema", ""));
    }

    private static void parseHiveRead(Map<String, Object> readerParameter, DataXModel dataXModel, DataXReadVO read) {
        List<String> querySqlS = (List<String>) readerParameter.get("hiveSql");
        if (CollectionUtil.isNotEmpty(querySqlS)) {
            read.setQuerySQL(querySqlS.get(0));
        }

        String jdbcUrl = readerParameter.get("hive_jdbc_url").toString();
        read.setDefaultSchema(getDefaultSchema(dataXModel, readerParameter, jdbcUrl));
    }

    private static DataXReadVO parseJdbcRead(Map<String, Object> readerParameter, DataXModel dataXModel,
                                             DataXReadVO read, List<Map<String, Object>> connections) {

        Map<String, Object> connection = connections.get(0);

        // 解析查询 SQL
        List<String> querySqlS = (List<String>) connection.get("querySql");
        if (CollectionUtil.isNotEmpty(querySqlS)) {
            read.setQuerySQL(querySqlS.get(0));
        }

        // 设置 JDBC URL
        read.setJdbcUrl(((List<String>) connection.get("jdbcUrl")).get(0));

        // 解析列信息
        parseColumns(readerParameter, read);

        // 解析表信息
        parseTables(connection, read);

        // 设置默认 schema
        read.setDefaultSchema(getDefaultSchema(dataXModel, readerParameter, read.getJdbcUrl()));

        return read;
    }

    private static void parseColumns(Map<String, Object> parameter, DataXReadVO read) {
        List<Object> columns = (List<Object>) parameter.get("column");
        if (CollectionUtil.isNotEmpty(columns)) {
            for (Object column : columns) {
                read.getColumns().add((String) column);
            }
        }
    }

    private static void parseTables(Map<String, Object> connection, DataXReadVO read) {
        List<Object> tables = (List<Object>) connection.get("table");
        if (CollectionUtil.isNotEmpty(tables)) {
            for (Object table : tables) {
                read.getTables().add((String) table);
            }
        }
    }

    public static DataXWriteVO parserDataXWrite(Map<String, Object> writeParameter, DataXModel dataXModel) {
        DataXWriteVO write = new DataXWriteVO();

        List<Map<String, Object>> connections = (List<Map<String, Object>>) writeParameter.get("connection");

        if (CollectionUtil.isEmpty(connections)) {
            return parseHdfsWrite(writeParameter, write);
        }

        return parseJdbcWrite(writeParameter, dataXModel, write, connections);
    }

    private static DataXWriteVO parseHdfsWrite(Map<String, Object> writeParameter, DataXWriteVO write) {
        if (!writeParameter.containsKey("defaultFS")) {
            return write;
        }

        write.setJdbcUrl((String) writeParameter.get("defaultFS"));
        String path = (String) writeParameter.get("path");

        List<Map<String, Object>> columns = (List<Map<String, Object>>) writeParameter.get("column");
        for (Map<String, Object> column : columns) {
            String value = (String) column.get("value");
            if (StringUtil.isBlank(value)) {
                write.getColumns().add((String) column.get("name"));
            }
        }

        Map<String, String> pathInfo = parserHdfsPath(path);
        write.setTables(Arrays.asList(pathInfo.getOrDefault("table", "")));
        write.setDefaultSchema(pathInfo.getOrDefault("defaultSchema", ""));

        return write;
    }

    private static DataXWriteVO parseJdbcWrite(Map<String, Object> writeParameter, DataXModel dataXModel,
                                               DataXWriteVO write, List<Map<String, Object>> connections) {

        Map<String, Object> connection = connections.get(0);
        write.setJdbcUrl((String) connection.get("jdbcUrl"));

        // 解析列和表信息
        parseColumns(writeParameter, write);
        parseTables(connection, write);

        write.setDefaultSchema(getDefaultSchema(dataXModel, writeParameter, write.getJdbcUrl()));

        return write;
    }

    private static void parseColumns(Map<String, Object> parameter, DataXWriteVO write) {
        List<Object> columns = (List<Object>) parameter.get("column");
        if (CollectionUtil.isNotEmpty(columns)) {
            for (Object column : columns) {
                write.getColumns().add((String) column);
            }
        }
    }

    private static void parseTables(Map<String, Object> connection, DataXWriteVO write) {
        List<Object> tables = (List<Object>) connection.get("table");
        if (CollectionUtil.isNotEmpty(tables)) {
            for (Object table : tables) {
                write.getTables().add((String) table);
            }
        }
    }

    private static String getDefaultSchema(DataXModel dataXModel, Map<String, Object> paramMeter, String jdbcUrl) {
        String datasourceType = dataXModel.getDatasourceType();

        switch (datasourceType) {
            case "Oracle":
                return (String) paramMeter.get("username");

            case "Mysql":
                return extractDatabaseFromUrl(jdbcUrl, datasourceType);

            case "SQLServer":
                String database = extractDatabaseFromUrl(jdbcUrl, datasourceType);
                return StringUtil.isNotBlank(database) ? StringUtil.join(database, ".", "dbo") : "";

            case "PostgreSQL":
                database = extractDatabaseFromUrl(jdbcUrl, datasourceType);
                return StringUtil.isNotBlank(database) ? StringUtil.join(database, ".", "public") : "";

            case "Hive":
                return extractDatabaseFromUrl(jdbcUrl, datasourceType);

            default:
                return "";
        }
    }

    private static String extractDatabaseFromUrl(String jdbcUrl, String datasourceType) {
        String patternStr = JDBC_PATTERNS.get(datasourceType);
        if (StringUtil.isBlank(patternStr)) {
            return "";
        }

        Pattern pattern = getPattern(patternStr);
        Matcher matcher = pattern.matcher(jdbcUrl);

        return matcher.matches() ? matcher.group("database") : "";
    }

    private static Map<String, String> parserHdfsPath(String hdfsPath) {
        Map<String, String> result = new HashMap<>();

        if (StringUtil.isBlank(hdfsPath) || !hdfsPath.startsWith(HDFS_WAREHOUSE_PREFIX)) {
            return result;
        }

        String relativePath = hdfsPath.replace(HDFS_WAREHOUSE_PREFIX, "");
        String[] pathParts = relativePath.split("/");

        if (pathParts.length >= 2) {
            String defaultSchema = pathParts[0].replace(DB_SUFFIX, "");
            String table = pathParts[1];

            result.put("defaultSchema", defaultSchema);
            result.put("table", table);
        }

        return result;
    }

    private static Pattern getPattern(String sampleUrl) {
        String pattern = sampleUrl;
        pattern = replaceAll(pattern, "\\[(.*?)]", m -> "\\\\E(?:\\\\Q" + m.group(1) + "\\\\E)?\\\\Q");
        pattern = replaceAll(pattern, "\\{(.*?)}", m -> "\\\\E(?<\\\\Q" + m.group(1) + "\\\\E>" + getPropertyRegex(m.group(1)) + ")\\\\Q");
        pattern = "^\\Q" + pattern + "\\E$";
        return Pattern.compile(pattern);
    }

    private static String replaceAll(String input, String regex, Function<Matcher, String> replacer) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            matcher.appendReplacement(sb, replacer.apply(matcher));
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    private static String getPropertyRegex(String property) {
        switch (property) {
            case PROP_FOLDER:
            case PROP_FILE:
            case PROP_PARAMS:
                return ".+?";
            default:
                return "[\\\\w\\\\-_.~]+";
        }
    }

}
