package com.wcompass.edgs.modules.parser.controller;

import com.wcompass.edgs.modules.core.cache.MetaDataCacheServer;
import com.wcompass.edgs.modules.parser.service.ForeignService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description:
 * @Author: wenshijin
 * @Date: 2025年06月04日15:51
 */
@RestController
@RequestMapping("/foreign")
@Validated
@Tag(name ="对外暴露的API接口")
public class ForeignController {

    @Resource
    private ForeignService foreignService;

    @Resource
    private MetaDataCacheServer metaDataCacheServer;


    @PostMapping("/exportDataflowLogs")
    @Operation(summary ="导出血缘解析批次对应的血缘解析结果")
    public void exportDataflowLogs(@NotNull(message = "无效的元数据") @RequestParam(name = "sessionId") String sessionId) {
        foreignService.exportDataflowLogs(sessionId);
    }

    @PostMapping("/cleanCache")
    @Operation(summary ="导出血缘解析批次对应的临时表")
    public void cleanCache() {
       metaDataCacheServer.cleanAll();
    }
}
