package com.wcompass.edgs.modules.parser.model.dataflow;

import com.wcompass.api.model.CustomizeRelation;
import com.wcompass.api.model.ParserColumn;
import com.wcompass.edgs.modules.core.constant.enums.ParseLevel;
import com.wcompass.edgs.utils.CollectionUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: wenshijin
 * @Date: 2025年06月03日13:47
 */

@Data
@Schema(description = "简易模式血缘展示图")
public class CtzDataflowMapVO {

    private Set<CtzNode> nodes;

    private Set<CtzEdgeVO> edges;

    private ParseLevel parseLevel;

    public List<CustomizeRelation> ofCustomizeRelation() {
        List<CustomizeRelation> customizeRelations = new ArrayList<>();
        Map<String, ParserColumn> columnMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(this.nodes) && CollectionUtil.isNotEmpty(this.edges)) {
            //判断是否是表级
            if (ParseLevel.TABLE == parseLevel) {
                columnMap = nodes.stream().map(CtzNode::ofParserTable).collect(
                        Collectors.toMap(ParserColumn::getTablePath, Function.identity()));


            } else if (ParseLevel.COLUMN == parseLevel) {
                columnMap = nodes.stream().flatMap(ctzNode -> ctzNode.ofParserColumn().stream())
                        .collect(Collectors.toMap(
                                ParserColumn::getColumnPath, Function.identity()
                        ));

            }
            for (CtzEdgeVO edgeVO : edges) {
                if (columnMap.containsKey(edgeVO.getSourceId()) && columnMap.containsKey(edgeVO.getTargetId())) {
                    customizeRelations.add(new CustomizeRelation(columnMap.get(edgeVO.getSourceId()),
                            columnMap.get(edgeVO.getTargetId())));
                }
            }

        }
        return customizeRelations;
    }
}