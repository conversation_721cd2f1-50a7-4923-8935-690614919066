package com.wcompass.edgs.modules.parser.service.impl;

import com.wcompass.edgs.modules.core.cache.SchemaParserCache;
import com.wcompass.edgs.modules.core.constant.SqlParserConstants;
import com.wcompass.edgs.modules.core.util.LogUtil;
import com.wcompass.edgs.modules.parser.dao.read.BaseParserReadMapper;
import com.wcompass.edgs.modules.parser.model.base.MetadataParserVO;
import com.wcompass.edgs.modules.parser.model.base.ParseMetadataVO;
import com.wcompass.edgs.modules.parser.model.base.ParserMetadata;
import com.wcompass.edgs.modules.parser.model.base.TemporaryObject;
import com.wcompass.edgs.modules.parser.model.log.DataflowParseLogVO;
import com.wcompass.edgs.modules.parser.model.parser.ParserTaskResult;
import com.wcompass.edgs.modules.parser.service.BaseParseMetadataService;
import com.wcompass.edgs.modules.parser.service.BaseParserService;
import com.wcompass.edgs.modules.parser.service.DataflowParseLogService;
import com.wcompass.edgs.modules.parser.service.DataflowParseTmpService;
import com.wcompass.edgs.utils.CollectionUtil;
import com.wcompass.edgs.utils.DateUtil;
import com.wcompass.edgs.utils.ObjectUtil;
import com.wcompass.edgs.utils.StringUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.wcompass.edgs.modules.core.constant.SqlParserConstants.TREE_NODE_TYPE_METADATA;
import static com.wcompass.edgs.modules.core.constant.SqlParserConstants.TREE_NODE_TYPE_META_MODEL;

/**
 * @Description:
 * @Author: wenshijin
 * @Date: 2025年06月03日17:28
 */
@Service
@Slf4j
public class BaseParserServiceImpl implements BaseParserService {

    @Resource
    private BaseParserReadMapper baseParserReadMapper;

    @Resource
    private BaseParseMetadataService baseParseMetadataService;

    @Resource
    private DataflowParseLogService dataflowParseLogService;

    @Resource
    private DataflowParseTmpService dataflowParseTmpService;

    @Override
    public List<MetadataParserVO> queryAllSystem(Set<String> adapterIds, Set<String> classifierIds) {
        Map<String, Boolean> schemaParse = SchemaParserCache.getSCHEMA_PARSE();
        return new ArrayList<>(baseParserReadMapper.queryAllSystem(adapterIds).stream()
                .filter(m -> schemaParse.getOrDefault(m.getSchemaId()
                        , false)).collect(Collectors.toMap(MetadataParserVO::getInstanceId,
                        Function.identity(), (k1, k2) -> k1)).values());
    }

    @Override
    public List<TemporaryObject> listDatasourceOfSystem(String systemId, Set<String> adapterIds) {
        List<TemporaryObject> temporaryObjects = baseParserReadMapper.listDatasourceOfSystem(systemId, adapterIds);
        Map<String, Boolean> schemaParse = SchemaParserCache.getSCHEMA_PARSE();
        ;
        return new ArrayList<>(temporaryObjects.stream().filter(t ->
                schemaParse.getOrDefault(t.getAdditionalMessage()
                        , false)).collect(Collectors.toMap(TemporaryObject::getValue,
                Function.identity(), (k1, k2) -> k1)).values());
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public String addParserMetadata(List<ParseMetadataVO> parseMetadataVOList, String operatorId, Set<String> classifierIds, Boolean isAlter) {
        List<String> parserIds = getParserIds(parseMetadataVOList, classifierIds);
        if (CollectionUtil.isEmpty(parserIds)) {
            return null;
        }
        if (isAlter) {
           parserIds = isAlterMetadata(parserIds);
        }
        String sessionId = DateUtil.formatDatetime3(DateUtil.now());
        //获取解析id的元数据,并进行切割到500以内
        List<List<String>> idLists = CollectionUtil.split(parserIds, 500);
        //要根据需要解析的类型数据来决定写的解析逻辑。 fineReport,ABI-BI
        idLists.forEach(ids -> {
            List<ParserMetadata> parserMetadataS = baseParserReadMapper.queryAllParserMetadata(ids);
            dataflowParseLogService.addDataflowParserLog(parserMetadataS, sessionId, operatorId);
        });
        return sessionId;
    }

    @Override
    public List<TemporaryObject> listSchemaOfDatasource(String systemId, String datasourceId, Set<String> adapterIds) {
        Map<String, Boolean> schemaParse = SchemaParserCache.getSCHEMA_PARSE();
        return baseParserReadMapper.selectMountedMetadataBySystemId(
                        systemId, datasourceId, adapterIds).stream().filter(m ->
                        schemaParse.getOrDefault(m.getId(), false))
                .map(m -> new TemporaryObject(m.getCode(), m.getId()))
                .collect(Collectors.toList());
    }

    @Override
    public List<TemporaryObject> listClassifier(Set<String> classifierIds) {
        return baseParserReadMapper.listClassifier(classifierIds);
    }

    @Override
    public String executeParserMetadata(List<String> parserIds, String currentUserId) {
        if (CollectionUtil.isEmpty(parserIds)) {
            return null;
        }
        String sessionId = DateUtil.formatDatetime3(DateUtil.now());
        //获取解析id的元数据,并进行切割到500以内
        List<List<String>> idLists = CollectionUtil.split(parserIds, 500);

        //要根据需要解析的类型数据来决定写的解析逻辑。现在有 Kettle、DataX、Perl,ABI-ETL脚本
        idLists.forEach(ids -> {
            List<ParserMetadata> parserMetadataS = baseParserReadMapper.queryAllParserMetadata(ids);
            dataflowParseLogService.addDataflowParserLog(parserMetadataS, sessionId, currentUserId);
        });
        return sessionId;
    }

    @Override
    public List<String> queryAdapterNames(Set<String> adapterIds) {
        return baseParserReadMapper.queryAdapterName(adapterIds);
    }

    @Override
    public Map<String, Boolean> getParseSchema(Set<String> adapterIds, Set<String> parseClassifierIds) {
        Map<String, Boolean> resultMap = new HashMap<>();

        if (CollectionUtil.isEmpty(adapterIds)) {
            return resultMap;
        }

        //根据adapterIds 获取到对应的schema
        List<TemporaryObject> schemaCodes = baseParserReadMapper.getSchemaCode(adapterIds);

        //判断schema 下是否有能解析的数据
        for (TemporaryObject schemaCode : schemaCodes) {
            resultMap.put(schemaCode.getLabel(), ObjectUtil.isNotEmpty(baseParserReadMapper.canUnderParseMetadata(
                    schemaCode.getValue(), parseClassifierIds)));
        }
        return resultMap;
    }

    @Override
    public void updateParserResult(ParserTaskResult parserTaskResult) {
        dataflowParseLogService.updateDataflowParserLog(parserTaskResult.getParserLogId()
                , parserTaskResult.getParserStatus(), parserTaskResult.getParserMessage());
        if (!parserTaskResult.getParserStatus().equals(SqlParserConstants.ParseStatus.FAIL)) {
            dataflowParseTmpService.insertDataflowParseTmpList(parserTaskResult.getMetadataRelationList()
                    , parserTaskResult.getMetadataId(), parserTaskResult.getParserLogId());
        }
        LogUtil.endLog(parserTaskResult.getParseLogDetails());
        dataflowParseLogService.addDataflowDetailsLog(parserTaskResult.getParserLogId(), parserTaskResult.getMetadataId(), parserTaskResult.getParseLogDetails().toString());
    }


    private List<String> getParserIds(List<ParseMetadataVO> parseMetadataVOList, Set<String> classifierIds) {
        List<String> parserIds = new ArrayList<>();
        //把数据进行分类处理
        //模型类型
        List<ParseMetadataVO> parseMetaModelList = parseMetadataVOList.stream().filter(
                m -> TREE_NODE_TYPE_META_MODEL.equals(m.getMetaModel())).collect(Collectors.toList());
        //可直接解析类型
        parserIds.addAll(parseMetadataVOList.stream().filter(m -> TREE_NODE_TYPE_METADATA.equals(m.getMetaModel()) &&
                        classifierIds.contains(m.getClassifierId()))
                .map(ParseMetadataVO::getInstanceId)
                .collect(Collectors.toList()));

        //不可直接解析类型，需要获取下级能解析的数据
        List<String> parentIds = parseMetadataVOList.stream().filter(m -> TREE_NODE_TYPE_METADATA
                        .equals(m.getMetaModel()) && (!classifierIds.contains(m.getClassifierId())))
                .map(ParseMetadataVO::getInstanceId).collect(Collectors.toList());
        parserIds.addAll(getDownParserMetadataIds(parentIds, classifierIds));

        parserIds.addAll(getDownParserMetadataIdsByMetaModel(parseMetaModelList.stream().
                filter(m -> classifierIds.contains(m.getClassifierId())).collect(Collectors.toList())));

        parserIds.addAll(getDownParserMetadataIdsByMetaModelNON(parseMetaModelList.stream().
                filter(m -> !classifierIds.contains(m.getClassifierId()))
                .collect(Collectors.toList()), classifierIds));

        return parserIds;
    }

    private List<String> getDownParserMetadataIdsByMetaModel(List<ParseMetadataVO> parseMetadataVOList) {
        List<String> parserIds = new ArrayList<>();
        //获取选中并且下级能解析的模型类数据
        parseMetadataVOList.forEach(p -> {
            MetadataParserVO m = baseParseMetadataService.getMetadataParserVO(p.getInstanceId().split("@")[0]);
            parserIds.addAll(baseParserReadMapper.queryDownParserMetadataIds(
                    m.getNamespace(), Set.of(p.getClassifierId())));
        });

        return parserIds;
    }


    private List<String> getDownParserMetadataIds(List<String> instanceIds, Set<String> parserClassifierIds) {
        List<String> parserIds = new ArrayList<>();
        instanceIds.forEach(d -> {
            MetadataParserVO metadataParserVO = baseParseMetadataService.getMetadataParserVO(d);
            if (ObjectUtil.isNotNull(metadataParserVO)) {
                parserIds.addAll(baseParserReadMapper.queryDownParserMetadataIds(
                        metadataParserVO.getNamespace(), parserClassifierIds));
            }
        });

        return parserIds;
    }

    private List<String> getDownParserMetadataIdsByMetaModelNON(List<ParseMetadataVO> parseMetadataVOList
            , Set<String> classifierIds) {
        List<String> parserIds = new ArrayList<>();
        // 不用当前模型
        parseMetadataVOList.forEach(p -> {
            MetadataParserVO m = baseParseMetadataService.getMetadataParserVO(p.getInstanceId().split("@")[0]);
            parserIds.addAll(baseParserReadMapper.queryDownParserMetadataIdsByMetaModel(
                    m.getInstanceId(), p.getClassifierId(), classifierIds));
        });

        return parserIds;
    }

    private List<String> isAlterMetadata(List<String> parserIds) {
        //获取 变更之后的元数据id,根据元数据id判断

        List<DataflowParseLogVO> dataflowParseLogVOS = dataflowParseLogService.queryLastParserLogByMetadataIds(parserIds);
        Map<String, DataflowParseLogVO> idMap = dataflowParseLogVOS.stream().collect(Collectors.toMap(DataflowParseLogVO::getInstanceId, Function.identity()));
        return parserIds.stream().filter(id -> {
            if (idMap.containsKey(id)) {
                //如果存在id，则判断时间 和解析状态
                DataflowParseLogVO dataflowParseLogVO = idMap.get(id);
                if (SqlParserConstants.ParseStatus.SUCCESS.equals(dataflowParseLogVO.getParseStatus())) {
                    //如果解析状态是成功,在判断元数据的变更时间
                    String metadataAlterTime = baseParserReadMapper.getMetadataAlterTime(id);
                    if (StringUtil.isNotBlank(metadataAlterTime)) {
                        Date date = DateUtil.parseDatetime(DateUtil.timestampTransfer(String.valueOf(metadataAlterTime)));
                        return DateUtil.between(dataflowParseLogVO.getParseTime(), date, ChronoUnit.SECONDS) > 0;
                    }
                }

            }
            return true;
        }).collect(Collectors.toList());
    }
}
