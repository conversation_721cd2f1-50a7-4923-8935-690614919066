package com.wcompass.edgs.modules.core.handler.mapping.bi;

import com.wcompass.edgs.modules.core.annotations.DatasourceType;
import com.wcompass.edgs.modules.core.constant.enums.DatasourceTypeEnum;
import com.wcompass.edgs.modules.core.handler.mapping.BIDatasourceHandler;
import com.wcompass.edgs.modules.parser.model.mapping.DataflowSyncConnectionVO;
import lombok.extern.slf4j.Slf4j;


import java.util.List;

/**
 * @Description:
 * @Author: wenshijin
 * @Date: 2025年06月02日14:35
 */


@Slf4j
@DatasourceType(DatasourceTypeEnum.FINE_REPORT)
public class FineReportBIHandler extends BIDatasourceHandler {


    @Override
    public List<DataflowSyncConnectionVO> queryAllDataflowSyncConnection(String datasourceId) {
        return queryFineReportConnection(datasourceId);
    }
}
