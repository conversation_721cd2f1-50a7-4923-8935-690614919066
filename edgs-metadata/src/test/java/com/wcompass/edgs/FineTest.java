package com.wcompass.edgs;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import com.wcompass.edgs.utils.JsonUtil;
import com.wcompass.edgs.utils.StringUtil;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

public class FineTest {
    static final String username = "sysadmin";
    static final String password = "Compass@2024";
    static final String baseUrl = "http://localhost:8075";

    public static void main(String[] args) throws Exception {
        String token = getToken();
        getRootPath(token);
    }

    public static String getRootPath(String token) {
        String url = StringUtil.join(baseUrl, "/webroot/decision/v10/decision-directory-root/entries/3");
        HttpResponse response = HttpRequest.get(url)
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + token)
                .timeout(10 * 1000)
                .execute();

        if (response.getStatus() == HttpStatus.HTTP_OK) {
            String msg = response.body();
            //callback({"accessToken":"eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJzeXNhZG1pbiIsInRlbmFudElkIjoiZGVmYXVsdCIsImlzcyI6ImZhbnJ1YW4iLCJkZXNjcmlwdGlvbiI6InN5c2FkbWluKHN5c2FkbWluKSIsImV4cCI6MTcyMzEwMzkxMSwiaWF0IjoxNzIxODk0MzExLCJqdGkiOiJVNEZ4emp2dWsyek00NUtjTzJZSkxYOENia1V3NkRlUDYzeUVqTEI3ZThmdElualIifQ.xFivt5A5XIGTDD4fZPTgRBu78hW-gUl26zf0k_QXsBU","url":"http://localhost:8075/webroot/decision?fine_username=sysadmin&amp;callback=&amp;validity=-2&amp;fine_password=Compass@2024","status":"success"})
            //解析json数据
            System.out.println(msg);
        }
        return null;
    }

    //http://localhost:8075/webroot/decision/login/cross/domain?fine_username=sysadmin&fine_password=Compass%402024&validity=-2&callback=
    public static String getToken() throws Exception {
        String url = StringUtil.join(baseUrl, "/webroot/decision/login/cross/domain");

        Map<String, Object> params = new HashMap<>();
        params.put("fine_username", username);
        params.put("fine_password", URLEncoder.encode(password));
        params.put("validity", -2);
        params.put("callback", "");

        HttpResponse response = HttpRequest.get(url)
                .form(params)
                .timeout(10 * 1000)
                .execute();
        if (response.getStatus() == HttpStatus.HTTP_OK) {
            String msg = response.body();
            //callback({"accessToken":"eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJzeXNhZG1pbiIsInRlbmFudElkIjoiZGVmYXVsdCIsImlzcyI6ImZhbnJ1YW4iLCJkZXNjcmlwdGlvbiI6InN5c2FkbWluKHN5c2FkbWluKSIsImV4cCI6MTcyMzEwMzkxMSwiaWF0IjoxNzIxODk0MzExLCJqdGkiOiJVNEZ4emp2dWsyek00NUtjTzJZSkxYOENia1V3NkRlUDYzeUVqTEI3ZThmdElualIifQ.xFivt5A5XIGTDD4fZPTgRBu78hW-gUl26zf0k_QXsBU","url":"http://localhost:8075/webroot/decision?fine_username=sysadmin&amp;callback=&amp;validity=-2&amp;fine_password=Compass@2024","status":"success"})
            //解析json数据
            String json = msg.replace("callback(", "").replace(")", "");
            Map<String, Object> map = JsonUtil.parseObject(json, Map.class);
            String accessToken = (String) map.get("accessToken");
            System.out.println("accessToken: " + accessToken);
            return accessToken;
        }
        return null;
    }
}
