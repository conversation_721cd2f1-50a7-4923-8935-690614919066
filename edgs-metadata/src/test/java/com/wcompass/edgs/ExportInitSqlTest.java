package com.wcompass.edgs;

import cn.hutool.core.io.FileUtil;
import com.alibaba.druid.DbType;
import com.wcompass.edgs.dbmanager.DBDialect;
import com.wcompass.edgs.mdoel.DatabaseType;
import com.wcompass.edgs.mdoel.ExportDdlConf;
import com.wcompass.edgs.mdoel.TableInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.sql.*;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class ExportInitSqlTest {

    /**
     * 是否去掉注释
     */
    Boolean isDelComment = false;
    /**
     * oracle需要指定schema,即从那个schema进行导出
     */
    String schema = "EDGS_POC";

    /**
     * 导出到那个schema,主要是删除语句中需要指定schema
     */
    String targetSchema = "EDGS0803";

    @Test
    public void exportDdlTest() throws SQLException, UnsupportedEncodingException, InstantiationException, IllegalAccessException {
        System.out.println("开始导出初始化sql...");
        ExportInitSqlTest exportEdgsDdl = new ExportInitSqlTest();
        exportEdgsDdl.export();
        System.out.println("导出完成,文件位置：edgs-metadata/target/edgs.sql");
    }

    public Connection getEdgsConn() throws SQLException {
        //导出初始化SQL时需要配置
        String driverClass = "com.mysql.cj.jdbc.Driver";
        String url = "**********************************************************************************";
        String user = "root";
        String password = "Compass@2023";



        try {
            Class.forName(driverClass);
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("驱动加载失败，驱动类不存在(ClassNotFoundException)！出错消息：" + e.getMessage(), e);
        }
        return DriverManager.getConnection(url, user, password);
    }

    //导出配置类
    public ExportDdlConf gen_db_exp_config(DatabaseType dbType) {
        //后续资料库表结构调整需要再此处维护
        ExportDdlConf initDbConfig = new ExportDdlConf();

        //1、直接插入的固定数据
        if (dbType.equals(DatabaseType.MySQL)) {
            initDbConfig.fixedInsertSql = "INSERT INTO t99_dept (dept_id, dept_name, dept_code, dept_ename, parent_dept_id, dept_level, is_virtual, is_valid,description, creator_id, create_time, last_modifier_id, last_modify_time, leader_id) VALUES ('faa4eda11f724cc9bc802f58f6bd9484', '默认部门', '01', 'default', '0', 1, 'N', 'Y', '', 'sysadmin','2021-06-12 12:55:15', 'sysadmin', '2021-06-12 12:55:15', null);" +
                    "INSERT INTO t99_role (role_id, role_name, built_in, status, creator_id, create_time, last_modifier_id,last_modify_time, valid_flag, description) VALUES ('319e6a44711d47479db516dcf99f55d7', '超级管理员', 'Y', '1', 'sysadmin', '2021-03-16 00:00:00', 'sysadmin', '2021-05-10 13:46:35', 'Y', '超级管理员');" +
                    "INSERT INTO t99_role_user (role_id, user_id) VALUES ('319e6a44711d47479db516dcf99f55d7', 'sysadmin');" +
                    "INSERT INTO t99_role (role_id, role_name, built_in, status, creator_id, create_time, last_modifier_id, last_modify_time, valid_flag, description) VALUES('74b3139db933419690ae2e2a6fcbe39d', '普通用户', 'N', '1', 'sysadmin', '2021-10-23 14:06:28', 'sysadmin', '2021-10-23 14:06:28', 'Y', '普通用户');" +
                    "INSERT INTO t99_user (user_id, user_name, password, sex, email, mobile_phone, work_phone, creator_id, creator_name,create_time, last_modifier_id, last_modifier_name, last_modify_time, valid_flag, expire_time,is_admin, built_in, description, verify_source, birthday, real_name, avatar_attach_id, weixin) VALUES ('sysadmin', '管理员', '21218cca77804d2ba1922c33e0151105', '1', '<EMAIL>', '', '000-8888888','sysadmin', '管理员', null, 'sysadmin', '管理员', '2021-11-18 00:00:00', '1', '2029-12-31 00:00:00', 'Y', '1','超级管理员', '1', '2021-03-04 00:00:00', null, null, null);" +
                    "INSERT INTO t99_dept_user (dept_id, user_id) VALUES ('faa4eda11f724cc9bc802f58f6bd9484', 'sysadmin');" +
                    "DELETE FROM t99_system_param WHERE param_name LIKE 'cdc#_binlog#_filename' ESCAPE '#';  " +
                    "DELETE FROM t99_system_param WHERE param_name LIKE 'cdc#_binlog#_position' ESCAPE '#';";

        } else if (dbType.equals(DatabaseType.Oracle)) {
            //
            initDbConfig.fixedInsertSql = "INSERT INTO t99_dept (dept_id, dept_name, dept_code, dept_ename, parent_dept_id, dept_level, is_virtual, is_valid,description, creator_id, create_time, last_modifier_id, last_modify_time, leader_id) VALUES ('faa4eda11f724cc9bc802f58f6bd9484', '默认部门', '01', 'default', '0', 1, 'N', 'Y', '', 'sysadmin',to_date('2021-06-12 12:55:15','yyyy-mm-dd hh24:mi:ss'), 'sysadmin', to_date('2021-06-12 12:55:15','yyyy-mm-dd hh24:mi:ss'), null);\n" +
                    "INSERT INTO t99_role (role_id, role_name, built_in, status, creator_id, create_time, last_modifier_id,last_modify_time, valid_flag, description)\n" +
                    "VALUES ('319e6a44711d47479db516dcf99f55d7', '超级管理员', 'Y', '1', 'sysadmin', to_date('2021-03-16 12:55:15','yyyy-mm-dd hh24:mi:ss'), 'sysadmin', to_date('2021-05-10 13:46:35','yyyy-mm-dd hh24:mi:ss'), 'Y', '超级管理员');\n" +
                    "INSERT INTO t99_role_user (role_id, user_id)\n" +
                    "VALUES ('319e6a44711d47479db516dcf99f55d7', 'sysadmin');\n" +
                    "INSERT INTO t99_user (user_id, user_name, password, sex, email, mobile_phone, work_phone, creator_id, creator_name,create_time, last_modifier_id, last_modifier_name, last_modify_time, valid_flag, expire_time,is_admin, built_in, description, verify_source, birthday, real_name, avatar_attach_id, weixin) VALUES ('sysadmin', '管理员', '21218cca77804d2ba1922c33e0151105', '1', '<EMAIL>', '', '000-8888888','sysadmin', '管理员', null, 'sysadmin', '管理员', to_date('2021-11-18 00:00:00','yyyy-mm-dd hh24:mi:ss'), '1',to_date('2029-12-31 00:00:00','yyyy-mm-dd hh24:mi:ss'), 'Y', '1','超级管理员', '1',to_date('2021-03-04 00:00:00','yyyy-mm-dd hh24:mi:ss'), null, null, null);\n" +
                    "INSERT INTO t99_role\n" +
                    "(role_id, role_name, built_in, status, creator_id, create_time, last_modifier_id, last_modify_time, valid_flag, description)\n" +
                    "VALUES('74b3139db933419690ae2e2a6fcbe39d', '普通用户', 'N', '1', 'sysadmin',to_date('2021-10-23 14:06:28','yyyy-mm-dd hh24:mi:ss'), 'sysadmin', to_date('2021-10-23 14:06:28','yyyy-mm-dd hh24:mi:ss'), 'Y', '普通用户');";

        }

        //2、部分数据表
        List<String> querySqls = new LinkedList<>();
        querySqls.add("select *from t99_role_resource t1 where t1.res_type in ('menu', 'function') and t1.role_id = '319e6a44711d47479db516dcf99f55d7'");
        querySqls.add("select * from t07_frequency t1 where t1.frequency_type = '0' and user_id='sysadmin'");
        querySqls.add("select * from t07_job_executor");
        querySqls.add("select * from t07_job_info t1 where t1.job_type = '0' and user_id ='sysadmin'");

        initDbConfig.querySqls = querySqls;

        //3、全量数据表
        initDbConfig.fullDataTables = new LinkedList<>(Arrays.asList("t00_*",
                "t02_rule_class",
                "t02_rule_template",
                "t02_template_variables",
                "t03_vocabulary",
                "t03_ik_dictionary",
                "t08_db_keywords",
                "t99_datasource_adapter",
                "t99_datasource_adapter_mode",
                "t99_datasource_adapter_param",
                "t99_dic", "t99_menu",
                "t99_menu_func",
                "t99_system_param",
                "t99_adapter_model_version",
                "t99_adapter_version",
                "t99_adapter_mode_param",
                "t01_dataflow_example",
                "t16_chat_prompt_template"

        ));

        //4、全库DDL排除表
        initDbConfig.fullDdlExcludeTable = new LinkedList<>(Arrays.asList("act*", "flw*"));
        return initDbConfig;
    }


    public void export() throws SQLException, UnsupportedEncodingException, InstantiationException, IllegalAccessException {
        Connection connection = getEdgsConn();
        DatabaseType databaseType = DatabaseType.match(connection.getMetaData().getDatabaseProductName());
        DBDialect dialect = databaseType
                .getDbQueryClass().newInstance();
        dialect.setTargetSchema(targetSchema);

        List<TableInfo> tableInfos = dialect.getTableInfos(getEdgsConn());
        ExportDdlConf exportDdlConf = gen_db_exp_config(databaseType);

        //指定表生成DDL
        log.info("开始生成指定表DDL");
        List<String> excludeTables = exportDdlConf.getFullDdlExcludeTable();
        List<String> tables = tableInfos.stream().filter(s -> {
            //过滤掉不需要的表
            for (String excludeTable : excludeTables) {
                Pattern r = Pattern.compile("^" + excludeTable, Pattern.CASE_INSENSITIVE);
                Matcher m = r.matcher(s.getTableName());
                if (m.find()) {
                    return false;
                }
            }
            return true;
        }).map(TableInfo::getTableName).collect(Collectors.toList());


        //生成全库DDL
        log.info("开始生成全库DDL");
        String fullDdl = dialect.getAllDdls(getEdgsConn(), schema, tables, isDelComment);

        //指定表 全量数据生成insert语句
        List<String> fullDataTables = exportDdlConf.getFullDataTables();
        List<String> fullDataTablesNew = new LinkedList<>();
        for (String tableName : fullDataTables) {
            if (tableName.contains("*")) {
                tableInfos.forEach(s -> {
                    Pattern r = Pattern.compile(tableName, Pattern.CASE_INSENSITIVE);
                    Matcher m = r.matcher(s.getTableName());
                    if (m.find()) {
                        fullDataTablesNew.add(s.getTableName());
                    }
                });
            } else {
                fullDataTablesNew.add(tableName);
            }
        }
        log.info("开始生成指定表全量数据insert语句");
        String fullDataInsertSql = dialect.getFullTableData(getEdgsConn(), schema, fullDataTablesNew);

        //根据查询sql生成insert语句
        log.info("开始生成查询sql insert语句");
        List<String> querySql = exportDdlConf.getQuerySqls();
        String queryInsertSqls = dialect.getSelectSqlData(getEdgsConn(), schema, querySql);

        //固定Insert语句
        log.info("开始生成固定insert语句");
        String fixInsertSql = exportDdlConf.fixedInsertSql;

        //写入文件
        dialect.buildDdl(fullDdl, fullDataInsertSql, queryInsertSqls, fixInsertSql);

        log.info("开始写入文件");

    }
}
