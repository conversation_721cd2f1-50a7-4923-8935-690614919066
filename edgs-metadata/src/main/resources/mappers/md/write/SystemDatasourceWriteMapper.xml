<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wcompass.edgs.modules.md.dao.write.SystemDatasourceWriteMapper">
  <insert id="insert" parameterType="com.wcompass.edgs.modules.md.entity.SystemDatasource">
    insert into t01_system_datasource (system_id, datasource_id)
    values (#{systemId,jdbcType=BIGINT}, #{datasourceId,jdbcType=VARCHAR})
  </insert>
  <insert id="multiInsertSystemDatasource">
    insert into t01_system_datasource (system_id, datasource_id)
    <foreach collection="datasourceIds" item="datasourceId" separator=" union ">
      select #{systemId}, #{datasourceId} from dual
    </foreach>
  </insert>

    <insert id="insertSystemInventoryScore" databaseId="mysql" useGeneratedKeys="true">
        insert into t01_system_score (system_id,user_id,score,score_time) values(#{systemId},#{userId},#{score},#{nowTime})
    </insert>

   <insert id="insertSystemInventoryScore" databaseId="kingbase" useGeneratedKeys="true">
        insert into t01_system_score (system_id,user_id,score,score_time) values(#{systemId},#{userId},#{score},#{nowTime})
    </insert>

  <insert id="insertSystemInventoryScore">
    <selectKey keyProperty="id" resultType="int" order="BEFORE">
      select seq_t01_sys_score_id.nextval from dual
    </selectKey>
    insert into t01_system_score (id,system_id,user_id,score,score_time) values(#{id},#{systemId},#{userId},#{score},#{nowTime})
  </insert>
    <update id="updateSystemInventoryScore">
        update t01_system_score set score = #{score},score_time = #{nowTime},last_score_time=#{lastScoreTime} where system_id =#{systemId} and user_id =#{userId}
    </update>
    <delete id="deleteSystemDatasource">
    delete from t01_system_datasource where system_id=#{systemId}
  </delete>
  <delete id="deleteSystemDatasourceByDatasouceId">
    delete from t01_system_datasource
    where system_id = #{systemId}
    and datasource_id in (
        <foreach collection="deleteDatasourceIds" item="deleteDatasourceId" separator=", ">
            #{deleteDatasourceId}
        </foreach>
      )
  </delete>
</mapper>
