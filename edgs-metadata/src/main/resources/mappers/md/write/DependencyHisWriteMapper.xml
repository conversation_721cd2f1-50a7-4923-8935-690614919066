<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wcompass.edgs.modules.md.dao.write.DependencyHisWriteMapper">

    <delete id="deleteDependencyHisByMetadataId">
        delete
        from t01_dependency_his
        where to_instance_id = #{metadataId}
           or from_instance_id = #{metadataId}
    </delete>
</mapper>
