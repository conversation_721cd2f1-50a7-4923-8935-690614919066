<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wcompass.edgs.modules.md.dao.write.InstanceWriteMapper">
    <insert id="saveInstance">
    insert into t01_instance (
        instance_id, instance_code, instance_name, classifier_id, namespace, parent_id, start_time, rel_type, version, is_valid, attr_checksum
        <foreach collection="stringAttrColumns" item="stringAttrColumn" separator=", " open=", ">
            ${stringAttrColumn}
        </foreach>
    )
    values (
        #{baseInfo.instanceId}, #{baseInfo.instanceCode}, #{baseInfo.instanceName}, #{baseInfo.classifierId},
        #{baseInfo.namespace}, #{baseInfo.parentId}, #{baseInfo.startTime}, #{baseInfo.relType}, 1, #{baseInfo.isValid}, #{baseInfo.attrChecksum}
        <foreach collection="stringAttrValues" item="stringAttrValue" separator=", " open=", ">
            #{stringAttrValue}
        </foreach>
    )
  </insert>
  <insert id="batchSaveInstance">
    insert into t01_instance (
        instance_id, instance_code, instance_name, classifier_id, namespace, parent_id, start_time, rel_type, version, attr_checksum
        <foreach collection="stringAttrColumns" item="stringAttrColumn" separator=", " open=", ">
            ${stringAttrColumn}
        </foreach>
    )
    <foreach collection="instanceList" item="instance" separator=" union ">
        select #{instance.instanceId}, #{instance.instanceCode}, #{instance.instanceName}, #{instance.classifierId},
        #{instance.namespace}, #{instance.parentId}, #{instance.startTime}, #{instance.relType}, 1, #{instance.attrChecksum}
        <foreach collection="instance.stringAttrValues" item="stringAttrValue" separator=", " open=", ">
            #{stringAttrValue}
        </foreach>
        from dual
    </foreach>
  </insert>
    <insert id="batchSaveInstance" databaseId="dm">
        insert into t01_instance (
        instance_id, instance_code, instance_name, classifier_id, namespace, parent_id, start_time, rel_type, version, attr_checksum
        <foreach collection="stringAttrColumns" item="stringAttrColumn" separator=", " open=", ">
            ${stringAttrColumn}
        </foreach>
        ) values
        <foreach collection="instanceList" item="instance" separator=" , ">
            (
             #{instance.instanceId}, #{instance.instanceCode}, #{instance.instanceName}, #{instance.classifierId},
            #{instance.namespace}, #{instance.parentId}, #{instance.startTime}, #{instance.relType}, 1, #{instance.attrChecksum}
            <foreach collection="instance.stringAttrValues" item="stringAttrValue" separator=", " open=", ">
                #{stringAttrValue}
            </foreach>
            )
        </foreach>
    </insert>

    <insert id="batchSaveInstance" databaseId="mysql">
        insert into t01_instance (
        instance_id, instance_code, instance_name, classifier_id, namespace, parent_id, start_time, rel_type, version, attr_checksum
        <foreach collection="stringAttrColumns" item="stringAttrColumn" separator=", " open=", ">
            ${stringAttrColumn}
        </foreach>
        ) values
        <foreach collection="instanceList" item="instance" separator=" , ">
            (
            #{instance.instanceId}, #{instance.instanceCode}, #{instance.instanceName}, #{instance.classifierId},
            #{instance.namespace}, #{instance.parentId}, #{instance.startTime}, #{instance.relType}, 1, #{instance.attrChecksum}
            <foreach collection="instance.stringAttrValues" item="stringAttrValue" separator=", " open=", ">
                #{stringAttrValue}
            </foreach>
            )
        </foreach>
    </insert>
  <insert id="batchSaveInstanceClob">
    insert into t01_instance_clob (
    instance_id, start_time
    <foreach collection="clobAttrColumns" item="clobAttrColumn" separator=", " open=", ">
      ${clobAttrColumn}
    </foreach>
    ) values
    <foreach collection="instanceList" item="instance" separator=", ">
      (#{instance.instanceId}, #{instance.startTime}
      <foreach collection="instance.clobAttrValues" item="clobAttrValue" separator=", " open=", ">
        #{clobAttrValue}
      </foreach>
      )
    </foreach>
  </insert>

    <insert id="batchSaveInstanceClobReader">
        begin
        <foreach collection="instanceList" item="instance">
            insert into t01_instance_clob (
            instance_id, start_time
            <foreach collection="clobAttrColumns" item="clobAttrColumn" separator=", " open=", ">
                ${clobAttrColumn}
            </foreach>
            )
            values (
            #{instance.instanceId}, #{instance.startTime}
            <foreach collection="instance.clobAttrReaders" item="clobAttrReader" separator=", " open=", ">
                #{clobAttrReader,typeHandler=org.apache.ibatis.type.ClobReaderTypeHandler}
            </foreach>
            );
        </foreach>
        end;
    </insert>

  <insert id="batchSaveDepRelations" databaseId="mysql">
    insert into t01_dependency (
        from_instance_id, from_classifier_id, to_instance_id, to_classifier_id, relationship, start_time, rel_type
    )
    <foreach collection="depRelationList" item="depRelation" separator=" union all ">
        select #{depRelation.fromId}, #{depRelation.fromClassifierId},
        #{depRelation.toId}, #{depRelation.toClassifierId},
        #{depRelation.relationship}, #{depRelation.startTime},
        #{depRelation.relType} from dual
    </foreach>
  </insert>

    <insert id="batchSaveDepRelations" databaseId="kingbase">
        insert into t01_dependency (
        from_instance_id, from_classifier_id, to_instance_id, to_classifier_id, relationship, start_time, rel_type
        )
        <foreach collection="depRelationList" item="depRelation" separator=" union all ">
            select #{depRelation.fromId}, #{depRelation.fromClassifierId},
            #{depRelation.toId}, #{depRelation.toClassifierId},
            #{depRelation.relationship}, #{depRelation.startTime},
            #{depRelation.relType} from dual
        </foreach>
    </insert>

    <insert id="batchSaveDepRelations" >
        insert into t01_dependency (
        from_instance_id, from_classifier_id, to_instance_id, to_classifier_id, relationship, start_time, rel_type
        ) values
        <foreach collection="depRelationList" item="depRelation" separator=" , ">
            (
            #{depRelation.fromId}, #{depRelation.fromClassifierId},
            #{depRelation.toId}, #{depRelation.toClassifierId},
            #{depRelation.relationship}, #{depRelation.startTime},
            #{depRelation.relType}
            )
        </foreach>
    </insert>


  <insert id="createDatasourceMount">
    insert into t01_instance_mount (instance_id, namespace, classifier_id, datasource_id)
    values (#{metadataId}, #{namespace}, #{classifierId}, #{datasourceId})
  </insert>
    <delete id="deleteMountMetadata">
        delete from t01_instance_mount where instance_id = #{instanceId}
    </delete>
    <insert id="moveSubToHis">
        insert into t01_instance_his (
            instance_id, instance_code, instance_name, classifier_id, parent_id, namespace, start_time, end_time, operate_type, version
            <foreach collection="stringAttrColumns" item="stringAttrColumn" open=", " separator=", ">
                ${stringAttrColumn}
            </foreach>
        )
        select t1.instance_id, t1.instance_code, t1.instance_name, t1.classifier_id, t1.parent_id, t1.namespace, t1.start_time, #{operateTime}, #{operateType}, t1.version
        <foreach collection="stringAttrColumns" item="stringAttrColumn" open=", " separator=", ">
            t1.${stringAttrColumn}
        </foreach>
        from t01_instance t1
        where t1.namespace like #{namespaceLike}
    </insert>
    <insert id="moveSubToClobHis">
        insert into t01_instance_clob_his (
            instance_id, start_time, end_time, operate_type, version
            <foreach collection="clobAttrColumns" item="clobAttrColumn" open=", " separator=", ">
                ${clobAttrColumn}
            </foreach>
        )
        select t1.instance_id, t1.start_time, #{operateTime}, #{operateType}, t2.version
        <foreach collection="clobAttrColumns" item="clobAttrColumn" open=", " separator=", ">
            t1.${clobAttrColumn}
        </foreach>
        from t01_instance_clob t1
        inner join t01_instance t2
        on t1.instance_id = t2.instance_id
        where t2.namespace like #{namespaceLike}
    </insert>
    <delete id="deleteSub">
        delete from t01_instance t1
        where t1.namespace like #{namespaceLike}
    </delete>
    <delete id="deleteSubClob">
        delete from t01_instance_clob t1
        where t1.instance_id in (
            select t2.instance_id from t01_instance t2
            where t2.namespace like #{namespaceLike}
        )
    </delete>
    <insert id="moveCurrentToClobHis">
        insert into t01_instance_clob_his (
        instance_id, start_time, end_time, operate_type, version
        <foreach collection="clobAttrColumns" item="clobAttrColumn" open=", " separator=", ">
            ${clobAttrColumn}
        </foreach>
        )
        select t2.instance_id, t2.start_time, ${operateTime}, ${operateType}, t2.version
        <foreach collection="clobAttrColumns" item="clobAttrColumn" open=", " separator=", ">
            t1.${clobAttrColumn}
        </foreach>
        from t01_instance_clob t1
        inner join t01_instance t2
        on t1.instance_id = t2.instance_id
        where t2.instance_id = #{instanceId}
    </insert>
    <insert id="moveCurrentToHis">
        insert into t01_instance_his (
            instance_id, instance_code, instance_name, classifier_id, parent_id, namespace, start_time, end_time, operate_type, version
            <foreach collection="stringAttrColumns" item="stringAttrColumn" open=", " separator=", ">
                ${stringAttrColumn}
            </foreach>
        )
        select instance_id, instance_code, instance_name, classifier_id, parent_id, namespace, start_time, ${operateTime}, ${operateType}, version
        <foreach collection="stringAttrColumns" item="stringAttrColumn" open=", " separator=", ">
            ${stringAttrColumn}
        </foreach>
        from t01_instance where instance_id = #{instanceId}
    </insert>

    <update id="updateInstance">
        update t01_instance
        set
            instance_name = #{baseInfo.instanceName},
            start_time = #{baseInfo.startTime},
            version = version + 1,
            attr_checksum = #{baseInfo.attrChecksum}
            <if test="baseInfo.relType != null">
                , rel_type = #{baseInfo.relType}
            </if>
            <foreach collection="attrList" open="," separator=", " item="attr">
                ${attr.column} = #{attr.value}
            </foreach>
        where instance_id = #{baseInfo.instanceId}
    </update>
    <update id="updateInstanceClob">
        update t01_instance_clob
        set start_time = #{baseInfo.startTime}
        <foreach collection="attrList" open="," separator=", " item="attr">
            ${attr.column} = #{attr.value}
        </foreach>
        where instance_id = #{baseInfo.instanceId}
    </update>
    <update id="updateValidStatus">
        update t01_instance set is_valid = #{isValid}
        where instance_id = #{instanceId}
    </update>
    <delete id="deleteCurrent">
        delete from t01_instance where instance_id = #{instanceId}
    </delete>
    <delete id="deleteCurrentClob">
        delete from t01_instance_clob where instance_id = #{instanceId}
    </delete>
    <delete id="deleteDep">
        delete from t01_dependency t1
        where exists (
            select 0 from (
                <foreach collection="depRelationList" item="depRelation" separator=" union all ">
                    select #{depRelation.fromId} as fromId,
                    #{depRelation.toId} as toId,
                    #{depRelation.relationship} as relationship
                    from dual
                </foreach>
            ) t2
            where t1.from_instance_id = t2.fromId
            and t1.to_instance_id = t2.toId
            and t1.relationship = t2.relationship
        )
    </delete>
    <delete id="deleteCollect">
        delete from t99_collect t1
        where t1.res_type = '1'
        and t1.res_id = #{instanceId}
    </delete>
    <delete id="deleteSubscribe">
        delete from t99_subscribe_object t1
        where t1.obj_id = #{instanceId}
    </delete>
    <delete id="clearMetadataCollect">
        <choose>
            <when test="_databaseId == 'dm' or _databaseId == 'oracle' or _databaseId == 'kingbase'">
                delete from t99_collect t1
                where t1.id in (
                select t2.id from (
                select to_number(res_id) as res_id, id, res_type
                from t99_collect
                ) t2
                left join t01_instance t3
                on t2.res_id = t3.instance_id
                where t2.res_type = '1'
                and t3.instance_id is null
                )
            </when>
            <otherwise>
                delete from t99_collect t1
                where t1.id in (
                select t2.id from (
                select cast(res_id as unsigned ) as res_id, id, res_type
                from t99_collect
                ) t2
                left join t01_instance t3
                on t2.res_id = t3.instance_id
                where t2.res_type = '1'
                and t3.instance_id is null
                )
            </otherwise>
        </choose>
    </delete>
    <delete id="deleteVisit">
        delete from t99_visit t1
        where t1.res_type = '1'
          and t1.res_id = #{instanceId}
    </delete>

    <delete id="clearMetadataVisit">

        <choose>
            <when test="_databaseId == 'dm' or _databaseId == 'oracle' or _databaseId == 'kingbase'">
                delete from t99_visit t1
                where t1.id in (
                select t2.id from (
                    select to_number(res_id) as res_id, id, res_type
                    from t99_visit
                ) t2
                left join t01_instance t3
                on t2.res_id = t3.instance_id
                where t2.res_type = '1'
                and t3.instance_id is null
                )
            </when>
            <otherwise>
                delete from t99_visit t1
                where t1.id in (
                select t2.id from (
                select cast(res_id as unsigned ) as res_id, id, res_type
                from t99_visit
                ) t2
                left join t01_instance t3
                on t2.res_id = t3.instance_id
                where t2.res_type = '1'
                and t3.instance_id is null
                )
            </otherwise>
        </choose>
    </delete>

    <delete id="deleteDataflow">
        delete from t01_dataflow_result t1
        where t1.source_id = #{instanceId}
        or t1.target_id = #{instanceId}
        or t1.transform_id = #{instanceId}
    </delete>

    <delete id="clearMetadataDataflow">
        delete from t01_dataflow_result t1
        where t1.id in (
            select t2.id
            from t01_dataflow_result t2
                     left join t01_instance t3
                               on t2.source_id = t3.instance_id
            where t2.source_id is not null
              and t3.instance_id is null
            union
            select t2.id
            from t01_dataflow_result t2
                     left join t01_instance t3
                               on t2.target_id = t3.instance_id
            where t2.target_id is not null
              and t3.instance_id is null
            union
            select t2.id
            from t01_dataflow_result t2
                     left join t01_instance t3
                               on t2.transform_id = t3.instance_id
            where t2.transform_id is not null
              and t3.instance_id is null
        )
    </delete>

    <delete id="deleteStandardMapping">
        delete from t03_standard_mapping
        where metadata_id = #{instanceId}
    </delete>

    <delete id="clearMetadataStandardMapping" databaseId="mysql">
        delete t1 from t03_standard_mapping t1
        inner join (
            select t2.id
            from t03_standard_mapping t2
            left join t01_instance t3
            on t2.metadata_id = t3.instance_id
            where t3.instance_id is null
        ) t4
        on t1.id = t4.id
    </delete>

    <delete id="clearMetadataStandardMapping">
        delete from t03_standard_mapping t1
        where t1.id in (
            select t2.id
            from t03_standard_mapping t2
            left join t01_instance t3
            on t2.metadata_id = t3.instance_id
            where t3.instance_id is null
        )
    </delete>

    <delete id="deleteDsMdDependency">
        delete from T03_DS_MD_DEPENDENCY t1
        where (t1.from_instance_id = #{instanceId}) or (t1.to_instance_id = #{instanceId})
    </delete>
    <delete id="clearMetadataDsMdDependency">
        delete from t03_ds_md_dependency t1
        where t1.id in (
            select t2.id
            from t03_ds_md_dependency t2
            inner join t01_instance t3
            on t2.from_instance_id = t3.instance_id
            where t2.from_type = 'metadata'
            union
            select t2.id
            from t03_ds_md_dependency t2
            inner join t01_instance t3
            on t2.to_instance_id = t3.instance_id
            where t2.to_type = 'metadata'
        )
    </delete>
    <delete id="clearMetadataDsMdDependency" databaseId="mysql">
        delete t1 from t03_ds_md_dependency t1
        inner join (
            select t2.id
            from t03_ds_md_dependency t2
            inner join t01_instance t3
            on t2.from_instance_id = t3.instance_id
            where t2.from_type = 'metadata'
            union
            select t2.id
            from t03_ds_md_dependency t2
            inner join t01_instance t3
            on t2.to_instance_id = t3.instance_id
            where t2.to_type = 'metadata'
        ) t4
        on t1.id = t4.id
    </delete>
    <delete id="deleteOnSource">
        <bind name="namespaceLike" value="namespace + '%'"/>
        update t01_instance
        set rel_type = '2'
        where namespace like #{namespaceLike}
        and rel_type = '1'
    </delete>
    <delete id="clearDirtyMetadata">
        delete from t01_instance
        where instance_id in (
        <foreach collection="deleteMetadataIds" item="deleteMetadataId" separator=", ">
            #{deleteMetadataId}
        </foreach>
        )
    </delete>
    <delete id="removeInstanceDelete">
        delete from t01_instance_delete where namespace_like = #{namespaceLike}
    </delete>
    <delete id="deleteDataflowById">
        delete from t01_dataflow_result where id = #{dataflowId}
    </delete>
    <insert id="moveDepToHis">
        insert into t01_dependency_his (from_instance_id, from_classifier_id, to_instance_id, to_classifier_id, relationship, start_time, end_time, rel_type)
        select t1.from_instance_id, t1.from_classifier_id, t1.to_instance_id, t1.to_classifier_id, t1.relationship, t1.start_time, #{operateTime}, rel_type
        from t01_dependency t1
        inner join (
            <foreach collection="depRelationList" item="depRelation" separator=" union all ">
                select #{depRelation.fromId} as fromId,
                       #{depRelation.toId} as toId,
                       #{depRelation.relationship} as relationship
                from dual
            </foreach>
        ) t2
        on t1.from_instance_id = t2.fromId
        and t1.to_instance_id = t2.toId
        and t1.relationship = t2.relationship
    </insert>
    <insert id="saveInstanceDelete">
        insert into t01_instance_delete (namespace_like, operator_id, operator_name, datasource_id, datasource_name, operate_time)
        values (#{namespaceLike}, #{operatorId}, #{operatorName}, #{datasourceId}, #{datasourceName}, #{operateTime})
    </insert>

    <delete id="deleteDataflowTmpById">
        delete from t01_dataflow_parse_tmp where id = #{dataflowId}
    </delete>
</mapper>
