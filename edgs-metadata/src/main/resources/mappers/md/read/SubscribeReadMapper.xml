<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wcompass.edgs.modules.md.dao.read.SubscribeReadMapper">

  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, res_type, res_id, subscriber_id, create_time
  </sql>

    <resultMap id="SubscriptionUserinfoVOMap" type="com.wcompass.edgs.modules.md.model.SubscriptionUserinfoVO">
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="sub_id" jdbcType="VARCHAR" property="subId"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <collection property="deptNames" resultMap="deptNameMap"/>
    </resultMap>

    <resultMap id="deptNameMap" type="String">
        <result column="dept_name"/>
    </resultMap>


  <select id="existSubscribe" resultType="boolean">
    select case when count(0) > 0 then 1 else 0 end
    from t99_subscribe
    where res_type = #{resourceType}
    and res_id = #{resourceId}
    and subscriber_id = #{subscriberId}
  </select>

  <select id="findSubName" resultType="java.lang.Integer">
    select count(t1.sub_name) from
      (select sub_name,create_user from t99_subscribe_definition where sub_type = #{subType}) t1
    where t1.sub_name = #{subName} and t1.create_user = #{userId}
  </select>

  <select id="subscriptionList" resultType="com.wcompass.edgs.modules.md.model.SubscriptionVO">
    select distinct t1.id,t1.sub_name,t1.sub_desc,t3.user_name,t1.create_time, enable_link_source,
            case when t1.create_user != #{userId} then '0' else '1' end checked,t.subscriptionNum
    from t99_subscribe_definition t1
      inner join t99_subscribe_user t2
          on t1.id = t2.sub_id
            and (t2.user_id = #{userId} or t1.create_user = #{userId})
      inner join (select sub_id,count(user_id) subscriptionNum from t99_subscribe_user group by sub_id) t
          on t.sub_id = t1.id
      inner join t99_user t3
          on t1.create_user = t3.user_id
    <where>
        <if test="keyword != null">
            <bind name="partten" value="'%'+keyword+'%'"/>
            <choose>
                <when test="_databaseId == 'dm' or _databaseId == 'oracle' or _databaseId == 'kingbase'">
                    and (
                        upper(t1.sub_name) like upper(#{partten}) escape '\'
                     or upper(t1.sub_desc) like upper(#{partten}) escape '\'
                    )
                </when>
                <otherwise>
                    and (
                        upper(t1.sub_name) like upper(#{partten})
                     or upper(t1.sub_desc) like upper(#{partten})
                    )
                </otherwise>
            </choose>

        </if>
        <if test="subType != null">
            and t1.sub_type = #{subType}
        </if>
    </where>
    order by t1.create_time desc
  </select>

  <select id="getUserinfoBySubId" resultMap="SubscriptionUserinfoVOMap">
    select t.user_id,t.sub_id,t1.user_name,t1.email,t2.dept_name from t99_subscribe_user t
      inner join t99_user t1
                 on t.user_id = t1.user_id
      left join (select t3.dept_name,t4.user_id from t99_dept t3
                    inner join t99_dept_user t4 on t3.dept_id = t4.dept_id) t2
                on t.user_id = t2.user_id
    where sub_id = ${subId}
  </select>

  <select id="selectSubscriptionBySubId" resultType="com.wcompass.edgs.modules.md.model.SubscriptionVO">
    select id, sub_name, sub_desc, sub_type, send_email, send_sms, send_wechat, create_todo, create_user, create_time, enable_link_source,
           case when sub_new is null then '0' else sub_new end as sub_new from t99_subscribe_definition
    where id = ${subId}
  </select>

    <select id="selectSubscriptionObjBySubId" resultType="com.wcompass.edgs.modules.md.model.SubscriptionObjVO">

        <choose>
            <when test="_databaseId == 'dm' or _databaseId == 'oracle' or _databaseId == 'kingbase'">
                select *
                from (
                    select temp.*, rownum as page_row_id
                    from (
                        select distinct case when t1.instance_id is not null then t1.instance_id else t2.instance_id end instance_id,
                        case when t1.instance_code is not null then t1.instance_code else t2.instance_code end instance_code,
                        case when t1.instance_name is not null then t1.instance_name else t2.instance_name end instance_name,
                        t1.classifier_id,
                        t4.instance_code "SCHEMA",
                        t4.datasource_name
                        from t01_instance t1
                        left join t01_instance_his t2
                        on t1.instance_id = t2.instance_id
                        left join (
                        select t5.instance_id,t5.instance_code,t6.datasource_name from t01_instance t5
                        left join (select t01_instance_mount.instance_id,t99_datasource.datasource_name from t01_instance_mount
                        inner join t99_datasource  on t01_instance_mount.datasource_id = t99_datasource.datasource_id) t6
                        on t5.instance_id = t6.instance_id
                        where t5.instance_id in (select t7.parent_id from t01_instance t7
                        inner join (select obj_id from t99_subscribe_object where sub_id = ${subId}) t8
                        on t7.instance_id = t8.obj_id)
                        )t4
                        on t1.parent_id = t4.instance_id
                        where t1.instance_id in (select obj_id from t99_subscribe_object where sub_id = ${subId})
                        <if test="keyword != null and keyword != ''">
                            and (instr(upper(t1.instance_code),upper(#{keyword}))>0 or instr(upper(t1.instance_name),upper(#{keyword}))>0)
                        </if>
                        order by instance_code
                    ) temp
                ) t
                where (${pageCurrent} + ${pageSize}) >= t.page_row_id and t.page_row_id > #{pageCurrent}
            </when>
            <otherwise>
                select distinct case when t1.instance_id is not null then t1.instance_id else t2.instance_id end instance_id,
                case when t1.instance_code is not null then t1.instance_code else t2.instance_code end instance_code,
                case when t1.instance_name is not null then t1.instance_name else t2.instance_name end instance_name,
                t1.classifier_id,
                t4.instance_code "schema",
                t4.datasource_name
                from t01_instance t1
                left join t01_instance_his t2
                on t1.instance_id = t2.instance_id
                left join (
                select t5.instance_id,t5.instance_code,t6.datasource_name from t01_instance t5
                left join (select t01_instance_mount.instance_id,t99_datasource.datasource_name from t01_instance_mount
                inner join t99_datasource  on t01_instance_mount.datasource_id = t99_datasource.datasource_id) t6
                on t5.instance_id = t6.instance_id
                where t5.instance_id in (select t7.parent_id from t01_instance t7
                inner join (select obj_id from t99_subscribe_object where sub_id = ${subId}) t8
                on t7.instance_id = t8.obj_id)
                )t4
                on t1.parent_id = t4.instance_id
                where t1.instance_id in (select obj_id from t99_subscribe_object where sub_id = ${subId})
                <if test="keyword != null and keyword != ''">
                    and (instr(upper(t1.instance_code),upper(#{keyword}))>0 or instr(upper(t1.instance_name),upper(#{keyword}))>0)
                </if>
                order by instance_code
                limit ${pageCurrent},#{pageSize}
            </otherwise>
        </choose>
    </select>
    <select id="selectSubscriptionObjId" resultType="java.lang.String">
        select obj_id from t99_subscribe_object where sub_id = ${subId}
    </select>

    <select id="selectSubscriptionUserId" resultType="java.lang.String">
        select user_id from t99_subscribe_user where sub_id = ${subId}
    </select>

    <select id="selectSubscriptionUserIdByObjId" resultType="java.lang.String">
        select user_id from t99_subscribe_object t
         inner join (select t1.id,t2.user_id from t99_subscribe_definition t1
                      inner join t99_subscribe_user t2
                       on t1.id = t2.sub_id) t3
          on t.sub_id = t3.id
        where t.obj_id = #{objId}
    </select>

    <select id="judgeSendType" resultType="com.wcompass.edgs.cloud.api.client.model.UserDTO">
        select user_id,user_name,weixin,mobile_phone from t99_user
            where user_id in
        <foreach collection="userIds" item="userId" separator="," open="(" close=")">
            #{userId}
        </foreach>
    </select>

    <select id="getSubIdByObjId" resultType="java.lang.Integer">
        select sub_id from t99_subscribe_object where obj_id = #{objId}
    </select>

    <select id="listRelatedInfoByPage" resultType="java.lang.Long">
        select  t3.instance_id
        from (
        <choose>
            <when test="!isClob">
                SELECT t1.instance_id
                from t01_instance t1
                left join t01_instance_his t2
                on t1.instance_id = t2.instance_id
                and t2.version = 0
                <where>
                    <if test="namespaces != null and namespaces.size() > 0">
                        and (
                        <foreach collection="namespaces" item="namespace" separator=" or " >
                            t1.namespace like #{namespace}
                        </foreach>
                        )
                    </if>
                    and t1.classifier_id = #{relateClassifierId}

                    <if test="attrField != null and attrField.trim() != '' and keyword != null and keyword.trim() != ''">
                        <bind name="keywordLike" value=" '%' + keyword + '%' "/>
                        <choose>
                            <when test="_databaseId == 'dm' or _databaseId == 'oracle'">
                                <if test="!isClob">
                                    and (upper(t1.${attrField}) like upper(#{keywordLike}) escape '\'
                                        <if test="!attrField.equals('is_valid')">
                                            or
                                            upper(t2.${attrField}) like upper(#{keywordLike}) escape '\'
                                        </if>
                                    )
                                </if>
                            </when>
                            <otherwise>
                                <if test="!isClob">
                                    and (upper(t1.${attrField}) like upper(#{keywordLike})
                                    <if test="!attrField.equals('is_valid')">
                                        or
                                        upper(t2.${attrField}) like upper(#{keywordLike})
                                    </if>
                                    )
                                </if>
                            </otherwise>
                        </choose>
                    </if>

                    <if test="attrField != null and attrField.trim() != '' and searchItems != null and searchItems.size() > 0">
                        and (
                        t1.${attrField} in
                        <foreach collection="searchItems" item="searchItem" open="(" separator=", " close=")">
                            #{searchItem}
                        </foreach>
                        or
                        t2.${attrField} in
                        <foreach collection="searchItems" item="searchItem" open="(" separator=", " close=")">
                            #{searchItem}
                        </foreach>
                        )
                    </if>
                </where>
            </when>
            <when test="isClob">
                SELECT t4.instance_id
                from t01_instance_clob t4
                left join t01_instance_clob_his t5
                on t4.instance_id = t5.instance_id
                and t5.version = 0
                <where>
                    and exists(
                    select 1 from (
                    SELECT t.instance_id
                    from t01_instance t
                    where (
                    <foreach collection="namespaces" item="namespace" separator=" or ">
                        t.namespace like #{namespace}
                    </foreach>)
                    and t.classifier_id = #{relateClassifierId}
                    )temp
                    where temp.instance_id = t4.instance_id
                    )

                    <if test="attrField != null and attrField.trim() != '' and keyword != null and keyword.trim() != ''">
                        <bind name="keywordLike" value=" '%' + keyword + '%' "/>
                        <choose>
                            <when test="_databaseId == 'dm' or _databaseId == 'oracle'">
                                and (upper(t4.${attrField}) like upper(#{keywordLike}) escape '\'
                                or
                                upper(t5.${attrField}) like upper(#{keywordLike}) escape '\'
                                )
                            </when>
                            <otherwise>
                                and (upper(t4.${attrField}) like upper(#{keywordLike})
                                or
                                upper(t5.${attrField}) like upper(#{keywordLike})
                                )
                            </otherwise>
                        </choose>

                    </if>
                </where>
            </when>
        </choose>
        ) t3
        where not exists (
        select 1
        from (
        select sub_id
        from t99_subscribe_user
        where user_id = #{userId}
        union
        select id as sub_id
        from t99_subscribe_definition
        where create_user = #{userId}
        )t1
        inner join t99_subscribe_object t2
        on t1.sub_id = t2.sub_id
        where t2.obj_id=t3.instance_id)
    </select>

    <select id="listSchemaIdBySystemId" resultType="java.lang.Long" parameterType="java.lang.String">
        select instance_id from t01_instance_mount t8
             inner join t01_system_datasource t9 on t8.datasource_id = t9.datasource_id
            <where>
                <if test="systemId!=null and systemId!=''">
                    t9.system_id = #{systemId}
                </if>
            </where>
    </select>

    <select id="count" resultType="java.lang.Integer">
        select count(*) from t99_subscribe_object t
        inner join t01_instance t1 on t1.instance_id = t.obj_id
        where t.sub_id = #{subId}
        <if test="keyword != null and keyword != ''">
            and (instr(upper(t1.instance_code),upper(#{keyword}))>0 or instr(upper(t1.instance_name),upper(#{keyword}))>0)
        </if>
    </select>

    <select id="selectSubIdByCreateUserId" parameterType="java.lang.String" resultType="java.lang.Long">
        select id from t99_subscribe_definition where create_user = #{userId}
    </select>

    <select id="selectExcludeSubId" resultType="java.lang.Long">
        select sub_id from t99_subscribe_user t where t.user_id = 'sysadmin'
        <if test="subIds!=null and subIds.size()>0">
         and not exists(
            select 1 from (
                <foreach collection="subIds" item="subId" separator="union all">
                    select ${subId} as id from dual
                </foreach>
                              )t1
                    where t.sub_id = t1.id
                )
        </if>
    </select>

    <select id="selectObjIdBySubIds" resultType="java.lang.String">
        select distinct t.obj_id from t99_subscribe_object t
        <if test="subIds!=null and subIds.size()>0">
            where exists(
                select 1 from (
                <foreach collection="subIds" item="subId" separator="union all">
                    select ${subId} as id from dual
                </foreach>
                )t1
                where t.sub_id = t1.id
                )
        </if>
        <if test="subIds == null or subIds.size()==0">
            where 1=2
        </if>
    </select>

  <select id="getCreateSubUserId" resultType="java.lang.String">
      select t1.create_user from t99_subscribe_definition t1 where t1.id = #{subId}
    </select>
    <select id="getDataflowResult" resultType="com.wcompass.edgs.modules.md.model.datamap.DataflowResultVO">
        select distinct
        t1.id,
        t1.source_id,
        t2.instance_code as source_code,
        t2.instance_name as source_name,
        t2.parent_id     as source_parent_id,
        t2.namespace     as source_namespace,
        t1.source_classifier_id,
        t1.target_id,
        t3.instance_code as target_code,
        t3.instance_name as target_name,
        t3.parent_id     as target_parent_id,
        t3.namespace     as target_namespace,
        t1.target_classifier_id,
        t1.schema_id
        from t01_dataflow_result t1
        inner join t01_instance t2 on t1.source_id = t2.instance_id
        <if test="classifierIds != null and classifierIds.size() > 0">
            and t2.classifier_id in
            <foreach collection="classifierIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        inner join t01_instance t3 on t1.target_id = t3.instance_id
        <if test="classifierIds != null and classifierIds.size() > 0">
            and t3.classifier_id in
            <foreach collection="classifierIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        where t1.source_id in
            <foreach collection="nodeIds" item="nodeId" separator="," open="(" close=")">
                #{nodeId}
            </foreach>
    </select>
    <select id="queryInfluence" resultType="com.wcompass.edgs.modules.md.model.dataflow.EdgeVO">
        select source_id,target_id
        from t01_dataflow_result
        where source_id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>
    <select id="getInfo" resultType="com.wcompass.edgs.modules.md.entity.AlterationHistory">
        select
            t1.instance_id,
            t1.instance_code,
            t1.instance_name,
            t1.namespace,
            t1.classifier_id,
            t2.classifier_name
        from t01_instance t1
                 inner join t00_classifier t2
                            on t1.classifier_id = t2.classifier_id
        where t1.instance_id = #{id}
    </select>
    <select id="getDatasourceInfo" resultType="com.wcompass.edgs.modules.md.entity.AlterationHistory">
        select t2.datasource_id,
               t2.datasource_name,
               t4.instance_id as system_id,
               t4.instance_name as system_name
        from t01_instance t1
                 inner join t99_datasource t2
                            on t1.instance_code = t2.datasource_id
                 inner join t01_system_datasource t3
                            on t2.datasource_id = t3.datasource_id
                 inner join t01_instance t4
                            on t3.system_id = t4.instance_id
        where t1.instance_id = #{id}
    </select>

    <select id="getSubObjId" resultType="java.lang.String">
        select obj_id
        from t99_subscribe_object
        where sub_id = #{subId}
    </select>
    <select id="queryDataSource" resultType="com.wcompass.edgs.modules.md.model.DatasourceVO">
        select distinct t3.DATASOURCE_ID,
                        t3.DATASOURCE_NAME
        from T01_INSTANCE t1
                 inner join T01_INSTANCE t2
                            on t1.PARENT_ID = t2.INSTANCE_ID
                                and t2.CLASSIFIER_ID = 'Root'
                 inner join T99_DATASOURCE t3
                            on t2.INSTANCE_CODE = t3.DATASOURCE_ID
                 join T01_INSTANCE t4 on t4.PARENT_ID = t1.INSTANCE_ID
                 join T99_SUBSCRIBE_OBJECT t5 on t5.OBJ_ID = t4.INSTANCE_ID
        where t5.SUB_ID = #{subId}
    </select>
    <select id="querySchema" resultType="com.wcompass.edgs.core.Option">
        select distinct
            t1.INSTANCE_ID as value,
            t1.INSTANCE_CODE as label
        from T01_INSTANCE t1
            inner join T01_INSTANCE t2
        on t1.PARENT_ID = t2.INSTANCE_ID
            and t2.CLASSIFIER_ID = 'Root'
            join T01_INSTANCE t4 on t4.PARENT_ID = t1.INSTANCE_ID
            join T99_SUBSCRIBE_OBJECT t5 on t5.OBJ_ID = t4.INSTANCE_ID
        where t5.SUB_ID = #{subId}
          and t2.INSTANCE_CODE = #{datasourceId}
    </select>
    <select id="selectObj" resultType="com.wcompass.edgs.modules.md.model.SubscriptionObjVO">
        <choose>
            <when test="_databaseId == 'dm' or _databaseId == 'oracle' or _databaseId == 'kingbase'">
                select *
                from (
                select temp.*, rownum as page_row_id
                from (
                select distinct
                t1.instance_id,
                t1.instance_code,
                case when t2.instance_name is not null then t2.instance_name else t1.instance_name end instance_name,
                t1.classifier_id,
                t3.INSTANCE_CODE as schema_name,
                t3.INSTANCE_ID as schema_id,
                t4.INSTANCE_CODE   as           datasource_id,
                t5.datasource_name as         datasource_name
                from t01_instance t1
                left join t01_instance_his t2
                on t1.instance_id = t2.instance_id
                and t2.version = 0
                inner join T01_INSTANCE t3
                on t1.PARENT_ID = t3.INSTANCE_ID
                and t3.CLASSIFIER_ID = 'Schema'
                inner join T01_INSTANCE t4
                on t3.PARENT_ID = t4.INSTANCE_ID
                and t4.CLASSIFIER_ID = 'Root'
                left join t99_datasource t5
                on t4.instance_code = t5.datasource_id
                where exists(
                select 0 from (select obj_id from t99_subscribe_object where sub_id = #{subId})tmp
                where t1.INSTANCE_ID = tmp.OBJ_ID
                )
                order by instance_code
                ) temp where 1=1
                <if test="keyword != null and keyword != ''">
                    and (instr(upper(temp.instance_code),upper(#{keyword}))>0 or instr(upper(temp.instance_name),upper(#{keyword}))>0)
                </if>
                <if test="dataSourceId != null and dataSourceId != ''">
                    and temp.datasource_id = #{dataSourceId}
                </if>
                <if test="schemaId != null and schemaId != ''">
                    and temp.schema_id = #{schemaId}
                </if>
                ) t
            </when>
            <otherwise>
                select *
                from (select distinct t1.instance_id,
                t1.instance_code,
                case
                when t2.instance_name is not null then t2.instance_name
                else t1.instance_name end instance_name,
                t1.classifier_id,
                t3.INSTANCE_CODE   as         schema_name,
                t3.INSTANCE_ID     as         schema_id,
                t4.INSTANCE_CODE   as         datasource_id,
                t5.datasource_name as         datasource_name
                from t01_instance t1
                left join t01_instance_his t2
                on t1.instance_id = t2.instance_id
                and t2.version = 0
                inner join T01_INSTANCE t3
                on t1.PARENT_ID = t3.INSTANCE_ID
                and t3.CLASSIFIER_ID = 'Schema'
                inner join T01_INSTANCE t4
                on t3.PARENT_ID = t4.INSTANCE_ID
                and t4.CLASSIFIER_ID = 'Root'
                left join t99_datasource t5
                on t4.instance_code = t5.datasource_id
                where exists(select 0
                from (select obj_id from t99_subscribe_object where sub_id = #{subId}) tmp
                where t1.INSTANCE_ID = tmp.OBJ_ID)
                order by instance_code) temp where 1=1
                <if test="keyword != null and keyword != ''">
                    and (instr(upper(temp.instance_code),upper(#{keyword}))>0 or instr(upper(temp.instance_name),upper(#{keyword}))>0)
                </if>
                <if test="dataSourceId != null and dataSourceId != ''">
                    and temp.datasource_id = #{dataSourceId}
                </if>
                <if test="schemaId != null and schemaId != ''">
                    and temp.schema_id = #{schemaId}
                </if>
                order by temp.instance_code
            </otherwise>
        </choose>
    </select>
</mapper>
