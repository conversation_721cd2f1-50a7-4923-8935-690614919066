<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wcompass.edgs.modules.md.dao.read.DataflowResultReadMapper">
  <resultMap id="BaseResultMap" type="com.wcompass.edgs.modules.md.entity.DataflowResult">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="source_id" jdbcType="BIGINT" property="sourceId" />
    <result column="source_classifier_id" jdbcType="VARCHAR" property="sourceClassifierId" />
    <result column="target_id" jdbcType="BIGINT" property="targetId" />
    <result column="target_classifier_id" jdbcType="VARCHAR" property="targetClassifierId" />
    <result column="transform_id" jdbcType="BIGINT" property="transformId" />
    <result column="transform_classifier_id" jdbcType="VARCHAR" property="transformClassifierId" />
    <result column="source_type" jdbcType="VARCHAR" property="sourceType" />
  </resultMap>
    <select id="metadataRelation" resultType="com.wcompass.edgs.modules.md.model.datamap.DataflowResultVO">
        select t1.source_id,
        t1.source_classifier_id,
        t2.instance_name as sourceName,
        t2.instance_code as sourceCode,
        t1.target_id,
        t1.target_classifier_id,
        t3.instance_name as targetName,
        t3.instance_code as targetCode
        from T01_DATAFLOW_RESULT t1
        inner join t01_instance t2
        on t1.source_id = t2.instance_id
        inner join t01_instance t3
        on t1.target_id = t3.instance_id
        where exists(select 0
        from T01_INSTANCE tt1
        where tt1.INSTANCE_ID = #{fromInstanceId}
        and t2.NAMESPACE like concat(tt1.NAMESPACE, '%'))
        and exists(select 0
        from T01_INSTANCE tt2
        where tt2.INSTANCE_ID = #{toInstanceId}
        and t3.NAMESPACE like concat(tt2.NAMESPACE, '%'))
        and exists(select 0
        from T00_RELATION_DATAFLOW tt3
        where tt3.dataflow_level = '2'
        and tt3.SOURCE_CLASSIFIER_ID = t1.SOURCE_CLASSIFIER_ID
        and tt3.TARGET_CLASSIFIER_ID = t1.TARGET_CLASSIFIER_ID)
    </select>

  <select id="queryDataflowResultByPage" resultType="com.wcompass.edgs.modules.md.model.dataflow.DataflowVO">
      select t1.id,
      t1.source_id            as sourceInstanceId,
      t1.source_classifier_id as sourceClassifier,
      t1.target_id            as targetInstanceId,
      t1.target_classifier_id as targetClassifier,
      t1.transform_id as transformInstanceId,
      t1.transform_classifier_id as transformClassifier,
      t1.create_time as createTimesTamp,
      t1.source_type as sourceType
      from t01_dataflow_result t1
      inner join t01_instance t2 on t1.source_id = t2.instance_id
      inner join t01_instance t3 on t1.target_id = t3.instance_id
      where t1.source_classifier_id != 'System'
      <if test="namespaces != null">
          and
          (
          <foreach collection="namespaces" item="namespace" separator=" or " open="(" close=")">
              t2.namespace like #{namespace}
          </foreach>
          and
          <foreach collection="namespaces" item="namespace" separator=" or " open="(" close=")">
              t3.namespace like #{namespace}
          </foreach>
          )
      </if>
      <if test="sourceId != null and sourceId != ''">
          and t2.instance_id = #{sourceId}
      </if>
      <if test="targetId != null and targetId != ''">
          and t3.instance_id = #{targetId}
      </if>
      <if test="sourceNameSpace != null and sourceNameSpace != ''">
          <bind name="sourceNamespaceLike" value="sourceNameSpace + '%'"/>
          and  t2.namespace like #{sourceNamespaceLike}
      </if>
      <if test="targetNameSpace != null and targetNameSpace != ''">
          <bind name="targetNamespaceLike" value="targetNameSpace + '%'"/>
          and t3.namespace like #{targetNamespaceLike}
      </if>
      <if test="sourceClassifierId != null and sourceClassifierId != ''">
          and t1.source_classifier_id = #{sourceClassifierId}
      </if>
      <if test="targetClassifierId != null and targetClassifierId != ''">
          and t1.target_classifier_id = #{targetClassifierId}
      </if>
      <if test="sourceType != null and sourceType != ''">
          and t1.source_type = #{sourceType}
      </if>
      order by t1.create_time desc,t1.source_id,t1.target_id,t1.transform_id
  </select>

  <select id="queryDataflowSourceDatasource" resultType="com.wcompass.edgs.modules.md.model.SystemDatasourceVO">
      select distinct td.datasource_id ,td.datasource_name
      from t01_dataflow_result tdr
      inner join t01_instance ti on ti.instance_id =tdr.source_id
      inner join t01_instance_mount tim on ti.namespace like concat(tim.namespace,'%')
      inner join t01_system_datasource t2 on tim.datasource_id = t2.datasource_id
      inner join t99_datasource td on td.datasource_id =t2.datasource_id
    <where>
        <if test="datasourceName!=null and datasourceName!=''">
            <bind name="datasourceNameLike" value=" '%' + datasourceName.trim() + '%' "/>
            and upper(td.datasource_name) like upper(#{datasourceNameLike})
        </if>
    </where>
  </select>


  <select id="queryDataflowTargetDatasource" resultType="com.wcompass.edgs.modules.md.model.SystemDatasourceVO">
      select distinct td.datasource_id ,td.datasource_name
      from t01_dataflow_result tdr
      inner join t01_instance ti on ti.instance_id =tdr.target_id
      inner join t01_instance_mount tim on ti.namespace like concat(tim.namespace,'%')
      inner join t01_system_datasource t2 on tim.datasource_id = t2.datasource_id
      inner join t99_datasource td on td.datasource_id =t2.datasource_id
    <if test="datasourceName!=null and datasourceName!=''">
        <bind name="datasourceNameLike" value=" '%' + datasourceName.trim() + '%' "/>
        and upper(td.datasource_name) like upper(#{datasourceNameLike})
    </if>
  </select>

  <select id="queryDataflowSourceInstanceByPage" resultType="com.wcompass.edgs.modules.md.model.dataflow.DataflowInstanceVO">
      select distinct ti.instance_id ,ti.instance_code ,ti.namespace
      from t01_dataflow_result tdr
      inner join t01_instance ti on tdr.source_id =ti.instance_id
      inner join t01_instance_mount tim on ti.namespace like concat(tim.namespace,'%')
      inner join t01_system_datasource t2 on tim.datasource_id = t2.datasource_id
      <if test="datasourceId != null">
          and t2.datasource_id = #{datasourceId}
      </if>
      <where>
          <if test="instanceCode!=null and instanceCode!=''">
              <bind name="instanceCodeLike" value=" '%' + instanceCode.trim() + '%' "/>
              <choose>
                  <when test="_databaseId == 'dm' or _databaseId == 'oracle' or _databaseId == 'kingbase'">
                      and upper(ti.instance_code) like upper(#{instanceCodeLike}) escape '\'
                  </when>
                  <otherwise>
                      and upper(ti.instance_code) like upper(#{instanceCodeLike})
                  </otherwise>
              </choose>
          </if>
          <if test="classifierId != null">
              and ti.classifier_id = #{classifierId}
          </if>
      </where>
  </select>

  <select id="queryDataflowTargetInstanceByPage" resultType="com.wcompass.edgs.modules.md.model.dataflow.DataflowInstanceVO">
      select distinct ti.instance_id ,ti.instance_code ,ti.namespace
      from t01_dataflow_result tdr
      inner join t01_instance ti on tdr.target_id =ti.instance_id
      inner join t01_instance_mount tim on ti.namespace like concat(tim.namespace,'%')
      inner join t01_system_datasource t2 on tim.datasource_id = t2.datasource_id
      <if test="datasourceId != null">
          and t2.datasource_id = #{datasourceId}
      </if>
      where 1=1
      <if test="instanceCode!=null and instanceCode!=''">
          <bind name="instanceCodeLike" value=" '%' + instanceCode.trim() + '%' "/>
          <choose>
              <when test="_databaseId == 'dm' or _databaseId == 'oracle' or _databaseId == 'kingbase'">
                  and upper(ti.instance_code) like upper(#{instanceCodeLike}) escape '\'
              </when>
              <otherwise>
                  and upper(ti.instance_code) like upper(#{instanceCodeLike})
              </otherwise>
          </choose>
      </if>
      <if test="classifierId != null">
          and ti.classifier_id = #{classifierId}
      </if>
  </select>
    <select id="listDataflowSourceObjectClassifier"
            resultType="com.wcompass.edgs.modules.md.model.metadata.ClassifierVO">
        select distinct ti.classifier_id
        from t01_dataflow_result tdr
        inner join t01_instance ti on tdr.source_id =ti.instance_id
        inner join t01_instance_mount tim on ti.namespace like concat(tim.namespace,'%')
        inner join t01_system_datasource t2 on tim.datasource_id = t2.datasource_id
        <if test="datasourceId != null">
            and t2.datasource_id = #{datasourceId}
        </if>
    </select>
    <select id="listDataflowTargetObjectClassifier"
            resultType="com.wcompass.edgs.modules.md.model.metadata.ClassifierVO">
        select distinct ti.classifier_id
        from t01_dataflow_result tdr
        inner join t01_instance ti on tdr.target_id =ti.instance_id
        inner join t01_instance_mount tim on ti.namespace like concat(tim.namespace,'%')
        inner join t01_system_datasource t2 on tim.datasource_id = t2.datasource_id
        <if test="datasourceId != null">
            and t2.datasource_id = #{datasourceId}
        </if>
    </select>
  <select id="selectDataflowResult" resultMap="BaseResultMap">
    select id,
           source_id,
           source_classifier_id,
           target_id,
           target_classifier_id,
           transform_id,
           transform_classifier_id
    from t01_dataflow_result
    where source_id=#{sourceId} and target_id=#{targetId}
    <if test="transformId!=null">
      and transform_id=#{transformId}
    </if>
    <if test="transformId==null">
      and transform_id is null
    </if>
  </select>

  <select id="listDataflowClassifier" resultType="com.wcompass.edgs.modules.md.model.metadata.ClassifierVO">
    select tc.classifier_id as classifierId,tc.classifier_name as classifierName
    from t00_classifier tc
    where 1=1
    <if test="classifierIds!=null">
      and exists ( select 1 from
      <foreach collection="classifierIds" item="classifierId" separator=" union all " open="(" close=")">
        select #{classifierId} as classifier_id from dual
      </foreach>
      temp where temp.classifier_id=tc.classifier_id)
    </if>
  </select>

  <select id="queryDataflowResultETLMap" resultType="com.wcompass.edgs.modules.md.model.dataflow.EdgeVO">
    select source_id,
           target_id,
           transform_id
    from t01_dataflow_result
    where source_id = #{sourceId}
      and target_id = #{targetId}
  </select>

  <select id="queryDataflowResultMap" resultType="com.wcompass.edgs.modules.md.model.dataflow.EdgeVO">
    select source_id,
           target_id,
           transform_id
    from t01_dataflow_result
    where source_id = #{sourceId}
      and target_id = #{targetId}
    <if test="transformId!=null">
      and transform_id = #{transformId}
    </if>
    <if test="transformId==null">
      and transform_id is null
    </if>
  </select>

  <select id="queryDataflowAncestry" resultType="com.wcompass.edgs.modules.md.model.dataflow.DataflowResultMapVO">
    select distinct tdr.source_id as sourceId,tdr.source_classifier_id as sourceClassifierId,ti.instance_code as sourceInstanceCode,
        ti.namespace as sourceNamespace,tdr.target_id as targetId,tdr.target_classifier_id as targetClassifierId,
        ti2.instance_code as targetInstanceCode,ti2.namespace as targetNamespace,tdr.transform_id as transformId,
        tdr.transform_classifier_id as transformClassifierId
    from t01_dataflow_result tdr
    left join t01_instance ti on tdr.source_id =ti.instance_id
    left join t01_instance ti2 on ti2.instance_id =tdr.target_id
    where tdr.target_id =#{targetId}
  </select>

  <select id="queryDataflowInfluence" resultType="com.wcompass.edgs.modules.md.model.dataflow.DataflowResultMapVO">
    select distinct tdr.source_id as sourceId,tdr.source_classifier_id as sourceClassifierId,ti.instance_code as sourceInstanceCode,
        ti.namespace as sourceNamespace,tdr.target_id as targetId,tdr.target_classifier_id as targetClassifierId,
        ti2.instance_code as targetInstanceCode,ti2.namespace as targetNamespace,tdr.transform_id as transformId,
        tdr.transform_classifier_id as transformClassifierId
    from t01_dataflow_result tdr
    left join t01_instance ti on tdr.source_id =ti.instance_id
    left join t01_instance ti2 on ti2.instance_id =tdr.target_id
    where tdr.source_id =#{sourceId}
  </select>

  <select id="listDataflowResult" resultMap="BaseResultMap">
      <choose>
          <when test="_databaseId == 'dm' or _databaseId == 'oracle' or _databaseId == 'kingbase'">
              select *
              from (
                  select temp.*, rownum as page_row_id
                  from (
                      select t1.source_id,
                      t1.source_classifier_id,
                      t1.target_id,
                      t1.target_classifier_id,
                      t1.transform_classifier_id,
                      t1.transform_id
                      from t01_dataflow_result t1
                      inner join (
                      select distinct source_classifier_id, target_classifier_id
                      from t00_relation_dataflow
                      where dataflow_level = #{level}
                      ) t2
                      on t1.source_classifier_id = t2.source_classifier_id
                      and t1.target_classifier_id = t2.target_classifier_id
                  ) temp
              ) t
              where (${startRow} + ${rows}) >= t.page_row_id and t.page_row_id > #{startRow}
          </when>
          <otherwise>
              select t1.source_id,
              t1.source_classifier_id,
              t1.target_id,
              t1.target_classifier_id,
              t1.transform_classifier_id,
              t1.transform_id
              from t01_dataflow_result t1
              inner join (
              select distinct source_classifier_id, target_classifier_id
              from t00_relation_dataflow
              where dataflow_level = #{level}
              ) t2
              on t1.source_classifier_id = t2.source_classifier_id
              and t1.target_classifier_id = t2.target_classifier_id
              limit #{startRow}, #{rows}
          </otherwise>
      </choose>
  </select>
    <select id="queryDataflowByTransform"
            resultType="com.wcompass.edgs.modules.md.model.dataflow.EdgeVO">
      select source_id,
             target_id,
             transform_id
      from t01_dataflow_result
      where transform_id = #{transformId}
      and source_classifier_id in <foreach collection="searchClassifiers" item="searchClassifier" open="(" separator=", " close=")">
                                        #{searchClassifier}
                                  </foreach>
      and target_classifier_id in <foreach collection="searchClassifiers" item="searchClassifier" open="(" separator=", " close=")">
                                        #{searchClassifier}
                                  </foreach>
    </select>
  <select id="queryDataflowBackward" resultType="com.wcompass.edgs.modules.md.model.dataflow.EdgeVO">
      select t1.source_id,
             t1.target_id,
             t1.transform_id
      from t01_dataflow_result t1
      where exists (
            select 0 from (
                <foreach collection="sourceIds" item="sourceId" separator=" union all ">
                  select #{sourceId} as source_id from dual
                </foreach>
            ) t2
            inner join t01_instance t3
            on t3.instance_id = t2.source_id
            inner join t00_relation_dataflow t4
            on t4.target_classifier_id = t3.classifier_id
            where t2.source_id = t1.target_id
            and t4.source_classifier_id = t1.source_classifier_id
       )
  </select>
  <select id="queryDataflowForward" resultType="com.wcompass.edgs.modules.md.model.dataflow.EdgeVO">
      select t1.source_id,
             t1.target_id,
             t1.transform_id
      from t01_dataflow_result t1
      where exists (
      select 0 from (
            <foreach collection="targetIds" item="targetId" separator=" union all ">
              select #{targetId} as target_id from dual
            </foreach>
            ) t2
            inner join t01_instance t3
            on t3.instance_id = t2.target_id
            inner join t00_relation_dataflow t4
            on t4.source_classifier_id = t3.classifier_id
            where t2.target_id = t1.source_id
            and t4.target_classifier_id = t1.target_classifier_id
      )
  </select>
    <select id="getById" resultType="com.wcompass.edgs.modules.md.entity.DataflowResult">
        select id,
               source_id,
               target_id,
               transform_id
        from t01_dataflow_result
        where id = #{id}
    </select>

    <select id="getDataflowThreshold" resultType="java.lang.String">
        select t1.param_value
        from t99_system_param t1
        where t1.param_name = 'dataflow_threshold'
    </select>

    <select id="selectDataflowResultByNodeId" resultType="com.wcompass.edgs.modules.md.entity.DataflowResult">
        select t1.source_id,
               t1.target_id
        from t01_dataflow_result t1
        where t1.source_id = #{instanceId}
           or t1.target_id = #{instanceId}
    </select>

  <select id="metadataRelationBytargetId" resultMap="BaseResultMap">
      <bind name="targetNamespaceLike" value=" targetNamespace.trim() + '%' "/>
      select
      t1.source_id,
      t1.source_classifier_id,
      t1.target_id,
      t1.target_classifier_id,
      t1.transform_classifier_id,
      t1.transform_id
      from  t01_dataflow_result t1 inner join t01_instance t2 on t1.target_id = t2 .instance_id
      where t2.namespace like #{targetNamespaceLike}
    </select>

  <select id="getDatasourceName" resultType="java.lang.String">
      select t2.datasource_name from t01_instance t1 inner join t99_datasource t2 on t1.instance_code = t2.datasource_id
      where t1.instance_id = #{instanceId}
    </select>
    <select id="existDataflowResult" resultType="java.lang.Boolean">
        select case when count(0) > 0 then 1 else 0 end
        from t01_dataflow_result
        where source_id = #{sourceId}
          and target_id = #{targetId}
    </select>
    <select id="judgeDatasource" resultType="java.lang.Boolean">
        select case when count(t2.res_id) > 0 then 1 else 0 end
        from t99_role_user t1
                 inner join t99_role_resource t2
                    on t1.role_id = t2.role_id
        where t2.res_type = 'datasource'
          <if test="userId != null and userId.trim() != ''">
              and t1.user_id = #{userId}
          </if>
          and t2.res_id = #{datasourceId}
    </select>

    <select id="queryDataflowSutureBySessionIdAndAdd"
            resultType="com.wcompass.edgs.modules.md.entity.DataflowResult">
        select t1.source_id,
               t1.source_classifier_id,
               t1.target_id,
               t1.target_classifier_id,
               t1.transform_id,
               t1.transform_classifier_id
        from t01_dataflow_suture t1
        where t1.session_id = #{sessionId}
          and t1.suture_type  = '1' and t1.status = '1'
    </select>

  <select id="queryAllDataflowByTransformId" resultType="com.wcompass.edgs.modules.md.model.dataflow.EdgeVO">
      select t1.source_id,
             t1.target_id,
             t1.transform_id
      from t01_dataflow_result t1
               inner join t00_relation_dataflow t2 on t1.source_classifier_id = t2.source_classifier_id and
                                                      t1.target_classifier_id = t2.target_classifier_id and
                                                      t1.transform_classifier_id = t2.transform_classifier_id
      where t1.transform_id = #{transformId}
        and t2.dataflow_level = #{level}
  </select>
    <select id="getDatasourceNamespace" resultType="java.lang.String">
        select distinct
            t1.namespace
        from t01_instance t1
                 inner join t01_system_datasource t2
                            on t1.instance_code = t2.datasource_id
                                and t1.classifier_id = 'Root'
                                and t1.parent_id = 0
        <where>
            <if test="ids != null">
                and t2.system_id in
                <foreach collection="ids" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getFlowNodeByParent" resultType="com.wcompass.edgs.modules.md.model.datamap.NodeVO">
        select distinct
        t.instance_id,
        t.code,
        t.label,
        t.classifier_id,
        t.namespace
        from (
        select
        t2.instance_id,
        t2.instance_code as code,
        case when t3.instance_name is not null and t3.instance_name != '' then t3.instance_name else t2.instance_name end as label,
        t2.classifier_id,
        t2.namespace
        from t01_dataflow_result t1
        inner join t01_instance t2
        on t1.target_id = t2.instance_id
        left join  t01_instance_his t3
        on t2.instance_id = t3.instance_id
        and t3.version = 0
        where t2.parent_id = #{parentId}
        union
        select
        t2.instance_id,
        t2.instance_code,
        case when t3.instance_name is not null and t3.instance_name != '' then t3.instance_name else t2.instance_name end as label,
        t2.classifier_id,
        t2.namespace
        from t01_dataflow_result t1
        inner join t01_instance t2
        on t1.source_id = t2.instance_id
        left join  t01_instance_his t3
        on t2.instance_id = t3.instance_id
        and t3.version = 0
        where t2.parent_id = #{parentId}
        ) t
        <where>
            <if test="keyword != null">
                <choose>
                    <when test="_databaseId == 'dm' or _databaseId == 'oracle' or _databaseId == 'kingbase'">
                        and (
                        upper(t.code) like upper(#{keyword}) escape '\' or
                        upper(t.label) like upper(#{keyword}) escape '\'
                        )
                    </when>
                    <otherwise>
                        and (
                        upper(t.code) like upper(#{keyword})  or
                        upper(t.label) like upper(#{keyword})
                        )
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>
    <select id="getFlowNodeBySource" resultType="com.wcompass.edgs.modules.md.model.FlowAiVO">
        select  distinct t1.target_id as instanceId,t2.instance_code,t2.instance_name, t3.classifier_name as classifierCode
        from t01_dataflow_result t1
                 inner join t01_instance t2 on t1.target_id = t2.instance_id
                 inner join t00_classifier t3 on t1.target_classifier_id = t3.classifier_id
        where t1.source_id = #{sourceId}
        <if test="classifierIds != null and classifierIds.size() > 0">
            and t1.target_classifier_id  in
            <foreach collection="classifierIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="getFlowNodeByTarget" resultType="com.wcompass.edgs.modules.md.model.FlowAiVO">
        select distinct t1.source_id as instanceId,t2.instance_code,t2.instance_name, t3.classifier_name as classifierCode
        from t01_dataflow_result t1
                 inner join t01_instance t2 on t1.source_id = t2.instance_id
                 inner join t00_classifier t3 on t1.source_classifier_id = t3.classifier_id
        where t1.target_id = #{targetId}
        <if test="classifierIds != null and classifierIds.size() > 0">
            and t1.source_classifier_id  in
            <foreach collection="classifierIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>
