<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wcompass.edgs.modules.md.dao.read.ReportViewLogReadMapper">
    <select id="list" resultType="com.wcompass.edgs.modules.md.model.report.ReportViewLogVO">
        select t1.id, t1.user_id, t3.user_name, t1.ip, t2.report_type, t2.report_name, t1.view_time
        from t15_report_view_log t1
                 inner join t15_report t2 on t1.report_id = t2.id
                 inner join t99_user t3 on t1.user_id = t3.user_id
        <where>
            <if test="startTime != null">
                t1.view_time >= #{startTime}
            </if>
            <if test="endTime != null">
                and #{endTime} >= t1.view_time
            </if>
            <if test="reportType != null and reportType != ''">
                and t2.report_type = #{reportType}
            </if>
            <if test="userId != null and userId != ''">
                and t1.user_id = #{userId}
            </if>
            <if test="reportName != null and reportName != ''">
                <bind name="reportName" value="'%' + reportName.trim() + '%'"/>
                <choose>
                    <when test="_databaseId == 'dm' or _databaseId == 'oracle'">
                        and  upper(t2.report_name) like upper(#{reportName}) escape '\'
                    </when>
                    <otherwise>
                        and  upper(t2.report_name) like upper(#{reportName})
                    </otherwise>
                </choose>
            </if>
        </where>
        order by t1.view_time desc,t2.report_type
    </select>

    <select id="getAllDeptName" resultType="java.lang.String">
        select t2.dept_name
        from t99_dept_user t1
                 inner join t99_dept t2 on t1.dept_id = t2.dept_id
        where t1.user_id = #{userId}
    </select>

    <select id="getUserName" resultType="com.wcompass.edgs.modules.md.model.report.UserVO">
        select distinct t1.user_id, t2.user_name
        from t15_report_view_log t1
                 inner join t99_user t2 on t1.user_id = t2.user_id
    </select>

    <select id="getQueryNum" resultType="java.lang.Integer">
        select count(*)
        from t15_report_view_log
        where view_time >= #{startDate} and #{endDate} >= view_time
    </select>
    <select id="getUserNum" resultType="java.lang.Integer">
        select count(distinct user_id)
        from t15_report_view_log
        where view_time >= #{startDate} and #{endDate} >= view_time
    </select>
    <select id="getReportTop" resultType="com.wcompass.edgs.modules.md.model.report.statistics.ReportTopVO">
        select
            t1.report_id,
            t2.report_name,
            count(t1.report_id) as query_num
        from t15_report_view_log t1
                 inner join t15_report t2
                            on t1.report_id = t2.id
        where t1.view_time >= #{startDate} and #{endDate} >= t1.view_time
        group by t1.report_id , t2.report_name
        order by query_num desc
    </select>
    <select id="getUserTop" resultType="com.wcompass.edgs.modules.md.model.report.statistics.UserTopVO">
        select
            t1.user_id,
            t2.user_name,
            count(t1.user_id) as query_num
        from t15_report_view_log t1
                 inner join t99_user t2 on t1.user_id = t2.user_id
        where t1.view_time >= #{startDate} and #{endDate} >= t1.view_time
        group by t1.user_id ,t2.user_name
        order by query_num desc
    </select>
    <select id="getStatisics" resultType="java.lang.Integer">
        select count(t1.report_id) as num
        from t15_report_view_log t1
            inner join t15_report t2
        on t1.report_id = t2.id
        where t1.view_time >= #{startDate} and #{endDate} > t1.view_time
        and t2.report_type = #{reportType}
    </select>
</mapper>