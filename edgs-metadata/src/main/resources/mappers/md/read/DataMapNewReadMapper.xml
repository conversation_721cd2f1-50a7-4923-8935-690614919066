<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wcompass.edgs.modules.md.dao.read.DataMapNewReadMapper">

    <select id="getDataInfo" resultType="com.wcompass.edgs.core.security.BaseInfo">
        select
        t1.instance_id,
        t1.instance_code,
        t1.instance_name,
        t1.classifier_id,
        t1.namespace,
        t1.parent_id
        from t01_instance t1
        where t1.instance_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="getDatasourceInfo" resultType="com.wcompass.edgs.core.security.BaseInfo">
        select distinct t1.instance_id,
        t1.instance_code,
        t3.datasource_name as instance_name,
        t1.classifier_id,
        t1.namespace,
        t1.parent_id
        from t01_instance t1
        inner join t01_system_datasource t2
        on t1.instance_code = t2.datasource_id
        and t1.classifier_id = 'Root'
        and t1.parent_id = 0
        inner join t99_datasource t3
        on t2.datasource_id = t3.datasource_id
        inner join t99_role_resource t4 on t3.datasource_id = t4.res_id and t4.res_type = 'datasource'
        inner join t99_role_user t5 on t4.role_id = t5.role_id
        where t2.system_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="userId != null">
            and t5.user_id = #{userId}
        </if>
    </select>
    <select id="getSystemByDatasource" resultType="com.wcompass.edgs.core.security.BaseInfo">
        select
            t1.instance_id,
            t1.instance_code,
            t1.instance_name,
            t1.classifier_id,
            t1.namespace,
            t1.parent_id
        from t01_instance t1
                 inner join t01_system_datasource t2
                            on t1.instance_id = t2.system_id
        where t2.datasource_id = #{datasourceId}
    </select>

    <select id="queryDataflowInfluence" resultType="com.wcompass.edgs.modules.md.model.datamap.DataResultVO">
        select distinct tdr.source_id      ,
        tdr.source_classifier_id    ,
        tdr.target_id               ,
        tdr.target_classifier_id
        from t01_dataflow_result tdr
        where tdr.source_id =#{sourceId}
        and tdr.target_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="queryDataflowByNode" resultType="com.wcompass.edgs.modules.md.model.datamap.DataResultVO">
        select distinct
               t1.id,
        t1.source_id,
        t2.instance_code as source_code,
        t2.instance_name as source_name,
        t2.parent_id     as source_parent_id,
        t2.namespace     as source_namespace,
        t1.source_classifier_id,
        t1.target_id,
        t3.instance_code as target_code,
        t3.instance_name as target_name,
        t3.parent_id     as target_parent_id,
        t3.namespace     as target_namespace,
        t1.target_classifier_id,
        t1.schema_id
        from t01_dataflow_result t1
        inner join t01_instance t2 on t1.source_id = t2.instance_id
        <if test="classifierIds != null and classifierIds.size() > 0">
            and t2.classifier_id in
            <foreach collection="classifierIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        inner join t01_instance t3 on t1.target_id = t3.instance_id
        <if test="classifierIds != null and classifierIds.size() > 0">
            and t3.classifier_id in
            <foreach collection="classifierIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        where
        <foreach collection="namespaces" item="namespace" separator=" or " open="(" close=")">
            t2.namespace like #{namespace}
        </foreach>
        and
        <foreach collection="namespaces" item="namespace" separator=" or " open="(" close=")">
            t3.namespace like #{namespace}
        </foreach>
        <if test="nodeId != null">
            and (t2.parent_id = #{nodeId} or t3.parent_id = #{nodeId})
        </if>
        <if test="classifierIds == null or classifierIds.size() == 0">
            and 1=2
        </if>
    </select>


    <select id="getRelationDataflow"
            resultType="com.wcompass.edgs.modules.md.model.datamap.RelationDataflowVO">
        select
            t1.source_classifier_id,
            t2.classifier_name as source_classifier_name,
            t1.target_classifier_id,
            t3.classifier_name as target_classifier_name,
            t1.transform_classifier_id,
            t1.dataflow_level
        from t00_relation_dataflow t1
                 inner join t00_classifier t2
                            on t1.source_classifier_id = t2.classifier_id
                 inner join t00_classifier t3
                            on t1.target_classifier_id = t3.classifier_id
    </select>

    <select id="queryDataflow" resultType="com.wcompass.edgs.modules.md.model.datamap.DataResultVO">
        select
               distinct
               t1.id,
        t1.source_id,
        t2.instance_code as source_code,
        t2.instance_name as source_name,
        t2.parent_id     as source_parent_id,
        t2.namespace     as source_namespace,
        t1.source_classifier_id,
        t1.target_id,
        t3.instance_code as target_code,
        t3.instance_name as target_name,
        t3.parent_id     as target_parent_id,
        t3.namespace     as target_namespace,
        t1.target_classifier_id,
        t1.schema_id,
        t1.transform_id
        from t01_dataflow_result t1
        inner join t01_instance t2 on t1.source_id = t2.instance_id
        <if test="classifierIds != null and classifierIds.size() > 0">
            and t2.classifier_id in
            <foreach collection="classifierIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        inner join t01_instance t3 on t1.target_id = t3.instance_id
        <if test="classifierIds != null and classifierIds.size() > 0">
            and t3.classifier_id in
            <foreach collection="classifierIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        where
        <foreach collection="namespaces" item="namespace" separator=" or " open="(" close=")">
            t2.namespace like #{namespace}
        </foreach>
        and
        <foreach collection="namespaces" item="namespace" separator=" or " open="(" close=")">
            t3.namespace like #{namespace}
        </foreach>
        <if test="sourceNodeIds != null and sourceNodeIds.size() > 0">
            and t2.instance_id
            in
            <foreach collection="sourceNodeIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="targetNodeIds != null and targetNodeIds.size() > 0">
            and t3.instance_id
            in
            <foreach collection="targetNodeIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="classifierIds == null or classifierIds.size() == 0">
            and 1=2
        </if>
    </select>

    <select id="queryFlow" resultType="com.wcompass.edgs.modules.md.model.datamap.DataResultVO">
        select
        distinct
        t1.source_id,
        t1.target_id,
        t1.transform_id
        from t01_dataflow_result t1
        inner join t01_instance t2 on t1.source_id = t2.instance_id
        <if test="classifierIds != null and classifierIds.size() > 0">
            and t2.classifier_id in
            <foreach collection="classifierIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        inner join t01_instance t3 on t1.target_id = t3.instance_id
        <if test="classifierIds != null and classifierIds.size() > 0">
            and t3.classifier_id in
            <foreach collection="classifierIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        where
        <foreach collection="namespaces" item="namespace" separator=" or " open="(" close=")">
            t2.namespace like #{namespace}
        </foreach>
        and
        <foreach collection="namespaces" item="namespace" separator=" or " open="(" close=")">
            t3.namespace like #{namespace}
        </foreach>
        <if test="sourceNodeIds != null and sourceNodeIds.size() > 0">
            and t2.instance_id
            in
            <foreach collection="sourceNodeIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="targetNodeIds != null and targetNodeIds.size() > 0">
            and t3.instance_id
            in
            <foreach collection="targetNodeIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="classifierIds == null or classifierIds.size() == 0">
            and 1=2
        </if>
    </select>

    <select id="queryMoreFlow" resultType="com.wcompass.edgs.modules.md.model.datamap.DataResultVO">
        select
        distinct
        t1.source_id,
        t1.target_id,
        t1.transform_id
        from t01_dataflow_result t1
        inner join t01_instance t2 on t1.source_id = t2.instance_id
        <if test="classifierIds != null and classifierIds.size() > 0">
            and t2.classifier_id in
            <foreach collection="classifierIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        inner join t01_instance t3 on t1.target_id = t3.instance_id
        <if test="classifierIds != null and classifierIds.size() > 0">
            and t3.classifier_id in
            <foreach collection="classifierIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        where
        <foreach collection="namespaces" item="namespace" separator=" or " open="(" close=")">
            t2.namespace like #{namespace}
        </foreach>
        and
        <foreach collection="namespaces" item="namespace" separator=" or " open="(" close=")">
            t3.namespace like #{namespace}
        </foreach>
        <if test="isSource">
            and t3.instance_id
            in
            <foreach collection="ids" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="!isSource">
            and t2.instance_id
            in
            <foreach collection="ids" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="classifierIds == null or classifierIds.size() == 0">
            and 1=2
        </if>
    </select>

    <select id="getSnapshotList" resultType="com.wcompass.edgs.modules.md.model.datamap.DataMapSnapshotVO">
        select
            distinct
            t.id,
            t6.snapshot_name,
            t6.snapshot_desc,
            t6.create_time,
            t6.creator_id,
            t8.user_name as creator_name,
            case when t7.snapshot_id is not null then 'Y' else 'N' end as default_snapshot,
            case when t8.user_id = #{userId} then 'Y' else 'N' end as is_creator
        from (select t1.snapshot_id as id
              from t01_datamap_snapshot_share t1
              where t1.share_type = '1'
                and t1.share_id = #{userId}
              union all
              select t1.snapshot_id as id
              from t01_datamap_snapshot_share t1
              where t1.share_type = '2'
                and t1.share_id in (select role_id
                                    from t99_role_user
                                    where user_id = #{userId})
              union all
              select t1.snapshot_id as id
              from t01_datamap_snapshot_share t1
              where t1.share_type = '0'
                and t1.share_id in (select dept_id
                                    from t99_dept_user
                                    where user_id = #{userId})

              union all
              select t1.id as id
              from t01_datamap_snapshot t1
              where t1.creator_id = #{userId}
             ) t
                 inner join t01_datamap_snapshot t6
                            on t.id = t6.id
                 left join t01_datamap_default_snapshot t7
                           on t6.id = t7.snapshot_id
                               and t7.user_id = #{userId}
                 inner join t99_user t8
                            on t6.creator_id = t8.user_id
        order by t6.create_time desc
    </select>
    <select id="getSnapshotData" resultType="java.lang.String">
        select snapshot_data
        from t01_datamap_snapshot
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
        </where>
    </select>

    <select id="getNamespaceByUser" resultType="java.lang.String">
        select
        distinct
        t9.namespace
        from t99_datasource t1
        inner join t99_role_resource t2 on t1.datasource_id = t2.res_id and t2.res_type = 'datasource'
        inner join t99_role_user t3 on t2.role_id = t3.role_id
        inner join t99_datasource_adapter_mode t4 on t1.mode_id = t4.mode_id
        inner join t99_datasource_adapter t5 on t4.adapter_id = t5.adapter_id
        inner join t01_system_datasource t6 on t1.datasource_id = t6.datasource_id
        inner join t01_instance t9
        on t9.parent_id = 0
        and t9.classifier_id = 'Root'
        and t9.instance_code = t1.datasource_id
        <where>
            <if test="userId != null and userId != ''">
                t3.user_id = #{userId}
            </if>
        </where>
    </select>
    <select id="getSnapshot" resultType="java.lang.String">
        select t2.user_name
        from t01_datamap_snapshot t1
        inner join t99_user t2
        on t1.creator_id = t2.user_id
        where t1.snapshot_name = #{keyword}
        <if test="id != null">
            and t1.id != #{id}
        </if>
    </select>
    <select id="getColumnEdge" resultType="com.wcompass.edgs.modules.md.model.dataflow.EdgeVO">
        select
            distinct
            t1.source_id,t1.target_id,t1.transform_id
        from t01_dataflow_result t1
        where t1.source_id in
              (
                  select t.instance_id
                  from t01_instance t
                  where t.parent_id = #{sourceId}
                    and exists (
                          select 0 from t01_dataflow_result t2
                          where t.instance_id = t2.source_id
                      )
              ) and t1.target_id in
                    (
                        select t4.instance_id
                        from t01_instance t4
                        where t4.parent_id = #{targetId}
                          and exists (
                                select 0 from t01_dataflow_result t3
                                where  t4.instance_id = t3.target_id
                            ))
    </select>
    <select id="judgeFlowLevel" resultType="java.lang.Integer">
        select
            distinct
            dataflow_level
        from t00_relation_dataflow
        where target_classifier_id = #{classifierId} or source_classifier_id = #{classifierId}
    </select>
    <select id="queryMoreDataflow" resultType="com.wcompass.edgs.modules.md.model.datamap.DataResultVO">
        select
        distinct
        t1.id,
        t1.source_id,
        t2.instance_code as source_code,
        t2.instance_name as source_name,
        t2.parent_id     as source_parent_id,
        t2.namespace     as source_namespace,
        t1.source_classifier_id,
        t1.target_id,
        t3.instance_code as target_code,
        t3.instance_name as target_name,
        t3.parent_id     as target_parent_id,
        t3.namespace     as target_namespace,
        t1.target_classifier_id,
        t1.schema_id,
        t1.transform_id
        from t01_dataflow_result t1
        inner join t01_instance t2 on t1.source_id = t2.instance_id
        <if test="classifierIds != null and classifierIds.size() > 0">
            and t2.classifier_id in
            <foreach collection="classifierIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        inner join t01_instance t3 on t1.target_id = t3.instance_id
        <if test="classifierIds != null and classifierIds.size() > 0">
            and t3.classifier_id in
            <foreach collection="classifierIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        where
        <foreach collection="namespaces" item="namespace" separator=" or " open="(" close=")">
            t2.namespace like #{namespace}
        </foreach>
        and
        <foreach collection="namespaces" item="namespace" separator=" or " open="(" close=")">
            t3.namespace like #{namespace}
        </foreach>
        <if test="isSource">
            and t3.instance_id
            in
            <foreach collection="ids" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="!isSource">
            and t2.instance_id
            in
            <foreach collection="ids" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="classifierIds == null or classifierIds.size() == 0">
            and 1=2
        </if>
    </select>
    <select id="getFlowIds" resultType="java.lang.Integer">
        select t4.id
        from t01_instance t2
                 INNER JOIN
             t01_dataflow_result t4 ON t4.source_id = t2.instance_id
        where t2.parent_id = #{nodeId}
          and t2.classifier_id in
              <foreach collection="classifierIds" item="classifierId" separator="," open="(" close=")">
                  #{classifierId}
              </foreach>
          and
              <foreach collection="namespaces" item="namespace" separator=" or " open="(" close=")">
                  t2.namespace LIKE #{namespace}
              </foreach>
        union
        select t4.id
        from t01_instance t2
                 INNER JOIN
             t01_dataflow_result t4 ON t4.target_id = t2.instance_id
        where t2.parent_id = #{nodeId}
        and t2.classifier_id in
        <foreach collection="classifierIds" item="classifierId" separator="," open="(" close=")">
            #{classifierId}
        </foreach>
        and
        <foreach collection="namespaces" item="namespace" separator=" or " open="(" close=")">
            t2.namespace LIKE #{namespace}
        </foreach>
    </select>
    <select id="getFlowById" resultType="com.wcompass.edgs.modules.md.model.datamap.DataResultVO">
        select id,
               source_id,
               source_classifier_id,
               target_id,
               target_classifier_id,
               schema_id
        from t01_dataflow_result t1
        where t1.id = #{id}
    </select>
    <select id="getAllFlows" resultType="com.wcompass.edgs.modules.md.model.datamap.DataResultVO">
        select t.id,
        t.source_id,
        t.source_classifier_id,
        t.target_id,
        t.target_classifier_id
        from t01_dataflow_result t
        where exists(
            select *
            from (
                select t4.id
                from t01_instance t2
                INNER JOIN
                t01_dataflow_result t4 ON t4.source_id = t2.instance_id
                <where>
                    <if test="nodeIds != null and nodeIds.size() > 0">
                        and t2.parent_id in
                        <foreach collection="nodeIds" item="nodeId" open="(" close=")" separator=",">
                            #{nodeId}
                        </foreach>
                    </if>
                    and t2.classifier_id in
                    <foreach collection="classifierIds" item="classifierId" separator="," open="(" close=")">
                        #{classifierId}
                    </foreach>
                    and
                    <foreach collection="namespaces" item="namespace" separator=" or " open="(" close=")">
                        t2.namespace LIKE #{namespace}
                    </foreach>
                </where>
                union
                select t4.id
                from t01_instance t2
                INNER JOIN
                t01_dataflow_result t4 ON t4.target_id = t2.instance_id
                <where>
                    <if test="nodeIds != null and nodeIds.size() > 0">
                        and t2.parent_id in
                        <foreach collection="nodeIds" item="nodeId" open="(" close=")" separator=",">
                            #{nodeId}
                        </foreach>
                    </if>
                    and t2.classifier_id in
                    <foreach collection="classifierIds" item="classifierId" separator="," open="(" close=")">
                        #{classifierId}
                    </foreach>
                    and
                    <foreach collection="namespaces" item="namespace" separator=" or " open="(" close=")">
                        t2.namespace LIKE #{namespace}
                    </foreach>
                </where>
            )temp
        where t.id = temp.id
        )
    </select>
    <select id="getNodeName" resultType="java.lang.String">
        select
            case when t2.instance_name is not null and t2.instance_name != '' then t2.instance_name else t1.instance_name end as instance_name
        from t01_instance t1
                 left join t01_instance_his t2
                           on t1.instance_id = t2.instance_id
                               and t2.version = 0
        where t1.instance_id = #{id}
    </select>
    <select id="getSnapshotShare" resultType="com.wcompass.edgs.modules.md.model.datamap.SnapshotShareVO">
        select snapshot_id, share_type, share_id
        from t01_datamap_snapshot_share
        where snapshot_id = #{snapshotId}
    </select>
    <select id="getShareUser" resultType="java.lang.String">
        select distinct user_id
        from (
                 select t2.user_id
                 from t01_datamap_snapshot_share t1
                          inner join t99_user t2
                                     on t1.share_id  = t2.user_id
                                         and t1.snapshot_id = #{snapshotId}
                                         and share_type = '1'
                 union all
                 select t2.user_id
                 from t01_datamap_snapshot_share t1
                          inner join t99_role_user t2
                                     on t1.share_id  = t2.role_id
                                         and t1.snapshot_id = #{snapshotId}
                                         and share_type = '2'
                 union all
                 select t2.user_id
                 from t01_datamap_snapshot_share t1
                          inner join t99_dept_user t2
                                     on t1.share_id  = t2.dept_id
                                         and t1.snapshot_id = #{snapshotId}
                                         and share_type = '0'
             )t
    </select>
    <select id="getDefaultUser" resultType="java.lang.String">
        select user_id
        from t01_datamap_default_snapshot
        where snapshot_id = #{snapshotId}
    </select>
    <select id="getDefaultSnapshotData" resultType="java.lang.String">
        select t2.snapshot_data
        from t01_datamap_default_snapshot t1
                 inner join t01_datamap_snapshot t2
                            on t1.snapshot_id = t2.id
        where t1.user_id = #{userId}
    </select>
    <select id="getFlow" resultType="java.lang.String">
        select
            <if test="isSource">
                source_id
            </if>
            <if test="!isSource">
                target_id
            </if>
        from t01_dataflow_result
        where
        <if test="isSource">
            target_id
        </if>
        <if test="!isSource">
            source_id
        </if>
        in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

</mapper>
