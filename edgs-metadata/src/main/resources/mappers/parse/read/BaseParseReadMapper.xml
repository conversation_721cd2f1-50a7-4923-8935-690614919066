<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wcompass.edgs.modules.parse.dao.read.BaseParseReadMapper">
    <select id="querySchemaByDatasourceId" resultType="java.lang.String">
        select t2.instance_id
        from t01_instance t1
                 inner join t01_instance t2 on t1.instance_id = t2.parent_id
        where t1.classifier_id = 'Root'
          and t1.instance_code = #{datasourceId}
    </select>

    <select id="selectInstanceCodeById" resultType="java.lang.String">
        select instance_code
        from t01_instance
        where instance_id = #{instanceId}
    </select>

    <select id="queryColumnByDbId" resultType="com.wcompass.edgs.modules.parse.model.base.ColumnInfoVO">
        select #{datasourceId}                                                             as datasourceId,
               t1.instance_code                                                            AS SCHEMA_CODE,
               t2.instance_code                                                            as TABLE_CODE,
               t2.instance_name                                                            as TABLE_COMMENT,
               t2.classifier_id                                                            as CLASSIFIER_ID,
               t2.instance_id                                                              as TABLE_ID,
               t3.instance_id                                                              as COLUMN_ID,
               t3.instance_code                                                            as COLUMN_CODE,
               t3.instance_name                                                            as COLUMN_NAME,
               t3.string_4                                                                 as COLUMN_SEQ,
               t3.classifier_id                                                            as TYPE_NAME,
               concat('/', t1.instance_code, '/', t2.instance_code, '/', t3.instance_code) AS COLUMN_CODE_PATH,
               concat('/', t1.instance_code, '/', t2.instance_code)                        AS TABLE_CODE_PATH
        from t01_instance t1
                 inner join t01_instance t2
                            on t2.parent_id = t1.instance_id
                                and t2.classifier_id in ('Table', 'View')
                 inner join t01_instance t3
                            on t3.parent_id = t2.instance_id
                                and t3.classifier_id = 'Column'
        where exists(select 0
                     from t01_instance t4
                     where t4.classifier_id = 'Root'
                       and t4.instance_code = #{datasourceId} -- 数据源的id
                       and t1.namespace like concat(t4.namespace, '/%'))
        order by t1.instance_code, t2.instance_code
    </select>

    <select id="queryInstanceNamespaceById" resultType="java.lang.String">
        select t1.NAMESPACE
        from T01_INSTANCE t1
        where t1.INSTANCE_ID = #{instanceId}
    </select>

    <select id="queryFeatureColByAttCode" resultType="java.lang.String">
        select t2.att_store
        from t00_feature t1
                 inner join t00_feature_col t2 on t1.att_id = t2.att_id and t1.classifier_id = t2.classifier_id
        where t1.att_code = #{attCode}
          and t1.classifier_id = #{classifierId}
    </select>

    <select id="getBaseInfo" resultType="com.wcompass.edgs.core.security.BaseInfo">
        select t1.instance_id,
               t1.instance_code,
               t1.instance_name,
               t1.classifier_id,
               t1.namespace,
               t1.parent_id,
               t1.start_time,
               t1.rel_type
        from t01_instance t1
        where t1.instance_id = #{instanceId}
    </select>
</mapper>