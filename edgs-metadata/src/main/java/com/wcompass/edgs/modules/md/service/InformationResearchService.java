package com.wcompass.edgs.modules.md.service;

import com.wcompass.edgs.cloud.api.client.model.ProgressDTO;
import com.wcompass.edgs.modules.md.model.InformationResearchGangedVO;
import com.wcompass.edgs.modules.mm.entity.internal.Classifier;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-11-30 15:06
 */
public interface InformationResearchService {

    /**
     * 查询对应用户下的shema树
     * @param userId
     * @return
     */
    List<InformationResearchGangedVO> getSchemaTree(String userId);

    /**
     * 补录模板下载
     */
    void downRecordingTemp();

    String downFillRateData(Map<String, String> paramMap, Integer progressId, String userId);

    String downLevelRateData(Map<String, String> paramMap, Integer progressId, String userId);

    ProgressDTO downInformation(String schemaId, String userId);

    ProgressDTO downDDL(String schemaId, String userId);

    void isSupportedDataSources(String schemaId);

    String downDDL(Integer progressId,String schemaId,String userId);

    void fillClassifier(List<Classifier> surveyClassifiers);
}
