package com.wcompass.edgs.modules.md.service;

import com.wcompass.edgs.core.Page;
import com.wcompass.edgs.modules.md.model.ClassifierCount;
import com.wcompass.edgs.modules.md.model.GlobalSearchScriptItemVo;
import com.wcompass.edgs.modules.md.model.ScriptTypeVo;

import java.util.List;


public interface GlobalSearchScriptService {

    List<GlobalSearchScriptItemVo> searchMetadata(
                                            Page<GlobalSearchScriptItemVo> page,
                                            String keyword,
                                            String userId,
                                            String systemId,
                                            String classifierId,
                                            String scriptTypeId);

    List<GlobalSearchScriptItemVo> getSearchSort(String currentUserId, String classifierId);

    void latelyDataSave(GlobalSearchScriptItemVo GlobalSearchScriptItemVo, String currentUserId);

    List<GlobalSearchScriptItemVo> listLatelyData(String type, String classifierId, String userId);


    ClassifierCount classifierCount(String keyword, String userId, String classifierId,String scriptTypeId);

    List<ScriptTypeVo> listScriptType();
}
