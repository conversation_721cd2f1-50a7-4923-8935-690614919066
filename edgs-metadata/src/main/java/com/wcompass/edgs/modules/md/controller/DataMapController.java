package com.wcompass.edgs.modules.md.controller;

import com.wcompass.edgs.core.AjaxResponseWrapper;
import com.wcompass.edgs.core.BaseController;
import com.wcompass.edgs.modules.md.model.datamap.*;
import com.wcompass.edgs.modules.md.service.DataMapService;
import com.wcompass.edgs.core.extract.MetaModel;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.hibernate.validator.constraints.Range;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/5/19 14:27
 * @Version 1.0
 */
@RestController
@RequestMapping("/dataMap/")
@Tag(name = "数据地图")
public class DataMapController extends BaseController {

    @Resource
    private DataMapService dataMapService;

    @GetMapping("/listSystem")
    @Operation(summary = "系统列表")
    public AjaxResponseWrapper<DataMapVO> listSystem(@Parameter(description = "系统id集合") @RequestParam(required = false) List<Long> systemIds) {

        return AjaxResponseWrapper.data(dataMapService.dataMap(systemIds, MetaModel.SYSTEM,this.getCurrentAccount()));
    }

    @GetMapping("/listDatabases")
    @Operation(summary = "数据库列表")
    public AjaxResponseWrapper<DataMapVO> listDatabases(@Parameter(description = "系统id集合") @RequestParam(required = false) List<Long> systemIds,
                                                        @Parameter(description = "是否智能排列,true-智能排列，需传入画布各值，false-不排序") @RequestParam(required = false,defaultValue = "0") boolean smartRange,
                                                        @Parameter(description = "小圆半径，推荐：50") @RequestParam(required = false) Integer radius,
                                                        @Parameter(description = "画布宽width") @RequestParam(required = false) Integer width,
                                                        @Parameter(description = "画布高height") @RequestParam(required = false) Integer height) {
        return AjaxResponseWrapper.data(dataMapService.dataMapSmartRange(systemIds, this.getCurrentAccount(),
                smartRange, radius, width, height));
    }

    @GetMapping("/filterCondition")
    @Operation(summary = "过滤条件")
    public AjaxResponseWrapper<List<FilterSystemGroupVO>> filterCondition() {
        return AjaxResponseWrapper.data(dataMapService.filterCondition(getCurrentAccount()));
    }

    @GetMapping("/checkedSystem")
    @Operation(summary = "被选中的系统")
    public AjaxResponseWrapper<List<FilterSystemVO>> checkedSystem() {
        return AjaxResponseWrapper.data(dataMapService.checkedSystem(getCurrentUserId()));
    }

    @PostMapping("/saveFilterCondition")
    @Operation(summary = "保存过滤条件配置")
    public AjaxResponseWrapper<Void> saveFilterCondition(@Parameter(description = "选中的系统id") @RequestBody List<Long> systemIds) {
        dataMapService.saveFilterCondition(systemIds, getCurrentUserId());
        return AjaxResponseWrapper.tip("配置保存成功");
    }

    @PostMapping("/saveNodeCoordinate")
    @Operation(summary = "保存节点坐标")
    public AjaxResponseWrapper<Void> saveNodeCoordinate(@Parameter(description = "节点VO") @RequestBody List<NodeCoordinateVO> coordinates) {
        dataMapService.saveNodeCoordinate(coordinates,this.getCurrentUserId());
        return AjaxResponseWrapper.tip("保存成功！");
    }

    @GetMapping("/metadataRelaition")
    @Operation(summary = "元数据关系")
    public AjaxResponseWrapper<List<FirstDataMap>> metadataRelaition(@NotNull(message = "无效的元数据") @Range(min = 1, message = "无效的元数据") @Parameter(description = "来源元数据") @RequestParam(name = "fromInstanceId") Long fromInstanceId,
                                                                     @NotNull(message = "无效的元数据") @Range(min = 1, message = "无效的元数据") @Parameter(description = "目标元数据") @RequestParam(name = "toInstanceId") Long toInstanceId) {
        return AjaxResponseWrapper.data(dataMapService.metadataRelation(fromInstanceId, toInstanceId));
    }

    @GetMapping("/queryMetadataCode")
    @Operation(summary = "schema->schema可以进行搜索的Code")
    public AjaxResponseWrapper<List<MetaDataCodeVO>> queryMetadataCode(@NotNull(message = "无效的元数据") @Range(min = 1, message = "无效的元数据") @Parameter(description = "来源元数据") @RequestParam(name = "fromInstanceId") Long fromInstanceId,
                                                               @NotNull(message = "无效的元数据") @Range(min = 1, message = "无效的元数据") @Parameter(description = "目标元数据") @RequestParam(name = "toInstanceId") Long toInstanceId,
                                                               @RequestParam(name = "keyword") String keyword) {
        return AjaxResponseWrapper.data(dataMapService.queryMetadataCode(fromInstanceId,toInstanceId,keyword));
    }

    @GetMapping("/queryMetadataRelaitionBySourceId")
    @Operation(summary = "根据instanceId来查询和他关联的所有元数据关系")
    public AjaxResponseWrapper<DataMapVO> queryMetadataRelaitionBySourceId(@NotNull(message = "无效的元数据") @Range(min = 1, message = "无效的元数据") @Parameter(description = "来源元数据") @RequestParam(name = "fromInstanceId") Long fromInstanceId,
                                                                              @NotNull(message = "无效的元数据") @Range(min = 1, message = "无效的元数据") @Parameter(description = "目标元数据") @RequestParam(name = "toInstanceId") Long toInstanceId,
                                                                             @Parameter(description = "点击元数据") @RequestParam(name = "sourceInstanceId",required = false) Long sourceInstanceId
                                                                              ) {
        return AjaxResponseWrapper.data(dataMapService.queryMetadataRelaitionBySourceId(fromInstanceId,toInstanceId,sourceInstanceId));
    }


}
