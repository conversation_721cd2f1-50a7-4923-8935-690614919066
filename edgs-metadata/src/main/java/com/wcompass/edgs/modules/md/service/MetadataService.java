package com.wcompass.edgs.modules.md.service;

import com.wcompass.edgs.cloud.api.client.metadata.model.CloudInstanceDTO;
import com.wcompass.edgs.core.Option;
import com.wcompass.edgs.core.Page;
import com.wcompass.edgs.core.extract.Dependency;
import com.wcompass.edgs.core.extract.Metadata;
import com.wcompass.edgs.core.security.BaseInfo;
import com.wcompass.edgs.core.security.InstanceVO;
import com.wcompass.edgs.job.model.ColumnChangeDetail;
import com.wcompass.edgs.modules.md.entity.AlterationHistory;
import com.wcompass.edgs.modules.md.model.QualityRuleVO;
import com.wcompass.edgs.modules.md.model.StandardVO;
import com.wcompass.edgs.modules.md.model.mapping.BiMappingVO;
import com.wcompass.edgs.modules.mm.entity.internal.Feature;
import com.wcompass.edgs.modules.md.entity.Instance;
import com.wcompass.edgs.modules.md.model.SystemVO;
import com.wcompass.edgs.modules.md.model.dataflow.DataflowInstanceVO;
import com.wcompass.edgs.modules.md.model.metadata.ClassifierVO;

import java.util.List;
import java.util.Map;

/**
 * 元数据相关的业务层
 *
 * <AUTHOR>
 * @date Created on 2021/2/1
 */
public interface MetadataService {

    /**
     * 元数据是否存在
     *
     * @param instanceId
     * @return
     */
    boolean existMetadata(long instanceId);


    /**
     * 补录元数据的属性
     * @param instanceId 元数据id
     * @param attCode    属性code
     * @param attValue   属性值
     */
    void makeupAttr(long instanceId, String attCode, String attValue);

    /**
     * 补录元数据名称
     * @param instanceId
     * @param instanceName
     */
    void makeupInstanceName(long instanceId, String instanceName);

    /**
     * 查询数据源关联的schema,
     *
     * @param datasourceId 数据源id
     * @param schema       Schema的代码
     * @return
     */
    Instance getMountedSchemaOfDatasource(String datasourceId, String schema);


    /**
     * 新增元数据, 所有涉及到元数据的新增必须直接或者间接调用该接口
     *
     * @param baseInfo  元数据基本信息
     * @param attrs     属性
     * @param creatorId 创建人id
     * @return 新增的元数据的id
     */
    long createMetadata(BaseInfo baseInfo, Map<String, String> attrs, String creatorId);

    Metadata createOrGetMountedRoot(String datasourceId);

    /**
     * 获取系统默认挂载的根结点
     *
     * @return
     */
    long createOrGetDefaultRoot();

    /**
     * 元数据采集，创建元数据
     *
     * @param metadataList
     * @param metaModelCache
     */
    void createMetadata(Long startTime, List<Metadata> metadataList, Map<String, List<Feature>> metaModelCache,
                        Map<String, Boolean> containClobCache);

    void createAlteration(Long startTime, Integer extractTaskId, List<Metadata> allAlteredMetadata, Map<String, List<Feature>> metaModelCache);

    void removeMetadata(Long startTime, List<Metadata> metadataList);

    void modifyMetadata(Long startTime, List<Metadata> metadataList, Map<String, List<Feature>> metaModelCache,
                        Map<String, Boolean> containClobCache);

    void createDatasourceMount(String datasourceId, Long schema, String namespace, String classifierId);

    /**
     * 小心使用该接口
     *
     * @param baseInfo
     * @param attrs    元数据的所有属性
     * @param editorId 修改人的id
     */
    void editMetadata(BaseInfo baseInfo, Map<String, String> attrs, String editorId);

    /**
     * 修改元数据
     *
     * @param baseInfo 元数据基本信息
     * @param attrs    元数据属性信息
     * @param isMakeUp 是否是补录信息
     * @param editorId 修改人的id
     */
    void editMetadata(BaseInfo baseInfo, Map<String, String> attrs, boolean isMakeUp, String editorId);

    void removeMetadata(Long id, long operateTime, String operatorId);

    void clearRelateMetadata();

    List<CloudInstanceDTO> queryTableColumn(long instanceId);

    /**
     * 获取元数据信息, 该元数据一定存在
     *
     * @return
     */
    Instance get(Long instanceId);

    /**
     * 获取元数据信息, 并且包含大字段
     *
     * @param instanceId
     * @param containClobAttr
     * @return
     */
    Instance get(Long instanceId, boolean containClobAttr);

    Instance get(Long instanceId, boolean containStringAttr, boolean containClobAttr);

    /**
     * 获取补录信息
     *
     * @param instanceId
     * @param containClob
     * @return
     */
    Instance getMakeUp(Long instanceId, boolean containClob);

    /**
     * 获取历史版本
     *
     * @param instanceId
     * @param version
     * @param containClob
     * @return
     */
    Instance get(Long instanceId, Short version, boolean containClob);

    /**
     * 获取补录合并后的信息
     *
     * @param instanceId
     * @return
     */
    Instance getCombinedInfo(Long instanceId);

    boolean existWithSameCode(Long parentId, String classifierId, String code,
                              Long excludeId);

    BaseInfo getBaseInfo(Long instanceId);

    BaseInfo getBaseInfo(Long instanceId, Short version);

    /**
     * 获取元数据的属性，若该属性有补录值，则使用补录值，没有则使用原始属性
     * @param instanceId
     * @return
     */
    Map<String, String> getCombinedAttrs(Long instanceId);

    /**
     * key 为属性代码，value为原始值
     * @param instanceId
     * @return
     */
    Map<String, String> getAttrs(Long instanceId);

    Map<String, String> getAttrs(Long instanceId, List<Feature> featureList);

    /**
     * key 为属性代码，value为原始值
     * @param instanceId
     * @param containStringAttr
     * @param containClobAttr
     * @return
     */
    Map<String, String> getAttrs(Long instanceId, boolean containStringAttr, boolean containClobAttr,
                                 List<Feature> featureList);

    String getAttrChecksum(Metadata metadata, List<Feature> featureList, boolean loadAttrsFromDB);

    /**
     * key 为属性代码，value为原始值
     * @param instanceId
     * @param version
     * @return
     */
    Map<String, String> getAttrs(Long instanceId, Short version);

    /**
     * key 为属性代码，value为原始值
     * @param instanceId
     * @param version
     * @param containClob
     * @return
     */
    Map<String, String> getAttrs(Long instanceId, Short version, boolean containClob);

    /**
     * key为存储为
     * @param instanceId
     * @return
     */
    Map<String, String> getClobAttrs(long instanceId);

    /**
     * key为存储为
     * @param instanceId
     * @param version
     * @return
     */
    Map<String,String> getClobAttrsHis(long instanceId,short version);

    Long getSchemaIdOfDatasource(String datasourceId, String schemaName);

    Metadata buildMetadata(Long schemaId);

    SystemVO getSystem(long schemaId);

    void createDependency(Long startTime, List<Dependency> createDependency);

    void removeDependency(Long endTime, List<Dependency> removedDependency);

    Instance getFromStart(Long instanceId, Long alterTime);

    Instance getFromStart(Long instanceId, Long alterTime, boolean containAttrs);

    Instance getFromEnd(Long instanceId, Long alterTime);

    List<String> listStringAttrColumns();

    List<String> listClobAttrColumns();

    List<ClassifierVO> listDatasourceClassifier(String datasourceId);

    Page<DataflowInstanceVO> listDatasourceInstance(String datasourceId, String classifierId, String instanceCode, int currentPage, int size);

    String queryCodePath(String namespace, String parentClassifier);

    String queryCodePathUnder(String namespace, String underClassifier);

    Long getIdByPath(long instanceId, String path, String classifierId);

    List<Instance> listParentInstanceByNamespace(String namespace);

    String getAttr(long instanceId, String attStore);

    Long getSystemIdOf(Long instanceId);

    /**
     * 获取数据源绑定的元数据根结点
     * @param datasourceId
     * @return
     */
    Metadata getMountedRoot(String datasourceId);

    BaseInfo getParentBaseInfo(Long sourceId);

    /**
     * 获取元数据的归属系统
     * @param instanceId
     * @return
     */
    BaseInfo getSystemBaseInfoOf(Long instanceId);

    List<AlterationHistory> queryInfluenceDatas(long instanceId);

    /**
     * 查询当前元数据归属的路径
     * @param instanceId
     * @return
     */
    String copyPath(String instanceId,String code);

    BaseInfo getAncestorBaseInfo(long instanceId, String ancestorClassifierId);

    void deleteOnSource(Long instanceId);

    void clearDirtyMetadata();

    Page<StandardVO> listStandardMapper(Page<StandardVO> page,long instanceId,String classifierId,String keyword,String sourceSystemId);

    void listBiStandardMapping(Page<BiMappingVO> page, long instanceId, String keyword, String sourceSystemId);

    /**
     * 查询订阅到的新增类型的字段的类型/长度/精度/小数位
     */
    List<ColumnChangeDetail> listSubAddColumnChangeDetail(List<AlterationHistory> subAddList, String dataTypeAttStore, String lengthAttStore, String precisionAttStore, String scaleAttStore);

    /**
     * 查询订阅到的删除类型的字段的类型/长度/精度/小数位
     */
    List<ColumnChangeDetail> listSubDeleteColumnChangeDetail(List<AlterationHistory> subDeleteList, String dataTypeAttStore, String lengthAttStore, String precisionAttStore, String scaleAttStore);
    /**
     * 查询订阅到的修改类型的字段的类型/长度/精度/小数位
     */
    List<ColumnChangeDetail> listSubEditColumnChangeDetail(List<AlterationHistory> subEditList, String dataTypeAttStore, String lengthAttStore, String precisionAttStore, String scaleAttStore, Long oldTimeScan);

    void clearAlteration();

    BaseInfo getBaseInfoByNamespace(String namespace);

    Page<QualityRuleVO> listQualityRule(int current, int size, String classifierId, String instanceId, String dataSourceId, String schemaName, String projectId, String execStatus, String auditResult, String ruleName);

    List<Option> listProject(String classifierId, String instanceId, String dataSourceId, String schemaName);

    void recordInstanceSecurity(Long instanceId, Map<String, String> attrs);


    List<Option> listColumnMappingStandard();

    List<InstanceVO> listTableColumn(Long instanceId);

    List<InstanceVO> listReportItem(Long instanceId);

    /**
     * 是否是表级类型
     */
    boolean isTableLevel(Long instanceId);
}
