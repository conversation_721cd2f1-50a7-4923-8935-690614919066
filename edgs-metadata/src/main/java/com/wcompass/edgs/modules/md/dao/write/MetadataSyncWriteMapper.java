package com.wcompass.edgs.modules.md.dao.write;

import com.wcompass.edgs.modules.md.entity.kettle.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/10
 */
public interface MetadataSyncWriteMapper {
    void truncateColumn();

    void truncateTable();

    void truncateView();

    void batchSyncColumn(@Param("syncTime") Long syncTime);

    void batchSyncTable(@Param("syncTime") Long syncTime);

    void batchSyncView(@Param("syncTime") Long syncTime);

    void deleteAllRJobs();

    void insertRJobs(@Param("batchList") List<RJob> batchList);

    void deleteAllRJobHops();

    void insertRJobHops(@Param("batchList") List<RJobHop> batchList);

    void deleteAllRJobentrys();

    void insertRJobentrys(@Param("batchList") List<RJobentry> batchList);

    void deleteAllRJobentryTypes();

    void insertRJobentryTypes(@Param("batchList") List<RJobentryType> batchList);

    void deleteAllRJobentryCopys();

    void insertRJobentryCopys(@Param("batchList") List<RJobentryCopy> batchList);

}
