package com.wcompass.edgs.modules.security.service;

import com.wcompass.edgs.cloud.api.client.model.ProgressDTO;
import com.wcompass.edgs.core.AccountProfile;
import com.wcompass.edgs.core.Page;
import com.wcompass.edgs.modules.security.model.*;
import org.springframework.web.socket.WebSocketSession;


import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-01-17 11:34
 */
public interface SecurityModelService {

    List<ModelVO> list(String modelStatus,
                       String keyword,
                       Page<ModelVO> page);

    Boolean checkName(String name,String oldName);

    Boolean checkItemName(Integer modelId,String dirId,String name);

    ModelVO addModel(ModelVO modelVO, AccountProfile accountProfile);

    ModelVO updateModel(ModelVO modelVO,String userId);

    ModelVO copyModel(Integer modelId,String name,String modelStatus,String description, AccountProfile accountProfile);

    void deleteModel(Integer modelId);

    void startModel(Integer modelId,String modelStatus,String userId);

    ModelVO getModel(Integer modelId);

    void addModelItem(ModelItemVO modelItemVO);

    void updateModelItem(ModelItemVO modelItemVO);

    Page<ModelItemVO> listModelItem(Integer modelId,
                                    String keyword,
                                    String dirId,
                                    Page<ModelItemVO> page);

    ModelItemVO getModelItem(Integer modelItemId);

    void deleteModelItem(List<Integer> modelItemIds,Integer modelId);

    List<ModelVO> listForSelected();

    ProgressDTO asyncExportModel(Integer modelId, String modelName, String userId);

    List<ModelItemVO> getModelItemList(Integer modelId);

    String syncDownResultList(ModelVO modelVO, String userId, Integer progressId) throws IOException;

      List<List<String[]>> getTemplateDate(Integer modelId);

    void downLoadImportTemplate();

    String importModelTemplate(String fileId);

    /**
     * 分页查看导入的模型
     * @param page
     * @return
     */
    Page<ModelImportDataVO> pageImportModel(Page<ModelImportDataVO> page, String sessionId, Integer status);

    /**
     * 分页查看导入的数据项
     * @param page
     * @return
     */
    Page<ModelItemImportDataVO> pageImportModelItem(Page<ModelItemImportDataVO> page, String sessionId, Integer status);

    void deleteImpModel(Integer id,String sessionId);

    void deleteImpModelItem(Integer id);


    /**
     * 判断能否保存
     * @param sessionId
     * @return
     */
    Integer saveAble(String sessionId);

    void modelImport(String sessionId, WebSocketSession session, String userId);

    //获取数据项ID
    Integer getModelItemId(Integer modelId,String dirId,String name);

    //获取特征值Id
    Integer getItemValueId(String dirId,String name);

     Long createDirectory(Integer modelId,String namePath);
}
