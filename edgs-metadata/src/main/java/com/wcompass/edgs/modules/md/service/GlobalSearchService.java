package com.wcompass.edgs.modules.md.service;

import com.wcompass.edgs.core.Page;
import com.wcompass.edgs.modules.md.model.BusinessMetadata.BusinessFolderVO;
import com.wcompass.edgs.modules.md.model.*;

import java.util.List;


public interface GlobalSearchService {

    List<GlobalSearchItemVo> searchMetadata(String optionsJson,
                                            Page<GlobalSearchItemVo> page,
                                            String keyword,
                                            String userId,
                                            String classifierId,
                                            String systemId,
                                            String folderId);

     List<GlobalSearchParamItemVo> getClassifier(String type,String userId);

     List<GlobalSearchItemVo> getSearchSort(String currentUserId,String classifierId);

    List<GlobalSearchItemVo> getResourceRegister(String type, String userId);

    List<GlobalSearchItemVo> getResourceRegister2(String type, String userId);

    List<String> getSearchWord(String currentUserId);

    void saveKeyword(String keyword, String userId);

    void latelyDataSave(GlobalSearchItemVo globalSearchItemVo, String currentUserId);

    List<GlobalSearchItemVo> listLatelyData(String type,String classifierId,String userId);

    List<GlobalSearchSelectedItem> getSelectedItem(String classifierId);

    List<GlobalSearchParamVo> getType(String userId);

    void searchTest();

    ClassifierCount classifierCount(String keyword, String userId, String classifierId);

    List<BusinessFolderVO> getBusinessFolder(String systemId, String folderId);

}
