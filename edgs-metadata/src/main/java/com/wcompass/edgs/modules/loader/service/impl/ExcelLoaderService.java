package com.wcompass.edgs.modules.loader.service.impl;

import com.wcompass.edgs.exception.SystemException;
import com.wcompass.edgs.core.extract.Metadata;
import com.wcompass.edgs.modules.loader.model.TemplateConfig;
import com.wcompass.edgs.modules.loader.model.TemplateSheet;
import com.wcompass.edgs.core.loader.AbstractLoaderService;
import com.wcompass.edgs.modules.md.dao.read.ExtractJobReadMapper;
import com.wcompass.edgs.modules.mm.entity.internal.Feature;
import com.wcompass.edgs.modules.mm.service.MetaModelService;
import com.wcompass.edgs.utils.CollectionUtil;
import com.wcompass.edgs.utils.ExcelUtil;
import com.wcompass.edgs.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * excel采集数据加载类型
 */
@Service
@Slf4j
public class ExcelLoaderService extends AbstractLoaderService {

    @Resource
    private ExtractJobReadMapper extractJobReadMapper;

    @Resource
    private MetaModelService metaModelService;

    @Override
    public List<Metadata> loadMetadata(InputStream is, String datasourceId) {
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(is);

            List<TemplateConfig> templateConfigList = listTemplateConfig(datasourceId);

            return loadMetadata(workbook, templateConfigList);
        } catch (IOException e) {
            throw SystemException.wrap(e);
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    log.error("关闭excel异常，", e);
                }
            }
        }
    }

    private List<Metadata> loadMetadata(XSSFWorkbook workbook, List<TemplateConfig> rootConfigs) {
        List<Metadata> metadataList = new ArrayList<>();
        for (TemplateConfig rootConfig : rootConfigs) {
            List<TemplateSheet> sheetList = rootConfig.getSheetList();
            metadataList.addAll(loadMetadata(workbook, rootConfig.getClassifierId(), sheetList));
        }

        // 合并元数据
        return mergeMetadata(metadataList);
    }

    private List<Metadata> loadMetadata(XSSFWorkbook workbook, String classifierId, List<TemplateSheet> sheetList) {
        Map<String, Map<String, Feature>> featureCache = new ConcurrentHashMap<>();
        List<Metadata> collect = sheetList.stream().flatMap(conf -> {
            XSSFSheet sheet;
            try {
                sheet = workbook.getSheetAt(conf.getSheetIndex());
            } catch (Exception e) {
                return Stream.empty();
            }

            Map<String, Object> mapping = conf.getMapping();

            int lastRowNum = sheet.getLastRowNum();
            if (conf.getEndRowNo() != null) {
                lastRowNum = conf.getEndRowNo();
            }

            List<Integer> rows = new ArrayList<>();
            for (int i = conf.getStartRowNo(); i <= lastRowNum; i++) {
                rows.add(i);
            }
            return rows.parallelStream()
                    .map(sheet::getRow)
                    .filter(Objects::nonNull)
                    .map(row -> {
                        Map<String, Object> ancestor = (Map<String, Object>) mapping.get("ancestor");

                        Metadata parent = null;
                        while (CollectionUtil.isNotEmpty(ancestor)) {
                            if (parent == null) {
                                parent = new Metadata();
                            } else {
                                Metadata child = new Metadata();
                                child.setParent(parent);
                                parent.addChild(child);
                                parent = child;
                            }
                            Integer index = (Integer) ancestor.get("index");
                            String classId = (String) ancestor.get("classifierId");
                            parent.setClassifierId(classId);
                            XSSFCell cell = row.getCell(index);
                            if(cell == null) {
                                // 上级元数据不存在，则不采集
                                return null;
                            }
                            parent.setCode(ExcelUtil.getCellValue(cell));
                            ancestor = (Map<String, Object>) ancestor.get("child");
                        }

                        Metadata metadata = new Metadata();
                        metadata.setParent(parent);
                        if (parent != null) {
                            parent.addChild(metadata);
                        }

                        Integer codeIndex = (Integer) mapping.get("code");
                        Integer nameIndex = (Integer) mapping.get("name");

                        String code = ExcelUtil.getCellValue(row.getCell(codeIndex));
                        if(StringUtil.isBlank(code)) {
                            return null;
                        }
                        metadata.setCode(code);
                        metadata.setName(ExcelUtil.getCellValue(row.getCell(nameIndex)));
                        metadata.setClassifierId(classifierId);

                        Map<String, Integer> attrs = (Map<String, Integer>) mapping.get("attrs");

                        Map<String, Feature> featureMap = featureCache.computeIfAbsent(classifierId, clazzId -> metaModelService.listFeatures(clazzId)
                                    .stream()
                                    .filter(f -> metaModelService.isEnum(f.getDatatypeId()))
                                    .collect(Collectors.toMap(Feature::getAttCode, f -> f)));

                        if (CollectionUtil.isNotEmpty(attrs)) {
                            for (Map.Entry<String, Integer> entry : attrs.entrySet()) {
                                XSSFCell cell = row.getCell(entry.getValue());
                                if(cell != null) {
                                    String value = ExcelUtil.getCellValue(cell);
                                    if(StringUtil.isNotBlank(value) && featureMap.containsKey(entry.getKey())) {
                                        Feature feature = featureMap.get(entry.getKey());
                                        value = metaModelService.valueOfDatatype(feature.getDatatypeId(), value);
                                    }
                                    metadata.addAttr(entry.getKey(), value);
                                }
                            }
                        }
                        while (metadata.getParent() != null) {
                            metadata = metadata.getParent();
                        }
                        return metadata;
                    });
        }).filter(Objects::nonNull).collect(Collectors.toList());

        // 合并元数据
        return mergeMetadata(collect);
    }



    private List<TemplateConfig> listTemplateConfig(String datasourceId) {
        List<TemplateConfig> configs = extractJobReadMapper.listTemplateConfig(datasourceId);
        configs.forEach(config -> config.setSheetList(extractJobReadMapper.listTemplateSheets(config.getId())));
        return configs;
    }
}
