package com.wcompass.edgs.modules.md.model.metadata;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/5/8 18:09
 * @Version 1.0
 */
@Data
@Schema(description = "元数据详情")
public class DetailInfo implements Serializable {

    private static final long serialVersionUID = 7598285624893020571L;

    private String instanceId;

    private String instanceName;

    private String additionInstanceName;

    private String instanceCode;

    private String classifierId;

    private String classifierName;

    private String parentInstanceCode;

    private String parentClassifierId;

    private String parentId;

    private String codeCnPath;

    private String codeEnPath;

    private String namespace;

    /**
     * 元数据浏览量
     */
    private Long viewCount;

    private Date startTime;

    private String datasourceId;

    private String datasourceName;

    private String dbType;

    private List<AttributeVO> attrs;

    private Map<String, List<AttributeVO>> attrGroups;

    private String operateType;

    private Integer relType;

    private String isValid;

    @Schema(description = "是否收藏")
    private Boolean isCollected;

    @Schema(description = "是否订阅")
    private Boolean isSubscribed;

    /**
     * 万家字段变更详情页面新增字段级查看上级元数据信息
     */
    private String parentCode;

    private String parentName;

    private String parentClassifier;

    private String deptName;
}
