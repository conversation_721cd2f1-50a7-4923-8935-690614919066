package com.wcompass.edgs.modules.parse.service.impl;

import com.wcompass.edgs.modules.md.dao.read.DatasourceMappingReadMapper;
import com.wcompass.edgs.modules.parse.model.datasource.DatasourceMapping;
import com.wcompass.edgs.modules.parse.model.datasource.DatasourceNameVO;
import com.wcompass.edgs.modules.parse.service.DatasourceMappingService;
import com.wcompass.edgs.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年01月03日16:52
 */

@Service
@Slf4j
public class DatasourceMappingServiceImpl implements DatasourceMappingService {

    @Resource
    private DatasourceMappingReadMapper datasourceMappingReadMapper;

    /**
     * @param datasourceType
     * @param status         true 按照类型查询， false 查询数据库所有
     * @return
     */
    @Override
    public List<DatasourceMapping> queryDatasourceMappingByType(String datasourceType, boolean status) {
        List<DatasourceMapping> datasourceMappingList = new ArrayList<>();
        if (status) {
            datasourceMappingList = datasourceMappingReadMapper.getDatasourceMappingByType(datasourceType);
        } else {
            datasourceMappingList = datasourceMappingReadMapper.queryAllDatasourceMapping();
        }

        //开始处理数据源的默认schema，根据业务场景进行处理。当前支持 Gp、pg、mysql、oracle。后续有需要在支持
        datasourceMappingList.forEach(datasourceMapping -> {
            //判断是否存在defaultSchema，如果存在则
            if (StringUtil.isBlank(datasourceMapping.getDefaultSchema())) {
                switch (datasourceMapping.getAdapterName().toUpperCase()) {
                    //oracle 取链接用户名为默认schema、多个用户名取第一个。
                    case "ORACLE":
                        if (StringUtil.isBlank(datasourceMapping.getDefaultSchema())) {
                            if ("Kettle".equals(datasourceMapping.getDatasourceMappingType())) {
                                //如果是Kettle,默认schema信息需要去connection去获取
                                datasourceMapping.setDefaultSchema(datasourceMappingReadMapper.getKettleConnectionUserByOracle(
                                        datasourceMapping.getExternalDatasourceName()));
                            } else {
                                datasourceMapping.setDefaultSchema(datasourceMappingReadMapper.queryDatasourceUserName(
                                        datasourceMapping.getDatasourceId()));
                            }
                        }
                        break;
                    case "MYSQL":
                    case "POSTGRESQL":
                    case "GREENPLUM":
                    case "SQLSERVER":
                        if (StringUtil.isBlank(datasourceMapping.getDefaultSchema())) {
                            datasourceMapping.setDefaultSchema(datasourceMappingReadMapper.queryDatasrouceDatabase(
                                    datasourceMapping.getDatasourceId()));
                        }
                        break;
                    default:
                        datasourceMapping.setDefaultSchema("");
                }
            }

        });
        return datasourceMappingList;
    }

    @Override
    public List<DatasourceNameVO> queryDatasourceNameVO() {
        return datasourceMappingReadMapper.queryAllDatasourceNameVO();
    }
}
