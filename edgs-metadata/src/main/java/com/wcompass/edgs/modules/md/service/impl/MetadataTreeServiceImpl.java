package com.wcompass.edgs.modules.md.service.impl;

import com.wcompass.edgs.cloud.api.client.auth.AuthCenter;
import com.wcompass.edgs.cloud.api.client.auth.AuthCenterClient;
import com.wcompass.edgs.core.AjaxResponseWrapper;
import com.wcompass.edgs.core.Page;
import com.wcompass.edgs.core.security.BaseInfo;
import com.wcompass.edgs.exception.SystemException;
import com.wcompass.edgs.modules.md.dao.read.InstanceMountReadMapper;
import com.wcompass.edgs.modules.md.dao.read.InstanceReadMapper;
import com.wcompass.edgs.modules.md.service.MetadataService;
import com.wcompass.edgs.modules.mm.entity.internal.Classifier;
import com.wcompass.edgs.modules.md.entity.Instance;
import com.wcompass.edgs.modules.md.entity.InstanceMount;
import com.wcompass.edgs.modules.md.model.metadata.MetadataTreeNodeVO;
import com.wcompass.edgs.modules.md.service.MetadataQueryService;
import com.wcompass.edgs.modules.md.service.MetadataTreeService;
import com.wcompass.edgs.core.extract.MetaModel;
import com.wcompass.edgs.modules.mm.service.MetaModelService;
import com.wcompass.edgs.utils.BeanUtil;
import com.wcompass.edgs.utils.CollectionUtil;
import com.wcompass.edgs.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/5/6 17:08
 * @Version 1.0
 */
@Service
@Slf4j
public class MetadataTreeServiceImpl implements MetadataTreeService {

    @Resource
    private InstanceReadMapper instanceReadMapper;

    @Resource
    private MetaModelService metaModelService;

    @Resource
    private AuthCenter authCenter;

    @Resource
    private MetadataQueryService metadataQueryService;

    @Resource
    private InstanceMountReadMapper instanceMountReadMapper;

    @Resource
    private MetadataService metadataService;

    @Resource
    private AuthCenterClient authCenterClient;

    @Override
    public List<MetadataTreeNodeVO> tree(Integer current, Long parentId, Long systemId,
                                         String datasourceId,
                                         Long mountMetadataId,
                                         String classifierId,
                                         String nodeType,
                                         String keyword,
                                         String userId) {
        AjaxResponseWrapper<Boolean> res = authCenterClient.isSysadmin(userId);
        if (res.isSuccess()) {
            if (res.getData()) {
                userId = null;
            }
        } else {
            throw SystemException.wrap(res.getMessage());
        }

        List<MetadataTreeNodeVO> tree = new ArrayList<>();
        AjaxResponseWrapper<String> pageSize = authCenter.getSystemValue(Constants.TREE_NODE_PAGE_SIZE);
        if (!pageSize.isSuccess()) {
            throw SystemException.wrap(pageSize.getMessage());
        }
        String paramValue = pageSize.getData();
        int size = 20;
        if (StringUtil.isNotBlank(paramValue)) {
            size = Integer.parseInt(paramValue);
        }
        if (StringUtil.isBlank(keyword) && parentId.equals(systemId)) {
            // 查询所有的Schema
            // 系统下可以挂载多个数据源
            Page<InstanceMount> page = new Page<>(size, current);
            List<InstanceMount> instanceMounts = instanceMountReadMapper.selectMountedMetadatasBySystemId(page, systemId, datasourceId, mountMetadataId, userId);
            if (CollectionUtil.isNotEmpty(instanceMounts)) {
                Instance instance = null;
                for (InstanceMount instanceMount : instanceMounts) {
                    instance = new Instance();
                    BeanUtil.copyProperties(instanceMount, instance);
                    instance.setParentId(Long.valueOf(instanceMount.getRootId()));
                    MetadataTreeNodeVO treeNodeVO = buildMetadata(instance, false);
                    tree.add(treeNodeVO);
                }
                //判断是否还有下一页
                if (page.getRecords().size() < page.getSize()) {
                    HAS_NEXT_PAGE.set(Boolean.FALSE);
                } else if (page.getTotal() == (long) current * size) {
                    HAS_NEXT_PAGE.set(Boolean.FALSE);
                } else {
                    HAS_NEXT_PAGE.set(Boolean.TRUE);
                }
                return tree;
            }
        } else {
            switch (nodeType) {
                case Constants.TreeNodeType.METADATA:
                    // 查询schema下的元模型
                    log.debug("获取元数据下的元模型节点");
                    BaseInfo baseInfo = metadataService.getBaseInfo(parentId);
                    if (metaModelService.canShowChild(baseInfo.getClassifierId())) {
                        List<Classifier> classifierIds = instanceReadMapper.listRelateClassifiersWithMetadata(parentId, classifierId);
                        for (Classifier classifier : classifierIds) {
                            Integer count = instanceReadMapper.selectObjectCount(parentId, classifier.getClassifierId());
                            MetadataTreeNodeVO modelNode = new MetadataTreeNodeVO();
                            modelNode.setNodeType(Constants.TreeNodeType.META_MODEL);
                            //modelNode.setName(classifier.getClassifierName());
                            modelNode.setName(count+"");
                            modelNode.setCode(classifier.getClassifierId());
                            modelNode.setClassifierId(classifier.getClassifierId());
                            modelNode.setParentNodeId(parentId.toString());
                            modelNode.setIsLeaf(false);

                            modelNode.setId(StringUtil.join(parentId, "@", classifier.getClassifierId()));
                            tree.add(modelNode);
                        }
                        HAS_NEXT_PAGE.set(Boolean.FALSE);
                    } else {
                        tree = loadTree(size, current, keyword, parentId, systemId, datasourceId, mountMetadataId, null, userId);
                    }
                    break;
                case Constants.TreeNodeType.META_MODEL:
                default:
                    tree = loadTree(size, current, keyword, parentId, systemId, datasourceId, mountMetadataId, classifierId, userId);
                    break;
            }
        }
        return tree;
    }

    private List<MetadataTreeNodeVO> loadTree(int size, int current, String keyword,
                                              Long parentId,
                                              Long systemId,
                                              String datasourceId,
                                              Long mountMetadataId,
                                              String classifierId,
                                              String userId) {
        Page<Instance> page = new Page<>(size, current);
        boolean needShowContext = false;
        if (StringUtil.isNotBlank(keyword)) {
            // 通过搜索查询
            parentId = null;
            needShowContext = true;
        }
        List<String> namespaceLikeList = metadataQueryService.listMountedNamespaceLikeBySystemId(systemId, datasourceId, mountMetadataId, userId);
        instanceReadMapper.pageBaseInfoByParentId(page, namespaceLikeList, parentId, classifierId, keyword);
        // 使用并行流计算
        boolean flag = needShowContext;
        List<MetadataTreeNodeVO> tree = page.getRecords()
                .parallelStream()
                .map(node -> this.buildMetadata(node, flag))
                .collect(Collectors.toList());
        //判断是否还有下一页
        if (page.getRecords().size() < page.getSize()) {
            HAS_NEXT_PAGE.set(Boolean.FALSE);
        } else if (page.getTotal() == (long) current * size) {
            HAS_NEXT_PAGE.set(Boolean.FALSE);
        } else {
            HAS_NEXT_PAGE.set(Boolean.TRUE);
        }
        return tree;
    }

    private MetadataTreeNodeVO buildMetadata(Instance instance, boolean needShowContext) {
        BaseInfo baseInfo = metadataService.getBaseInfo(instance.getInstanceId());

        MetadataTreeNodeVO treeNodeVO = new MetadataTreeNodeVO();
        treeNodeVO.setId(String.valueOf(instance.getInstanceId()));
        treeNodeVO.setName(instance.getInstanceName());
        treeNodeVO.setInstanceName(baseInfo.getInstanceName());
        treeNodeVO.setCode(instance.getInstanceCode());
        treeNodeVO.setNamespace(instance.getNamespace());
        if (needShowContext) {
            treeNodeVO.setPathCN(metadataService.copyPath(String.valueOf(instance.getInstanceId()), "instance_name"));
            treeNodeVO.setPathEN(metadataService.copyPath(String.valueOf(instance.getInstanceId()), "instance_code"));
            treeNodeVO.setPath(metadataQueryService.getNameSpaceContext(instance.getNamespace()));
        }
        treeNodeVO.setClassifierId(instance.getClassifierId());
        treeNodeVO.setNodeType(Constants.TreeNodeType.METADATA);
        treeNodeVO.setParentNodeId(instance.getParentId().toString());
        treeNodeVO.setRelType(instance.getRelType());
        if (instanceReadMapper.existMedataUnderParentId(instance.getInstanceId())) {
            treeNodeVO.setIsLeaf(false);
        }

        // 当存在补录名称时，要显示补录名称
        String instanceName = instanceReadMapper.getMakeUpInstanceName(instance.getInstanceId());
        if (StringUtil.isNotBlank(instanceName)) {
            treeNodeVO.setName(instanceName);
        }
        return treeNodeVO;
    }

    @Override
    public MetadataTreeNodeVO locationTree(Long instanceId) {
        BaseInfo baseInfo = instanceReadMapper.getBaseInfo(instanceId);
        if (baseInfo == null) {
            throw SystemException.warn("无效元数据");
        }

        List<Long> namespaces = Arrays.stream(baseInfo.getNamespace().split("/"))
                .filter(StringUtil::isNotBlank)
                .map(Long::valueOf)
                .collect(Collectors.toList());

        List<BaseInfo> namespaceInstances = instanceReadMapper.selectBaseInfoByIds(namespaces).stream()
                .filter(e -> !MetaModel.ROOT.equals(e.getClassifierId()) && !MetaModel.SYSTEM.equals(e.getClassifierId()))
                .sorted(Comparator.comparing(e -> e.getNamespace().length()))
                .collect(Collectors.toList());

        MetadataTreeNodeVO root = new MetadataTreeNodeVO();
        MetadataTreeNodeVO currentNode = root;
        boolean isFirstNode = true;
        for (BaseInfo metadataVO : namespaceInstances) {
            BaseInfo parentMetadata = metadataService.getBaseInfo(metadataVO.getParentId());
            if (!isFirstNode && parentMetadata != null && metaModelService.canShowChild(parentMetadata.getClassifierId())) {
                MetadataTreeNodeVO classifierNode = createClassifierNode(metadataVO, false);
                currentNode.setChildren(Collections.singletonList(classifierNode));
                currentNode = classifierNode;
            }

            MetadataTreeNodeVO metadataNode = createMetadataNode(metadataVO, false);
            currentNode.setChildren(Collections.singletonList(metadataNode));
            currentNode = metadataNode;
            isFirstNode = false;
        }
        return root.getChildren().isEmpty() ? null : root.getChildren().get(0);
    }

    private MetadataTreeNodeVO createClassifierNode(BaseInfo metadataVO, Boolean isLeaf) {
        Classifier classifier = metaModelService.selectByClassifierId(metadataVO.getClassifierId());
        MetadataTreeNodeVO node = new MetadataTreeNodeVO();
        node.setNodeType(Constants.TreeNodeType.META_MODEL);
        node.setName(classifier.getClassifierName());
        node.setCode(classifier.getClassifierId());
        node.setParentNodeId(String.valueOf(metadataVO.getInstanceId()));
        node.setIsLeaf(isLeaf);
        node.setId(StringUtil.join(metadataVO.getInstanceId(), "@", classifier.getClassifierId()));
        return node;
    }

    private MetadataTreeNodeVO createMetadataNode(BaseInfo metadataVO, Boolean isLeaf) {
        MetadataTreeNodeVO node = new MetadataTreeNodeVO();
        node.setId(String.valueOf(metadataVO.getInstanceId()));
        node.setCode(metadataVO.getInstanceCode());
        node.setName(getMetadataInstanceName(metadataVO));
        node.setNodeType(Constants.TreeNodeType.METADATA);
        node.setClassifierId(metadataVO.getClassifierId());
        node.setParentNodeId(String.valueOf(metadataVO.getParentId()));
        node.setNamespace(metadataVO.getNamespace());
        node.setIsLeaf(isLeaf);
        return node;
    }


    private String getMetadataInstanceName(BaseInfo metadataVO) {
        String instanceName = instanceReadMapper.getMakeUpInstanceName(metadataVO.getInstanceId());
        return StringUtil.isNotBlank(instanceName) ? instanceName : metadataVO.getInstanceName();
    }



}
