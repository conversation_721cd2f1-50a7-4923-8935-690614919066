package com.wcompass.edgs.modules.md.controller;

import com.wcompass.edgs.core.AjaxResponseWrapper;
import com.wcompass.edgs.core.log.SystemLog;
import com.wcompass.edgs.modules.md.model.extract.ExtractLogVO;
import com.wcompass.edgs.modules.md.model.extract.TaskDatasourceVO;
import com.wcompass.edgs.modules.md.service.ExtractLogService;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/31
 */
@RestController
@RequestMapping("/job/log")
@Tag(name = "元数据-元数据采集任务日志管理")
@Valid
public class ExtractLogController {

    @Resource
    private ExtractLogService extractLogService;

    @GetMapping("/listTaskDatasource")
    @Operation(summary = "查询采集的数据源")
    @SystemLog(description = "查询采集的数据源")
    public AjaxResponseWrapper<List<TaskDatasourceVO>> listTaskDatasource(
            @Parameter(description = "任务id", required = true) @RequestParam int taskId
    ) {
        return AjaxResponseWrapper.data(extractLogService.listTaskDatasource(taskId));
    }

    @GetMapping("/datasource/status")
    @Operation(summary = "查看采集数据源的状态")
    @SystemLog(description = "查看采集数据源的状态")
    public AjaxResponseWrapper<TaskDatasourceVO> getStatusOfDatasource(
            @Parameter(description = "任务id", required = true) @RequestParam int taskId,
            @Parameter(description = "数据源的id", required = true) @RequestParam String datasourceId
    ) {
        return AjaxResponseWrapper.data(extractLogService.getStatusOfDatasource(taskId, datasourceId));
    }

    @GetMapping("/listTaskLogs")
    @Operation(summary = "查询采集任务执行日志")
    @SystemLog(description = "查询采集任务执行日志")
    public AjaxResponseWrapper<List<ExtractLogVO>> listExtractLogs(
            @Parameter(description = "任务id", required = true) @RequestParam int taskId,
            @Parameter(description = "数据源的id") @RequestParam(required = false) String datasourceId,
            @Parameter(description = "采集的Schema的名称") @RequestParam(required = false) String schema,
            @Parameter(description = "日志时间") @RequestParam(required = false) Long logTime
    ) {
        return AjaxResponseWrapper.data(extractLogService.listExtractLogs(taskId, datasourceId, schema, logTime));
    }
}
