package com.wcompass.edgs.modules.md.controller;

import com.wcompass.edgs.core.AjaxResponseWrapper;
import com.wcompass.edgs.core.BaseController;
import com.wcompass.edgs.core.Option;
import com.wcompass.edgs.core.Page;
import com.wcompass.edgs.core.log.SystemLog;
import com.wcompass.edgs.modules.md.model.SystemDatasourceVO;
import com.wcompass.edgs.modules.md.model.dataflow.*;
import com.wcompass.edgs.modules.md.model.dataflow.request.AddDataflowVO;
import com.wcompass.edgs.modules.md.model.dataflow.request.DataflowSutureEditVO;
import com.wcompass.edgs.modules.md.model.dataflow.request.EditDataflowVO;
import com.wcompass.edgs.modules.md.model.datamap.NodeVO;
import com.wcompass.edgs.modules.md.model.metadata.ClassifierVO;
import com.wcompass.edgs.modules.md.service.DataflowService;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/7/5 13:30
 */
@RestController
@RequestMapping("/dataflow")
@Tag(name = "元数据-血缘")
@Validated
@Valid
public class DataflowController extends BaseController {

    @Autowired
    private DataflowService dataflowService;

    @GetMapping("/listDataflow")
    @Operation(summary = "元数据血缘列表")
    public AjaxResponseWrapper<Page<DataflowVO>> listDataflow(
            @Parameter(name = "sourceDatasourceId", description = "源端数据源ID") @RequestParam(name = "sourceDatasourceId", required = false) String sourceDatasourceId,
            @Parameter(name = "sourceId", description = "源端对象ID") @RequestParam(name = "sourceId", required = false) String sourceId,
            @Parameter(name = "sourceClassifierId", description = "源端对象类型ID") @RequestParam(name = "sourceClassifierId", required = false) String sourceClassifierId,
            @Parameter(name = "targetDatasourceId", description = "目标端数据源ID") @RequestParam(name = "targetDatasourceId", required = false) String targetDatasourceId,
            @Parameter(name = "targetId", description = "目标端对象ID") @RequestParam(name = "targetId", required = false) String targetId,
            @Parameter(name = "targetClassifierId", description = "目标端对象类型ID") @RequestParam(name = "targetClassifierId", required = false) String targetClassifierId,
            @Parameter(name = "sourceType", description = "数据来源") @RequestParam(name = "sourceType", required = false) String sourceType,
            @Parameter(name = "page") @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @Parameter(name = "size") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        return AjaxResponseWrapper.data(dataflowService.listDataflow(sourceDatasourceId, sourceId, targetDatasourceId,
                targetId, Page.of(size,page),sourceClassifierId,targetClassifierId,sourceType,getCurrentUserId()));
    }


    @GetMapping("/listDataflowDatasource")
    @Operation(summary = "获取元素据血缘数据源列表")
    public AjaxResponseWrapper<List<SystemDatasourceVO>> listDataflowDatasource(@Parameter(name = "datasourceName", description = "数据源名称")
                                                                                @RequestParam(name = "datasourceName", required = false) String datasourceName,
                                                                                @Parameter(name = "datasourceType", description = "数据源类型,1:源端,2：目标端")
                                                                                @RequestParam(name = "datasourceType", required = false, defaultValue = "1") Byte datasourceType) {
        return AjaxResponseWrapper.data(dataflowService.listDataflowDatasource(datasourceName, datasourceType));
    }


    @GetMapping("/listDataflowInstance")
    @Operation(summary = "获取元素据血缘对象列表")
    public AjaxResponseWrapper<Page<DataflowInstanceVO>> listDataflowObject(
            @Parameter(name = "instanceCode", description = "对象代码") @RequestParam(name = "instanceCode", required = false) String instanceCode,
            @Parameter(name = "instanceType", description = "对象类型,1：源端,2：目标端") @RequestParam(name = "instanceType", required = false, defaultValue = "1") Byte instanceType,
            @Parameter(description = "数据源id") @RequestParam(name = "datasourceId", required = false) String datasourceId,
            @Parameter(description = "元数据类型id") @RequestParam(name = "classifierId", required = false) String classifierId,
            @Parameter(name = "page") @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @Parameter(name = "size") @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        return AjaxResponseWrapper.data(dataflowService.listDataflowInstance(instanceCode, instanceType, datasourceId, classifierId, page, size));
    }
    @GetMapping("/listDataflowObjectClassifier")
    @Operation(summary = "获取元素据血缘对象类型列表")
    public AjaxResponseWrapper<List<ClassifierVO>> listDataflowObjectClassifier(
            @Parameter(name = "instanceType", description = "对象类型,1：源端,2：目标端") @RequestParam(name = "instanceType", required = false, defaultValue = "1") Byte instanceType,
            @Parameter(description = "数据源id") @RequestParam(name = "datasourceId", required = false) String datasourceId) {
        return AjaxResponseWrapper.data(dataflowService.listDataflowObjectClassifier(instanceType, datasourceId));
    }


    @PostMapping("/addDataflow")
    @Operation(summary = "添加血缘")
    @SystemLog(description = "添加血缘", level = 3)
    public AjaxResponseWrapper<Void> addDataflow(@Validated @RequestBody AddDataflowVO addDataflowVO) {
        dataflowService.addDataflow(addDataflowVO);
        return AjaxResponseWrapper.operate(true, "添加");
    }

    @PutMapping("/editDataflow")
    @Operation(summary = "编辑血缘")
    @SystemLog(description = "编辑血缘", level = 3)
    public AjaxResponseWrapper<Void> editDataflow(@Validated @RequestBody EditDataflowVO editDataflowVO) {
        dataflowService.updateDataflow(editDataflowVO);
        return AjaxResponseWrapper.operate(true, "修改");
    }


    @DeleteMapping("/deleteDataflow")
    @Operation(summary = "删除血缘")
    @SystemLog(description = "删除血缘", level = 3)
    public AjaxResponseWrapper<Void> deleteDataflow(
            @Parameter(description = "血缘id, 多个用','隔开") @RequestParam List<Long> id) {
        dataflowService.deleteDataflow(id);
        return AjaxResponseWrapper.operate(true, "删除");
    }


    @GetMapping("/listDatasource")
    @Operation(summary = "获取数据源列表")
    public AjaxResponseWrapper<List<SystemDatasourceVO>> listDatasource(
            @Parameter(description = "数据源名称") @RequestParam(required = false) String datasourceName,
            @Parameter(description = "元数据类型") @RequestParam String classifierId
    ) {
        return AjaxResponseWrapper.data(dataflowService.listSystemDatasource(classifierId, datasourceName));
    }

    @GetMapping("/listDatasourceClassifier")
    @Operation(summary = "获取数据源下元数据类型")
    public AjaxResponseWrapper<List<ClassifierVO>> listInstanceClassifier(@Parameter(name = "datasourceId", description = "数据源Id")
                                                                          @RequestParam(name = "datasourceId", required = false) String datasourceId) {
        return AjaxResponseWrapper.data(dataflowService.listDatasourceClassifier(datasourceId));
    }


    @GetMapping("/listDatasourceInstance")
    @Operation(summary = "获取数据源下元数据")
    public AjaxResponseWrapper<Page<DataflowInstanceVO>> listDatasourceInstance(@Parameter(name = "datasourceId", description = "数据源Id")
                                                                                @RequestParam(name = "datasourceId", required = false) String datasourceId,
                                                                                @Parameter(name = "classifierId", description = "元数据类型")
                                                                                @RequestParam(name = "classifierId", required = false) String classifierId,
                                                                                @Parameter(name = "instanceCode", description = "元数据代码")
                                                                                @RequestParam(name = "instanceCode", required = false) String instanceCode,
                                                                                @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
                                                                                @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        return AjaxResponseWrapper.data(dataflowService.listInstance(datasourceId, classifierId, instanceCode, page, size));
    }


    @GetMapping("/downloadInstanceDataflow")
    @Operation(summary = "下载元数据血缘模板")
    public void downloadInstanceDataflowTemplate() {
        dataflowService.downloadInstanceDataflowTemplate();
    }


    @PostMapping("/uploadInstanceDataflow")
    @Operation(summary = "上传元数据血缘")
    public AjaxResponseWrapper<String> uploadInstanceDataflow(@NotNull(message = "请选择上传文件")
                                                              @RequestPart MultipartFile file) {
        return AjaxResponseWrapper.data(dataflowService.uploadInstanceDataflow(file));
    }

    @PostMapping("/checkImportData")
    @Operation(summary = "校验导入的数据")
    public AjaxResponseWrapper<Void> checkImportData(@RequestParam String sessionId) {
        dataflowService.asyncCheckImportData(sessionId);
        return AjaxResponseWrapper.tip("校验完成");
    }

    @GetMapping("/status/{sessionId}")
    @Operation(summary = "获取校验状态, 0-校验完成、1-校验中")
    public AjaxResponseWrapper<Integer> getCheckStatus(@PathVariable String sessionId) {
        return AjaxResponseWrapper.data(dataflowService.getCheckStatus(sessionId));
    }


    @GetMapping("/listImportDataflow")
    @Operation(summary = "获取血缘导入结果")
    public AjaxResponseWrapper<Page<DataflowSutureVO>> listImportDataflow(@NotBlank(message = "请指定上传批次")
                                                                          @Parameter(name = "sessionId", description = "导入批次ID")
                                                                          @RequestParam(name = "sessionId") String sessionId,
                                                                          @Parameter(description = "结果") @RequestParam(required = false) String status,
                                                                          @Parameter(description = "搜索关键字") @RequestParam(required = false) String keyword,
                                                                          @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
                                                                          @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        return AjaxResponseWrapper.data(dataflowService.listDataflowSuture(sessionId, page, size, status, keyword))
                .addExtras("existErrorData", dataflowService.existErrorData(sessionId))
                .addExtras("num", dataflowService.getDataflowSutureNum(sessionId));
    }


    @DeleteMapping("/deleteImportDataflow/{resultId}")
    @Operation(summary = "删除血缘导入结果")
    public AjaxResponseWrapper<Void> deleteImportDataflow(@NotBlank(message = "请指定删除记录")
                                                          @PathVariable(name = "resultId") String resultId) {
        dataflowService.deleteDataflowSuture(resultId);
        return AjaxResponseWrapper.operate(true, "删除");
    }


    @PutMapping("/editImportDataflow")
    @Operation(summary = "编辑血缘导入结果")
    public AjaxResponseWrapper<Void> updateImportDataflow(@Validated @RequestBody DataflowSutureEditVO dataflowSutureEditVO) {
        dataflowService.editDataflowSuture(dataflowSutureEditVO);
        return AjaxResponseWrapper.operate(true, "修改");
    }


    @PostMapping("/submitImportDataflow")
    @Operation(summary = "提交血缘导入结果")
    public AjaxResponseWrapper<Void> submitImportDataflow(@NotNull(message = "请指定上传批次")
                                                          @Parameter(name = "sessionId", description = "导入批次ID")
                                                          @RequestParam(name = "sessionId") String sessionId) {
        dataflowService.submitDataflowSuture(sessionId);
        return AjaxResponseWrapper.operate(true, "导入");
    }


    @GetMapping("/queryDataflowMap")
    @Operation(summary = "查询血缘关系图")
    public AjaxResponseWrapper<DataflowGraphVO> queryDataflowMap(@Parameter(name = "sourceId", description = "源端对象ID")
                                                                 @NotBlank(message = "请指定血缘记录")
                                                                 @RequestParam(name = "sourceId") String sourceId,
                                                                 @Parameter(name = "targetId", description = "目标端对象ID")
                                                                 @NotBlank(message = "请指定血缘记录")
                                                                 @RequestParam(name = "targetId") String targetId,
                                                                 @Parameter(name = "transformId", description = "转换对象ID")
                                                                 @RequestParam(name = "transformId", required = false) String transformId,
                                                                 @Parameter(name = "depth", description = "血缘深度,默认为1")
                                                                 @RequestParam(name = "depth", required = false, defaultValue = "1") Integer depth,
                                                                 @Parameter(description = "是否显示ETL信息")
                                                                 @RequestParam(required = false, defaultValue = "false") boolean showEtl) {
        return AjaxResponseWrapper.data(dataflowService.queryDataflowMap(sourceId, targetId, transformId, depth, showEtl));
    }


    @PostMapping("/queryAncestryDataflow")
    @Operation(summary = "血统分析")
    public AjaxResponseWrapper<DataflowGraphVO> queryAncestryDataflow(
            @Parameter(description = "分析对象ID")
            @NotBlank(message = "请指定分析对象")
            @RequestParam String metadataId,
            @Parameter(description = "血缘深度,默认为1")
            @RequestParam(required = false, defaultValue = "1") Integer depth,
            @Parameter(description = "是否显示ETL信息")
            @RequestParam(required = false, defaultValue = "false") boolean showEtl,
            @Parameter(description = "显示层级, 1-字段级，2-表级") @RequestParam(required = false, defaultValue = "2") byte showLevel) {
        return AjaxResponseWrapper.data(dataflowService.queryAncestryDataflow(metadataId, depth, showEtl, showLevel));
    }


    @GetMapping("/queryInfluenceDataflow")
    @Operation(summary = "影响分析")
    public AjaxResponseWrapper<DataflowGraphVO> queryInfluenceDataflow(
            @Parameter(description = "分析对象ID")
            @NotBlank(message = "请指定分析对象")
            @RequestParam String metadataId,
            @Parameter(description = "血缘深度,默认为1")
            @RequestParam(required = false, defaultValue = "1") Integer depth,
            @Parameter(description = "是否显示ETL信息")
            @RequestParam(required = false, defaultValue = "false") boolean showEtl,
            @Parameter(description = "显示层级, 1-字段级，2-表级") @RequestParam(required = false, defaultValue = "2") byte showLevel) {
        return AjaxResponseWrapper.data(dataflowService.queryInfluenceDataflow(metadataId, depth, showEtl, showLevel));
    }


    @GetMapping("/queryAllDataflow")
    @Operation(summary = "全链分析")
    public AjaxResponseWrapper<DataflowGraphVO> queryAllDataflow(
            @Parameter(description = "分析对象ID")
            @NotBlank(message = "请指定分析对象")
            @RequestParam String metadataId,
            @Parameter(description = "血缘深度,默认为1")
            @RequestParam(required = false, defaultValue = "1") Integer depth,
            @Parameter(description = "是否显示ETL信息")
            @RequestParam(required = false, defaultValue = "false") boolean showEtl,
            @Parameter(description = "显示层级, 1-字段级，2-表级") @RequestParam(required = false, defaultValue = "2") byte showLevel,
            @Parameter(description = "展示范围")
            @RequestParam(required = false, defaultValue = "all") String scope,
            @Parameter(description = "目标id集合") @RequestParam(required = false) List<String> analysisIds
    ) {
        return AjaxResponseWrapper.data(dataflowService.queryAllDataflow(metadataId, depth, showEtl, showLevel, scope, analysisIds));
    }

    @GetMapping("/listSourceClassifier")
    @Operation(summary = "查询血缘源端缝合类型")
    public AjaxResponseWrapper<List<Option>> listSourceClassifier(
    ) {
        return AjaxResponseWrapper.data(dataflowService.listSourceClassifier());
    }

    @GetMapping("/listDataflowClassifier")
    @Operation(summary = "查询血缘缝合类型")
    public AjaxResponseWrapper<List<RelationDataflowVO>> listDataflowClassifier(
            @RequestParam String sourceClassifierId,
            @RequestParam(required = false) String targetClassifierId
    ) {
        return AjaxResponseWrapper.data(dataflowService.listDataflowClassifier(sourceClassifierId, targetClassifierId));
    }

    @GetMapping("/exportDataflow")
    @Operation(summary = "导出血缘链路的全链")
    public void exportDataflow(@Parameter(name = "metadataId", description = "元数据ID")
                                   @NotBlank(message = "请指定元数据")@RequestParam String metadataId,
                               @Parameter(description = "显示层级, 1-字段级，2-表级") @RequestParam(required = false, defaultValue = "2") byte showLevel) {
        dataflowService.exportDataflow(metadataId,showLevel);
    }

    @GetMapping("/judgeDatasource")
    @Operation(summary = "判断当前用户是否具有该数据源权限")
    public AjaxResponseWrapper<Boolean> judgeDatasource(@Parameter(name = "datasourceId", description = "数据源ID")   @RequestParam String datasourceId){
        return AjaxResponseWrapper.data(dataflowService.judgeDatasource(datasourceId,getCurrentAccount() ));
    }

    /**
     * 删除指定批次的全量异常血缘数据
     */
    @PostMapping("/deleteInvalidDataflow")
    @Operation(summary = "删除指定批次的全量异常血缘数据")
    public AjaxResponseWrapper<Void> deleteInvalidDataflow(@NotNull(message = "sessionId不能为空")
                                                           @Parameter(name = "sessionId", description = "导入批次ID")
                                                           @RequestParam(name = "sessionId") String sessionId) {
        this.dataflowService.deleteInvalidDataflow(sessionId);
        return AjaxResponseWrapper.success();
    }

    @GetMapping("/exportErrorData")
    @Operation(summary = "导出异常血缘数据")
    public AjaxResponseWrapper<Integer> exportErrorData(@Parameter(description = "当前批次id") @RequestParam("sessionId") String sessionId) {
        return AjaxResponseWrapper.data(dataflowService.exportErrorDataProcess(sessionId,this.getCurrentUserId()).getProgressId());
    }

    @GetMapping("/getColumnNode")
    @Operation(summary = "获取表下有血缘的字段")
    public AjaxResponseWrapper<List<NodeVO>> getColumnNode(
            @Parameter(description = "表级id") @RequestParam("tableId") String tableId,
            @Parameter(description = "关键字") @RequestParam("keyword") String keyword
    ) {
        return AjaxResponseWrapper.data(this.dataflowService.getColumnNode(tableId, keyword));
    }




















}
