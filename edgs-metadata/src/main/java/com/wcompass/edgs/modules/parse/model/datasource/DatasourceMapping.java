package com.wcompass.edgs.modules.parse.model.datasource;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年11月11日10:25
 */

@Data
@Schema(description = "数据源映射对象")
public class DatasourceMapping {
    @Schema(description = "本地数据源映射id")
    private String datasourceId;
    @Schema(description = "本地数据源映射名称")
    private String datasourceName;
    @Schema(description = "本地数据源映射类型")
    private String datasourceMappingType;
    @Schema(description = "数据源类型")
    private String adapterName;
    @Schema(description = "外部数据源名称")
    private String externalDatasourceName;
    @Schema(description = "默认schema")
    private String defaultSchema;
}
