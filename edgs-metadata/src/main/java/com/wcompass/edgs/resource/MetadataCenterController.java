package com.wcompass.edgs.resource;

import com.wcompass.edgs.cloud.api.client.metadata.MetadataCenter;
import com.wcompass.edgs.cloud.api.client.metadata.model.*;
import com.wcompass.edgs.core.AjaxResponseWrapper;
import com.wcompass.edgs.core.security.GradingResultVO;
import com.wcompass.edgs.core.security.JudgeResultVO;
import com.wcompass.edgs.core.security.Task;
import com.wcompass.edgs.modules.md.service.MetadataAnalyseService;
import com.wcompass.edgs.modules.md.service.SecurityLevelService;
import com.wcompass.edgs.modules.security.service.TaskService;
import com.wcompass.edgs.resource.service.MetadataCenterService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/05/20
 */
@RestController
@RequestMapping
public class MetadataCenterController implements MetadataCenter {
    @Resource
    private MetadataCenterService metadataCenterService;
    @Resource
    private SecurityLevelService securityLevelService;

    @Resource
    private MetadataAnalyseService metadataAnalyseService;

    @Resource
    private TaskService taskService;

    @GetMapping(QUERY_TABLE_COLUMN)
    @Operation(summary = "查询表字段")
    @Override
    public AjaxResponseWrapper<List<CloudInstanceDTO>> queryTableColumn(@RequestParam Long instanceId) {
        return AjaxResponseWrapper.data(metadataCenterService.queryTableColumn(instanceId));
    }

    @PostMapping(value = PUSH_EXTRACT_METADATA_SOURCE, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Override
    public AjaxResponseWrapper<Void> pushExtractMetadataSource(@RequestPart("metadataFile") MultipartFile metadataFile) {
        metadataCenterService.pushExtractMetadataSource(metadataFile);
        return AjaxResponseWrapper.tip("推送成功");
    }

    @PostMapping(EXTRACT_LOG)
    @Override
    public AjaxResponseWrapper<Void> log(@RequestBody ExtractLogDTO extractLogDTO) {
        metadataCenterService.log(extractLogDTO);
        return AjaxResponseWrapper.tip("日志记录成功");
    }

    @Override
    @PostMapping(SAVE_EXTRACT_TASK_SUCCESS_STATUS)
    public AjaxResponseWrapper<Void> saveExtractTaskSchemaSuccessStatus(@RequestParam Integer extractTaskId,
                                                                        @RequestParam String datasourceId,
                                                                        @RequestParam String schema) {
        metadataCenterService.saveExtractTaskSuccessStatus(extractTaskId, datasourceId, schema);
        return AjaxResponseWrapper.tip("状态保存成功");
    }

    @Override
    @PostMapping(SAVE_EXTRACT_TASK_RUNNING_STATUS)
    public AjaxResponseWrapper<Void> saveExtractTaskSchemaRunningStatus(@RequestParam Integer extractTaskId,
                                                                        @RequestParam String datasourceId,
                                                                        @RequestParam String schema) {
        metadataCenterService.saveExtractTaskRunningStatus(extractTaskId, datasourceId, schema);
        return AjaxResponseWrapper.tip("状态保存成功");
    }

    @Override
    @PostMapping(SAVE_EXTRACT_TASK_FAILURE_STATUS)
    public AjaxResponseWrapper<Void> saveExtractTaskSchemaFailureStatus(@RequestParam Integer extractTaskId,
                                                                        @RequestParam String datasourceId,
                                                                        @RequestParam String schema) {
        metadataCenterService.saveExtractTaskFailureStatus(extractTaskId, datasourceId, schema);
        return AjaxResponseWrapper.tip("状态保存成功");
    }

    @Override
    @PostMapping(GET_SYSTEM_BY_INSTANCE_ID)
    public AjaxResponseWrapper<BaseInfoDTO> getSystemByInstanceId(@RequestParam String id) {
        return AjaxResponseWrapper.data(metadataCenterService.getSystemByInstanceId(id));
    }


    @Override
    @PostMapping(MAKEUP_SECURITYLEVEL_METADATA)
    public AjaxResponseWrapper<Void> editMakeUpSecurityLevelMetadata(@RequestParam Long instanceId, @RequestParam String securityLevel, @RequestParam String userId) {
        securityLevelService.makeUpSecurityLevelMetadata(instanceId, securityLevel, userId);
        return AjaxResponseWrapper.tip("元数据修改成功");
    }

    @Override
    @PostMapping(START_RUN_EXTRACT_TASK)
    public AjaxResponseWrapper<Void> saveExtractTaskRunningStatus(@RequestParam Integer taskId) {
        metadataCenterService.saveExtractTaskRunningStatus(taskId);
        return AjaxResponseWrapper.tip("状态保存成功");
    }

    @Override
    @PostMapping(SAVE_FAILURE_EXTRACT_TASK)
    public AjaxResponseWrapper<Void> saveExtractTaskFailureStatus(@RequestParam Integer taskId) {
        metadataCenterService.saveExtractTaskFailureStatus(taskId);
        return AjaxResponseWrapper.tip("状态保存成功");
    }

    @Override
    @PostMapping(SAVE_TASK_SCHEMA)
    public AjaxResponseWrapper<Void> saveTaskSchema(@RequestBody List<ExtractSchemaDTO> extractSchemaList) {
        metadataCenterService.saveTaskSchema(extractSchemaList);
        return AjaxResponseWrapper.tip("保存成功");
    }

    @Override
    @GetMapping(GET_EXTRACT_SCRIPT_PARAMS)
    public AjaxResponseWrapper<Map<String, String>> getExtractScriptParams() {
        return AjaxResponseWrapper.data(metadataCenterService.getExtractScriptParams());
    }

    @Override
    @PostMapping(SAVE_ANALYSE_RESULT)
    public AjaxResponseWrapper<Void> summaryAnalyseResult(@RequestBody MetadataAnalyseResultDTO metadataAnalyseDTO) {
        metadataAnalyseService.summaryAnalyseResult(metadataAnalyseDTO);
        return AjaxResponseWrapper.tip("保存剖析结果成功");
    }

    /**
     * @Description: 元数据模块校验分级分类信息
     * @Param: [taskId]
     * @Return: com.wcompass.edgs.core.AjaxResponseWrapper<java.util.Map>
     * <AUTHOR>
     * @Date: 2024/10/22 15:06
     */
    @Override
    @PostMapping(JUDGE_GET_SCAN_INSTANCE)
    public AjaxResponseWrapper<JudgeResultVO> judgeAnGetScanInstances() {
        return AjaxResponseWrapper.data(taskService.securityScan());
    }

    /**
     * @Description: 出现异常时，更新任务表的任务状态
     * @Param: [task]
     * @Return: com.wcompass.edgs.core.AjaxResponseWrapper<java.lang.Void>
     * <AUTHOR>
     * @Date: 2024/10/24 11:02
     */
    @Override
    @PostMapping(UPDATE_TASK)
    public AjaxResponseWrapper<Void> updateTask(@RequestBody Task task) {
        taskService.updateTask(task);
        return AjaxResponseWrapper.tip("任务状态更新成功");
    }

    /**
     * @Description: 保存抽取代理模块中的分类分级结果
     * @Param: [gradingResultVO]
     * @Return: com.wcompass.edgs.core.AjaxResponseWrapper<java.lang.Void>
     * <AUTHOR>
     * @Date: 2024/10/24 11:02
     */
    @Override
    @PostMapping(SAVE_GRADE_RESULT)
    public AjaxResponseWrapper<Void> saveGradingResultVO(@RequestBody GradingResultVO gradingResultVO) {
        taskService.saveGradeResult(gradingResultVO);
        return AjaxResponseWrapper.tip("分类分级结果保存成功");
    }


    @Override
    @PostMapping(SECURITY_JOB_LOG)
    public AjaxResponseWrapper<Void> securityJobLog(@RequestParam("taskId") int taskId,
                                                    @RequestParam("logTime") long logTime,
                                                    @RequestParam("logLevel") String logLevel,
                                                    @RequestParam("logDesc") String logDesc) {
        taskService.log(taskId, logTime, logLevel, logDesc);
        return AjaxResponseWrapper.tip("日志打印成功");
    }

    @Override
    @PostMapping(MAKEUP_INSTANCE_NAME)
    public AjaxResponseWrapper<Void> makeupInstanceName(@RequestParam("instanceId") Long instanceId,
                                                        @RequestParam("instanceName") String instanceName) {
        metadataCenterService.makeupInstanceName(instanceId,instanceName);
        return AjaxResponseWrapper.tip("补录成功");
    }
}
