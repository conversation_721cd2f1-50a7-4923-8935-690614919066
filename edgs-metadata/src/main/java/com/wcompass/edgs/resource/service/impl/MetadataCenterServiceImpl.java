package com.wcompass.edgs.resource.service.impl;

import com.wcompass.edgs.cloud.api.client.metadata.model.BaseInfoDTO;
import com.wcompass.edgs.cloud.api.client.metadata.model.CloudInstanceDTO;
import com.wcompass.edgs.cloud.api.client.metadata.model.ExtractLogDTO;
import com.wcompass.edgs.cloud.api.client.metadata.model.ExtractSchemaDTO;
import com.wcompass.edgs.constant.GlobalConstant;
import com.wcompass.edgs.core.extractor.ExtractRoot;
import com.wcompass.edgs.core.extractor.MetaInfo;
import com.wcompass.edgs.core.security.BaseInfo;
import com.wcompass.edgs.exception.SystemException;
import com.wcompass.edgs.log.LogHelper;
import com.wcompass.edgs.modules.D;
import com.wcompass.edgs.modules.md.dao.read.InstanceReadMapper;
import com.wcompass.edgs.modules.md.entity.ExtractLog;
import com.wcompass.edgs.modules.md.entity.ExtractTaskSchema;
import com.wcompass.edgs.modules.md.service.ExtractTaskService;
import com.wcompass.edgs.modules.md.service.MetadataService;
import com.wcompass.edgs.resource.service.MetadataCenterService;
import com.wcompass.edgs.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import jakarta.annotation.Resource;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MetadataCenterServiceImpl implements MetadataCenterService {

    @Resource
    private InstanceReadMapper instanceReadMapper;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private ExtractTaskService extractTaskService;

    @Resource
    private MetadataService metadataService;

    public static final String TAR_GZ_SUFFIX = ".tar.gz";

    /**
     * 查询表字段
     *
     * @param instanceId 元数据ID
     */
    @Override
    public List<CloudInstanceDTO> queryTableColumn(long instanceId) {
        return instanceReadMapper.queryTableColumn(instanceId);
    }

    @Override
    public void pushExtractMetadataSource(MultipartFile metadataFile) {
        String fileName = metadataFile.getOriginalFilename();
        try {
            Path dirPath = Paths.get(System.getProperty("user.home"), ".edgs/metadata");
            // 创建元数据源文件存放文件夹
            FileUtil.mkdir(dirPath.toFile());
            File tarFile = dirPath.resolve(fileName).toFile();
            FileUtil.writeFromStream(metadataFile.getInputStream(), tarFile, true);
            // 元数据文件绝对路径
            String tarFilePath = tarFile.getAbsolutePath();
            CompressUtil.tarGzipDecompress(tarFilePath);

            Path tarDir = Paths.get(StringUtil.removeEnd(tarFilePath, TAR_GZ_SUFFIX));

            Path info = tarDir.resolve(GlobalConstant.META_INFO);
            MetaInfo metaInfo = JsonUtil.getMapper().readValue(Files.newInputStream(info), MetaInfo.class);

            Integer extractTaskId = metaInfo.getExtractTaskId();
            String datasourceId = metaInfo.getDatasourceId();
            long batchNo = metaInfo.getBatchNo();
            ExtractRoot extractRoot = metaInfo.getCurrentExtractRoot();
            String extractRootName = extractRoot.getName();

            // 创建元数据文件存储目录
            File sourceDir = FileUtil.mkdir(dirPath.resolve(datasourceId)
                    .resolve(extractRootName)
                    .resolve(String.valueOf(batchNo))
                    .toFile());

            File[] sourceFiles = tarDir.toFile().listFiles();
            if (sourceFiles == null) {
                LogHelper.error(extractTaskId, datasourceId, extractRootName, "元数据文件为空");
                extractTaskService.saveFailureStatus(extractTaskId, datasourceId, extractRootName);
            } else {
                for (File sourceFile : sourceFiles) {
                    FileUtil.move(sourceFile, sourceDir, Boolean.TRUE);
                }
            }
            // 删除临时文件夹
            FileUtil.del(tarDir);
            // 归档目录
            FileUtil.del(tarFile);

            // 推送带采集队列中
            LOAD_TASK_PREPARE_QUEUE.add(metaInfo);
        } catch (Exception e) {
            log.error("", e);
            throw SystemException.wrap("推送文件失败，{}", ExceptionUtil.getCauseMessage(e));
        }
    }

    @Override
    public void log(ExtractLogDTO extractLogDTO) {
        ExtractLog extractLog = new ExtractLog();
        extractLog.setTaskId(extractLogDTO.getTaskId());
        extractLog.setDatasourceId(extractLogDTO.getDatasourceId());
        extractLog.setSchemaName(extractLogDTO.getSchema());
        extractLog.setLogTime(extractLogDTO.getLogTime());
        extractLog.setLogLevel(extractLogDTO.getLogLevel());
        extractLog.setLogDesc(extractLogDTO.getLogDesc());
        extractTaskService.log(extractLog);
    }

    @Override
    public void saveExtractTaskSuccessStatus(Integer extractTaskId, String datasourceId, String schema) {
        extractTaskService.saveSuccessStatus(extractTaskId, datasourceId, schema);
    }

    @Override
    public void saveExtractTaskRunningStatus(Integer extractTaskId, String datasourceId, String schema) {
        extractTaskService.saveRunningStatus(extractTaskId, datasourceId, schema);
    }

    @Override
    public void saveExtractTaskFailureStatus(Integer extractTaskId, String datasourceId, String schema) {
        extractTaskService.saveFailureStatus(extractTaskId, datasourceId, schema);
    }

    @Override
    public BaseInfoDTO getSystemByInstanceId(String id) {
        BaseInfo baseInfo = instanceReadMapper.getSystemByInstanceId(id);
        BaseInfoDTO baseInfoDTO = new BaseInfoDTO();
        BeanUtil.copyProperties(baseInfo, baseInfoDTO);
        return baseInfoDTO;
    }

    @Override
    public void saveExtractTaskRunningStatus(Integer taskId) {
         extractTaskService.updateExtractTaskStatus(taskId, D.TaskStatus.RUNNING);
    }

    @Override
    public void saveExtractTaskFailureStatus(Integer taskId) {
        extractTaskService.updateExtractTaskStatus(taskId, D.TaskStatus.FAILURE, DateUtil.now());
    }

    @Override
    public void saveTaskSchema(List<ExtractSchemaDTO> extractSchemaList) {
        List<ExtractTaskSchema> collect = extractSchemaList.stream()
                .map(dto -> {
                    ExtractTaskSchema extractTaskSchema = new ExtractTaskSchema();
                    extractTaskSchema.setTaskId(dto.getTaskId());
                    extractTaskSchema.setDatasourceId(dto.getDatasourceId());
                    extractTaskSchema.setSchemaName(dto.getSchemaName());
                    extractTaskSchema.setExtractStatus(dto.getExtractStatus());
                    extractTaskSchema.setBatchNo(dto.getBatchNo());
                    return extractTaskSchema;
                }).collect(Collectors.toList());
        extractTaskService.saveTaskSchema(collect);
    }

    @Override
    public Map<String, String> getExtractScriptParams() {
        return extractTaskService.getExtractScriptParams();
    }

    @Override
    public void makeupInstanceName(Long instanceId, String instanceName) {
        metadataService.makeupInstanceName(instanceId,instanceName);
    }
}
